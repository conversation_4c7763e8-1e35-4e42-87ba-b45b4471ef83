.class final Lcom/kwad/components/ct/detail/b/d$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$2;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$2;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->d(Lcom/kwad/components/ct/detail/b/d;Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method
