.class public final synthetic Ltop/cycdm/cycapp/ui/email/z;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/a;

    invoke-static {p1}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->a(Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;

    move-result-object p1

    return-object p1
.end method
