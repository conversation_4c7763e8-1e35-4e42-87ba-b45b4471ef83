.class final Lcom/kwad/components/ct/detail/ad/presenter/c$2;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

.field amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method private xK()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/c;->a(Lcom/kwad/components/ct/detail/ad/presenter/c;I)I

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->z(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    const/4 v1, 0x4

    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method private xL()V
    .locals 2

    .line 1
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/c$2$1;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$2$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c$2;)V

    .line 4
    .line 5
    .line 6
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 9
    .line 10
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->z(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 19
    .line 20
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 0

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pQ()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->xK()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->xL()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final pR()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pR()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->xK()V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->y(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 17
    .line 18
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->z(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 27
    .line 28
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 29
    .line 30
    .line 31
    :cond_0
    return-void
.end method
