.class final Ltop/cycdm/cycapp/ui/home/<USER>
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.home.HomeViewModel$initVersion$1"
    f = "HomeViewModel.kt"
    i = {
        0x0
    }
    l = {
        0x8a,
        0x8c
    }
    m = "invokeSuspend"
    n = {
        "$this$intent"
    }
    s = {
        "L$0"
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/home/<USER>


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltop/cycdm/cycapp/ui/home/<USER>",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(ZLtop/cycdm/model/z;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/z;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>

    move-result-object p0

    return-object p0
.end method

.method private static final invokeSuspend$lambda$0(ZLtop/cycdm/model/z;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 20

    .line 1
    invoke-virtual/range {p2 .. p2}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    move-object v1, v0

    .line 6
    check-cast v1, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 7
    .line 8
    const v18, 0xf3f7

    .line 9
    .line 10
    .line 11
    const/16 v19, 0x0

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    const/4 v3, 0x0

    .line 15
    const/4 v4, 0x0

    .line 16
    const/4 v6, 0x0

    .line 17
    const/4 v7, 0x0

    .line 18
    const/4 v8, 0x0

    .line 19
    const/4 v9, 0x0

    .line 20
    const/4 v10, 0x0

    .line 21
    const/4 v11, 0x0

    .line 22
    const-string v12, "4.2.8-ga5770ca"

    .line 23
    .line 24
    const/4 v14, 0x0

    .line 25
    const/4 v15, 0x0

    .line 26
    const/16 v16, 0x0

    .line 27
    .line 28
    const/16 v17, 0x0

    .line 29
    .line 30
    move/from16 v5, p0

    .line 31
    .line 32
    move-object/from16 v13, p1

    .line 33
    .line 34
    invoke-static/range {v1 .. v19}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/Map;Ljava/util/Map;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;Ltop/cycdm/model/z;Ltop/cycdm/model/s;Ltop/cycdm/model/p;Ltop/cycdm/cycapp/ui/weekly/DayOfWeek;Ljava/util/List;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    return-object v0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    invoke-direct {v0, v1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/home/<USER>

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_2

    .line 11
    .line 12
    if-eq v1, v4, :cond_1

    .line 13
    .line 14
    if-ne v1, v3, :cond_0

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_4

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 29
    .line 30
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 31
    .line 32
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_2
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 40
    .line 41
    move-object v1, p1

    .line 42
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 43
    .line 44
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 45
    .line 46
    invoke-static {p1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/b;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-interface {p1}, Lg8/b;->a()Lkotlinx/coroutines/flow/d;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    new-instance v5, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 55
    .line 56
    invoke-direct {v5, v2}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {p1, v5}, Lkotlinx/coroutines/flow/f;->i(Lkotlinx/coroutines/flow/d;Lkotlin/jvm/functions/Function3;)Lkotlinx/coroutines/flow/d;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 64
    .line 65
    iput v4, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 66
    .line 67
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/f;->D(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    if-ne p1, v0, :cond_3

    .line 72
    .line 73
    goto :goto_3

    .line 74
    :cond_3
    :goto_0
    check-cast p1, Ltop/cycdm/model/z;

    .line 75
    .line 76
    const/4 v5, 0x0

    .line 77
    if-eqz p1, :cond_4

    .line 78
    .line 79
    invoke-virtual {p1}, Ltop/cycdm/model/z;->a()I

    .line 80
    .line 81
    .line 82
    move-result v6

    .line 83
    goto :goto_1

    .line 84
    :cond_4
    move v6, v5

    .line 85
    :goto_1
    const v7, 0x6876938c

    .line 86
    .line 87
    .line 88
    if-le v6, v7, :cond_5

    .line 89
    .line 90
    goto :goto_2

    .line 91
    :cond_5
    move v4, v5

    .line 92
    :goto_2
    new-instance v5, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 93
    .line 94
    invoke-direct {v5, v4, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/z;)V

    .line 95
    .line 96
    .line 97
    iput-object v2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 98
    .line 99
    iput v3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 100
    .line 101
    invoke-static {v1, v5, p0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    if-ne p1, v0, :cond_6

    .line 106
    .line 107
    :goto_3
    return-object v0

    .line 108
    :cond_6
    :goto_4
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 109
    .line 110
    return-object p1
.end method
