.class final Lcom/kwad/components/ct/detail/ad/presenter/b$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amq:Lcom/kwad/components/ct/detail/ad/presenter/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$2;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final xG()V
    .locals 0

    return-void
.end method

.method public final xH()V
    .locals 3

    .line 1
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yo()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$2;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->c(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/a;->aJ(Lcom/kwad/sdk/core/response/model/AdInfo;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-nez v0, :cond_1

    .line 22
    .line 23
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$2;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 24
    .line 25
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->d(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/components/ct/detail/c;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 30
    .line 31
    instance-of v0, v0, Lcom/kwad/components/ct/detail/ad/a;

    .line 32
    .line 33
    if-eqz v0, :cond_0

    .line 34
    .line 35
    invoke-static {}, Lcom/kwad/components/core/t/d;->sM()Z

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    if-eqz v0, :cond_0

    .line 40
    .line 41
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$2;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 46
    .line 47
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/b;->e(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/components/ct/detail/c;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 52
    .line 53
    const/4 v2, 0x1

    .line 54
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/e/b;->b(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 55
    .line 56
    .line 57
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$2;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 58
    .line 59
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->c(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    const/16 v1, 0xc

    .line 64
    .line 65
    const/4 v2, 0x0

    .line 66
    invoke-static {v0, v1, v2}, Lcom/kwad/sdk/core/adlog/c;->a(Lcom/kwad/sdk/core/response/model/AdTemplate;ILcom/kwad/sdk/utils/ai$a;)V

    .line 67
    .line 68
    .line 69
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$2;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 70
    .line 71
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->f(Lcom/kwad/components/ct/detail/ad/presenter/b;)V

    .line 72
    .line 73
    .line 74
    :cond_1
    return-void
.end method
