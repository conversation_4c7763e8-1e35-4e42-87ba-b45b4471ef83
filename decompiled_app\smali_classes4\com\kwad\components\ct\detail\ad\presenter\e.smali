.class public final Lcom/kwad/components/ct/detail/ad/presenter/e;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

.field private amA:Landroid/view/View;

.field private amB:Z

.field private dm:Lcom/kwad/components/core/widget/ComplianceTextView;

.field private eQ:Lcom/kwad/sdk/core/i/c;

.field private jD:Landroid/widget/TextView;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amB:Z

    .line 6
    .line 7
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;

    .line 8
    .line 9
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/e$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/e;)V

    .line 10
    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 13
    .line 14
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/e$3;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/e;)V

    .line 17
    .line 18
    .line 19
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 20
    .line 21
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/e;JJJ)I
    .locals 0

    .line 1
    invoke-static/range {p1 .. p6}, Lcom/kwad/components/ct/detail/ad/presenter/e;->b(JJJ)I

    move-result p0

    return p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/e;)Landroid/content/Context;
    .locals 0

    .line 2
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/e;I)V
    .locals 0

    .line 3
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->bi(I)V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/e;Z)V
    .locals 0

    .line 4
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->bo(Z)V

    return-void
.end method

.method private static b(JJJ)I
    .locals 0

    .line 3
    invoke-static {p0, p1, p4, p5}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p0

    sub-long/2addr p0, p2

    long-to-float p0, p0

    const/high16 p1, 0x447a0000    # 1000.0f

    div-float/2addr p0, p1

    const/high16 p1, 0x3f000000    # 0.5f

    add-float/2addr p0, p1

    float-to-int p0, p0

    return p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/core/widget/ComplianceTextView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->dm:Lcom/kwad/components/core/widget/ComplianceTextView;

    return-object p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/e;I)V
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->p(I)V

    return-void
.end method

.method private bi(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 4
    .line 5
    iget v0, v0, Lcom/kwad/components/ct/home/<USER>

    .line 6
    .line 7
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getActivity()Landroid/app/Activity;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-static {v1}, Lcom/kwad/components/core/t/e;->e(Landroid/app/Activity;)Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-static {v1}, Lcom/kwad/sdk/c/a/a;->getStatusBarHeight(Landroid/content/Context;)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    add-int/2addr v0, v1

    .line 26
    :cond_0
    if-ltz v0, :cond_1

    .line 27
    .line 28
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    sget v2, Lcom/kwad/sdk/R$dimen;->ksad_content_detail_ad_margin_top:I

    .line 33
    .line 34
    invoke-static {v1, v2}, Lcom/kwad/sdk/c/a/a;->h(Landroid/content/Context;I)I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amA:Landroid/view/View;

    .line 39
    .line 40
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    instance-of v2, v2, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 45
    .line 46
    if-eqz v2, :cond_1

    .line 47
    .line 48
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amA:Landroid/view/View;

    .line 49
    .line 50
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    check-cast v2, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 55
    .line 56
    add-int/2addr v1, v0

    .line 57
    add-int/2addr v1, p1

    .line 58
    iput v1, v2, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 59
    .line 60
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amA:Landroid/view/View;

    .line 61
    .line 62
    invoke-virtual {p1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 63
    .line 64
    .line 65
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amA:Landroid/view/View;

    .line 66
    .line 67
    const/4 v0, 0x0

    .line 68
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 69
    .line 70
    .line 71
    :cond_1
    const/4 p1, 0x1

    .line 72
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amB:Z

    .line 73
    .line 74
    return-void
.end method

.method private bo(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amB:Z

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amA:Landroid/view/View;

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/16 v1, 0x8

    .line 12
    .line 13
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 17
    .line 18
    xor-int/lit8 p1, p1, 0x1

    .line 19
    .line 20
    const/16 v1, 0xa

    .line 21
    .line 22
    invoke-virtual {v0, p1, v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->h(ZI)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private p(I)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, Lcom/kwad/sdk/R$string;->ksad_draw_ad_force_look_count_down_format:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->jD:Landroid/widget/TextView;

    .line 12
    .line 13
    new-instance v2, Ljava/lang/StringBuilder;

    .line 14
    .line 15
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 19
    .line 20
    .line 21
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    filled-new-array {p1}, [Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-static {v0, p1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {v1, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 4

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/a;->aT(Lcom/kwad/sdk/core/response/model/AdInfo;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->dm:Lcom/kwad/components/core/widget/ComplianceTextView;

    .line 19
    .line 20
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/e$1;

    .line 21
    .line 22
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/e$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/e;)V

    .line 23
    .line 24
    .line 25
    const-wide/16 v2, 0x12c

    .line 26
    .line 27
    invoke-virtual {v0, v1, v2, v3}, Landroid/view/View;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_0
    const/4 v0, 0x0

    .line 32
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->bi(I)V

    .line 33
    .line 34
    .line 35
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 36
    .line 37
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 38
    .line 39
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 40
    .line 41
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 42
    .line 43
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 44
    .line 45
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 46
    .line 47
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 48
    .line 49
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 50
    .line 51
    .line 52
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 53
    .line 54
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 55
    .line 56
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 57
    .line 58
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/sdk/core/i/c;)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_content_draw_ad_forcelook_title_info:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->amA:Landroid/view/View;

    .line 11
    .line 12
    sget v0, Lcom/kwad/sdk/R$id;->ksad_content_draw_forcelook_count_down:I

    .line 13
    .line 14
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    check-cast v0, Landroid/widget/TextView;

    .line 19
    .line 20
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->jD:Landroid/widget/TextView;

    .line 21
    .line 22
    sget v0, Lcom/kwad/sdk/R$id;->ksad_compliance_view:I

    .line 23
    .line 24
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    check-cast v0, Lcom/kwad/components/core/widget/ComplianceTextView;

    .line 29
    .line 30
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->dm:Lcom/kwad/components/core/widget/ComplianceTextView;

    .line 31
    .line 32
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 11
    .line 12
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 16
    .line 17
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 18
    .line 19
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/sdk/core/i/c;)V

    .line 22
    .line 23
    .line 24
    :cond_0
    return-void
.end method
