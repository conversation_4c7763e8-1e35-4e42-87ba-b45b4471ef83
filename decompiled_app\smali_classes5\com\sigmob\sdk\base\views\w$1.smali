.class Lcom/sigmob/sdk/base/views/w$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/sigmob/sdk/base/views/w;->a(Landroid/content/Context;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/sigmob/sdk/base/views/w;


# direct methods
.method public constructor <init>(Lcom/sigmob/sdk/base/views/w;)V
    .locals 0

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/w$1;->a:Lcom/sigmob/sdk/base/views/w;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/w$1;->a:Lcom/sigmob/sdk/base/views/w;

    invoke-static {v0}, Lcom/sigmob/sdk/base/views/w;->a(Lcom/sigmob/sdk/base/views/w;)V

    return-void
.end method
