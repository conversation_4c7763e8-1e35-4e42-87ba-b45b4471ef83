.class public final Lcom/kwad/components/ct/detail/b/c/b;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# static fields
.field private static avg:Z = false


# instance fields
.field private Zh:I

.field private alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

.field private amo:Lcom/kwad/components/core/j/a;

.field private apZ:Lcom/kwad/components/core/widget/a/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private awF:J

.field private awG:Z

.field private awH:I

.field private awI:Z

.field private volatile awJ:J

.field private awK:Z

.field private eQ:Lcom/kwad/sdk/core/i/c;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mSceneImpl:Lcom/kwad/sdk/internal/api/SceneImpl;

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const-wide/16 v0, 0x0

    .line 5
    .line 6
    iput-wide v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awF:J

    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awG:Z

    .line 10
    .line 11
    iput v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awH:I

    .line 12
    .line 13
    const/4 v1, 0x1

    .line 14
    iput-boolean v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->awI:Z

    .line 15
    .line 16
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awK:Z

    .line 17
    .line 18
    new-instance v0, Lcom/kwad/components/ct/detail/b/c/b$1;

    .line 19
    .line 20
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/c/b$1;-><init>(Lcom/kwad/components/ct/detail/b/c/b;)V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 24
    .line 25
    new-instance v0, Lcom/kwad/components/ct/detail/b/c/b$2;

    .line 26
    .line 27
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/c/b$2;-><init>(Lcom/kwad/components/ct/detail/b/c/b;)V

    .line 28
    .line 29
    .line 30
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->amo:Lcom/kwad/components/core/j/a;

    .line 31
    .line 32
    new-instance v0, Lcom/kwad/components/ct/detail/b/c/b$3;

    .line 33
    .line 34
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/c/b$3;-><init>(Lcom/kwad/components/ct/detail/b/c/b;)V

    .line 35
    .line 36
    .line 37
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 38
    .line 39
    return-void
.end method

.method private BQ()V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awK:Z

    .line 3
    .line 4
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awG:Z

    .line 5
    .line 6
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awI:Z

    .line 7
    .line 8
    const-wide/16 v1, 0x0

    .line 9
    .line 10
    iput-wide v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->awJ:J

    .line 11
    .line 12
    iput v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awH:I

    .line 13
    .line 14
    return-void
.end method

.method private BV()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/e;->a(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/e/b;->q(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 17
    .line 18
    .line 19
    :cond_0
    return-void
.end method

.method private BW()V
    .locals 10

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awG:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awG:Z

    .line 8
    .line 9
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    .line 10
    .line 11
    .line 12
    move-result-wide v0

    .line 13
    iget-wide v2, p0, Lcom/kwad/components/ct/detail/b/c/b;->awF:J

    .line 14
    .line 15
    sub-long v6, v0, v2

    .line 16
    .line 17
    sget-boolean v0, Lcom/kwad/components/ct/detail/b/c/b;->avg:Z

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    new-instance v0, Ljava/lang/StringBuilder;

    .line 22
    .line 23
    const-string v1, "position: "

    .line 24
    .line 25
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->Zh:I

    .line 29
    .line 30
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    const-string v1, " startPlayDuration startPlayDuration: "

    .line 34
    .line 35
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 36
    .line 37
    .line 38
    invoke-virtual {v0, v6, v7}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 39
    .line 40
    .line 41
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    const-string v1, "DetailLogVideoPresenter"

    .line 46
    .line 47
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 51
    .line 52
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 53
    .line 54
    if-eqz v0, :cond_2

    .line 55
    .line 56
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/e/a;->getCurrentPlayingUrl()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    :goto_0
    move-object v8, v0

    .line 61
    goto :goto_1

    .line 62
    :cond_2
    const-string v0, ""

    .line 63
    .line 64
    goto :goto_0

    .line 65
    :goto_1
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 66
    .line 67
    .line 68
    move-result-object v4

    .line 69
    iget-object v5, p0, Lcom/kwad/components/ct/detail/b/c/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 70
    .line 71
    invoke-static {}, Lcom/kwad/components/core/video/c;->tt()Lcom/kwad/components/core/video/c;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    invoke-virtual {v0}, Lcom/kwad/components/core/video/c;->tw()I

    .line 76
    .line 77
    .line 78
    move-result v9

    .line 79
    invoke-virtual/range {v4 .. v9}, Lcom/kwad/components/ct/e/b;->a(Lcom/kwad/components/ct/response/model/CtAdTemplate;JLjava/lang/String;I)V

    .line 80
    .line 81
    .line 82
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/b;->BX()V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method private BX()V
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_1

    .line 5
    .line 6
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/e;->getPreItem()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/b;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 11
    .line 12
    invoke-virtual {v2}, Lcom/kwad/components/ct/detail/viewpager/e;->getCurrentItem()I

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    if-le v2, v0, :cond_0

    .line 17
    .line 18
    const/4 v1, 0x3

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    if-ge v2, v0, :cond_1

    .line 21
    .line 22
    const/4 v1, 0x2

    .line 23
    :cond_1
    :goto_0
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 28
    .line 29
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 30
    .line 31
    .line 32
    move-result-wide v3

    .line 33
    invoke-virtual {v0, v2, v3, v4, v1}, Lcom/kwad/components/core/p/a;->a(Lcom/kwad/sdk/core/response/model/AdTemplate;JI)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static synthetic BY()Z
    .locals 1

    .line 1
    sget-boolean v0, Lcom/kwad/components/ct/detail/b/c/b;->avg:Z

    .line 2
    .line 3
    return v0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c/b;J)J
    .locals 0

    .line 1
    iput-wide p1, p0, Lcom/kwad/components/ct/detail/b/c/b;->awF:J

    return-wide p1
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 2
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    return-object p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c/b;Z)Z
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/b/c/b;->awI:Z

    return p1
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/c/b;J)J
    .locals 0

    .line 1
    iput-wide p1, p0, Lcom/kwad/components/ct/detail/b/c/b;->awJ:J

    return-wide p1
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 2
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    return-object p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/c/b;Z)Z
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/b/c/b;->awK:Z

    return p1
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/c/b;)I
    .locals 2

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awH:I

    .line 2
    .line 3
    add-int/lit8 v1, v0, 0x1

    .line 4
    .line 5
    iput v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->awH:I

    .line 6
    .line 7
    return v0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/b/c/b;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awH:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/b/c/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/b;->BV()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/b/c/b;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awK:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic h(Lcom/kwad/components/ct/detail/b/c/b;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->Zh:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic i(Lcom/kwad/components/ct/detail/b/c/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/b;->BW()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic j(Lcom/kwad/components/ct/detail/b/c/b;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awG:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic k(Lcom/kwad/components/ct/detail/b/c/b;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awI:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic l(Lcom/kwad/components/ct/detail/b/c/b;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->awJ:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static synthetic m(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/sdk/internal/api/SceneImpl;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->mSceneImpl:Lcom/kwad/sdk/internal/api/SceneImpl;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic n(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/core/widget/a/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->apZ:Lcom/kwad/components/core/widget/a/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic o(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/sdk/core/i/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/b;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic p(Lcom/kwad/components/ct/detail/b/c/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/b;->BQ()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 7
    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    iget-object v2, v1, Lcom/kwad/components/ct/home/<USER>/kwad/components/core/widget/a/b;

    .line 11
    .line 12
    iput-object v2, p0, Lcom/kwad/components/ct/detail/b/c/b;->apZ:Lcom/kwad/components/core/widget/a/b;

    .line 13
    .line 14
    iget-object v1, v1, Lcom/kwad/components/ct/home/<USER>/kwad/sdk/internal/api/SceneImpl;

    .line 15
    .line 16
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->mSceneImpl:Lcom/kwad/sdk/internal/api/SceneImpl;

    .line 17
    .line 18
    :cond_0
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 19
    .line 20
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 21
    .line 22
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 23
    .line 24
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 25
    .line 26
    iget v0, v0, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 27
    .line 28
    iput v0, p0, Lcom/kwad/components/ct/detail/b/c/b;->Zh:I

    .line 29
    .line 30
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/b;->BQ()V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 34
    .line 35
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 36
    .line 37
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->amo:Lcom/kwad/components/core/j/a;

    .line 38
    .line 39
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 43
    .line 44
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 45
    .line 46
    if-eqz v0, :cond_1

    .line 47
    .line 48
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 49
    .line 50
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 51
    .line 52
    .line 53
    :cond_1
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 7
    .line 8
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->amo:Lcom/kwad/components/core/j/a;

    .line 9
    .line 10
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 14
    .line 15
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 22
    .line 23
    .line 24
    :cond_0
    return-void
.end method
