.class public interface abstract Lcom/beizi/fusion/RewardedVideoAdListener;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/beizi/fusion/a;


# virtual methods
.method public abstract onRewarded()V
.end method

.method public abstract onRewardedVideoAdClosed()V
.end method

.method public abstract onRewardedVideoAdFailedToLoad(I)V
.end method

.method public abstract onRewardedVideoAdLoaded()V
.end method

.method public abstract onRewardedVideoAdShown()V
.end method

.method public abstract onRewardedVideoClick()V
.end method

.method public abstract onRewardedVideoComplete()V
.end method
