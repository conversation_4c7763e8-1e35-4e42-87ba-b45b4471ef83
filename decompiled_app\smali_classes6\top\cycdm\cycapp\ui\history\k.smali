.class public final synthetic Ltop/cycdm/cycapp/ui/history/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Landroidx/compose/ui/Modifier;

.field public final synthetic c:Ljava/lang/String;

.field public final synthetic d:I

.field public final synthetic e:I


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/k;->a:Ljava/lang/String;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/k;->b:Landroidx/compose/ui/Modifier;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/history/k;->c:Ljava/lang/String;

    iput p4, p0, Ltop/cycdm/cycapp/ui/history/k;->d:I

    iput p5, p0, Ltop/cycdm/cycapp/ui/history/k;->e:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/k;->a:Ljava/lang/String;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/k;->b:Landroidx/compose/ui/Modifier;

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/history/k;->c:Ljava/lang/String;

    iget v3, p0, Ltop/cycdm/cycapp/ui/history/k;->d:I

    iget v4, p0, Ltop/cycdm/cycapp/ui/history/k;->e:I

    move-object v5, p1

    check-cast v5, Landroidx/compose/runtime/Composer;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v6

    invoke-static/range {v0 .. v6}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->p(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p1

    return-object p1
.end method
