.class final Lcom/kwad/components/ct/detail/ad/presenter/c$7;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/components/core/webview/jshandler/bn$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amv:Lcom/kwad/components/ct/detail/ad/presenter/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$7;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final uF()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$7;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->H(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/e/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$7;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->H(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/e/a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/e/a;->restart()V

    .line 16
    .line 17
    .line 18
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$7;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 19
    .line 20
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->I(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method
