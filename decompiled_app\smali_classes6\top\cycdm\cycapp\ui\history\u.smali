.class public final synthetic Ltop/cycdm/cycapp/ui/history/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

.field public final synthetic b:Ltop/cycdm/model/j;


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/u;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/u;->b:Ltop/cycdm/model/j;

    return-void
.end method


# virtual methods
.method public final invoke(<PERSON>ja<PERSON>/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/u;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/u;->b:Ltop/cycdm/model/j;

    check-cast p1, Landroidx/compose/material3/SwipeToDismissBoxValue;

    invoke-static {v0, v1, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->a(Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;Landroidx/compose/material3/SwipeToDismissBoxValue;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
