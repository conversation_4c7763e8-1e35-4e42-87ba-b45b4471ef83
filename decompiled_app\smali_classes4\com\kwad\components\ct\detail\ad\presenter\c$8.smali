.class final Lcom/kwad/components/ct/detail/ad/presenter/c$8;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/components/core/webview/jshandler/al$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amv:Lcom/kwad/components/ct/detail/ad/presenter/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$8;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/kwad/components/core/webview/jshandler/al$a;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$8;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/c;->I(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
