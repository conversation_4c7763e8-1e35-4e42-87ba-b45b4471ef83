.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->doTask()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic anl:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3$2;->anl:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3$2;->anl:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;

    .line 2
    .line 3
    iget-object p1, p1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 4
    .line 5
    const/16 v0, 0x8

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    invoke-static {p1, v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;II)V

    .line 9
    .line 10
    .line 11
    return-void
.end method
