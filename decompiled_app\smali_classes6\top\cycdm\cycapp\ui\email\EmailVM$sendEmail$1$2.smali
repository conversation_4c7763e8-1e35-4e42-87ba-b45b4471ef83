.class final Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function3;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function3<",
        "Lkotlinx/coroutines/flow/e;",
        "Ljava/lang/Throwable;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u0004*\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "Lkotlinx/coroutines/flow/e;",
        "",
        "",
        "it",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/e;Ljava/lang/Throwable;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.email.EmailVM$sendEmail$1$2"
    f = "EmailVM.kt"
    i = {
        0x0
    }
    l = {
        0x33,
        0x34
    }
    m = "invokeSuspend"
    n = {
        "it"
    }
    s = {
        "L$0"
    }
.end annotation


# instance fields
.field final synthetic $$this$intent:Lorg/orbitmvi/orbit/syntax/simple/b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/orbitmvi/orbit/syntax/simple/b;"
        }
    .end annotation
.end field

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltop/cycdm/cycapp/ui/email/EmailVM;",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->$$this$intent:Lorg/orbitmvi/orbit/syntax/simple/b;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Ljava/lang/Throwable;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->invokeSuspend$lambda$0(Ljava/lang/Throwable;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;

    move-result-object p0

    return-object p0
.end method

.method private static final invokeSuspend$lambda$0(Ljava/lang/Throwable;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 3

    .line 1
    invoke-virtual {p1}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ltop/cycdm/cycapp/ui/email/x;

    .line 6
    .line 7
    new-instance v0, Ltop/cycdm/cycapp/utils/h$a;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    const/4 v2, 0x2

    .line 11
    invoke-direct {v0, p0, v1, v2, v1}, Ltop/cycdm/cycapp/utils/h$a;-><init>(Ljava/lang/Throwable;Ljava/lang/Object;ILkotlin/jvm/internal/n;)V

    .line 12
    .line 13
    .line 14
    invoke-static {p1, v0, v1, v2, v1}, Ltop/cycdm/cycapp/ui/email/x;->b(Ltop/cycdm/cycapp/ui/email/x;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/email/x;

    .line 15
    .line 16
    .line 17
    move-result-object p0

    .line 18
    return-object p0
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/e;

    check-cast p2, Ljava/lang/Throwable;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->invoke(Lkotlinx/coroutines/flow/e;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/e;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e;",
            "Ljava/lang/Throwable;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance p1, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;

    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->$$this$intent:Lorg/orbitmvi/orbit/syntax/simple/b;

    invoke-direct {p1, v0, v1, p3}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;-><init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    iput-object p2, p1, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->L$0:Ljava/lang/Object;

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast v1, Ljava/lang/Throwable;

    .line 30
    .line 31
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    move-object v1, p1

    .line 41
    check-cast v1, Ljava/lang/Throwable;

    .line 42
    .line 43
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 44
    .line 45
    iget-object v4, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->$$this$intent:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 46
    .line 47
    new-instance v5, Ljava/lang/StringBuilder;

    .line 48
    .line 49
    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    .line 50
    .line 51
    .line 52
    const-string v6, "\u53d1\u9001\u9519\u8bef\uff1a"

    .line 53
    .line 54
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 55
    .line 56
    .line 57
    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 58
    .line 59
    .line 60
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v5

    .line 64
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->L$0:Ljava/lang/Object;

    .line 65
    .line 66
    iput v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->label:I

    .line 67
    .line 68
    invoke-static {p1, v4, v5, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    if-ne p1, v0, :cond_3

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :cond_3
    :goto_0
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->$$this$intent:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 76
    .line 77
    new-instance v3, Ltop/cycdm/cycapp/ui/email/a0;

    .line 78
    .line 79
    invoke-direct {v3, v1}, Ltop/cycdm/cycapp/ui/email/a0;-><init>(Ljava/lang/Throwable;)V

    .line 80
    .line 81
    .line 82
    const/4 v1, 0x0

    .line 83
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->L$0:Ljava/lang/Object;

    .line 84
    .line 85
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;->label:I

    .line 86
    .line 87
    invoke-static {p1, v3, p0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    if-ne p1, v0, :cond_4

    .line 92
    .line 93
    :goto_1
    return-object v0

    .line 94
    :cond_4
    :goto_2
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 95
    .line 96
    return-object p1
.end method
