.class public final enum Lcom/sigmob/sdk/downloader/core/cause/b;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/sigmob/sdk/downloader/core/cause/b;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum a:Lcom/sigmob/sdk/downloader/core/cause/b;

.field public static final enum b:Lcom/sigmob/sdk/downloader/core/cause/b;

.field public static final enum c:Lcom/sigmob/sdk/downloader/core/cause/b;

.field public static final enum d:Lcom/sigmob/sdk/downloader/core/cause/b;

.field public static final enum e:Lcom/sigmob/sdk/downloader/core/cause/b;

.field public static final enum f:Lcom/sigmob/sdk/downloader/core/cause/b;

.field public static final enum g:Lcom/sigmob/sdk/downloader/core/cause/b;

.field public static final enum h:Lcom/sigmob/sdk/downloader/core/cause/b;

.field private static final synthetic i:[Lcom/sigmob/sdk/downloader/core/cause/b;


# direct methods
.method static constructor <clinit>()V
    .locals 10

    new-instance v0, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v1, "INFO_DIRTY"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/sigmob/sdk/downloader/core/cause/b;->a:Lcom/sigmob/sdk/downloader/core/cause/b;

    new-instance v1, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v2, "FILE_NOT_EXIST"

    const/4 v3, 0x1

    invoke-direct {v1, v2, v3}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/sigmob/sdk/downloader/core/cause/b;->b:Lcom/sigmob/sdk/downloader/core/cause/b;

    new-instance v2, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v3, "OUTPUT_STREAM_NOT_SUPPORT"

    const/4 v4, 0x2

    invoke-direct {v2, v3, v4}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v2, Lcom/sigmob/sdk/downloader/core/cause/b;->c:Lcom/sigmob/sdk/downloader/core/cause/b;

    new-instance v3, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v4, "RESPONSE_ETAG_CHANGED"

    const/4 v5, 0x3

    invoke-direct {v3, v4, v5}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lcom/sigmob/sdk/downloader/core/cause/b;->d:Lcom/sigmob/sdk/downloader/core/cause/b;

    new-instance v4, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v5, "RESPONSE_PRECONDITION_FAILED"

    const/4 v6, 0x4

    invoke-direct {v4, v5, v6}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v4, Lcom/sigmob/sdk/downloader/core/cause/b;->e:Lcom/sigmob/sdk/downloader/core/cause/b;

    new-instance v5, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v6, "RESPONSE_CREATED_RANGE_NOT_FROM_0"

    const/4 v7, 0x5

    invoke-direct {v5, v6, v7}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lcom/sigmob/sdk/downloader/core/cause/b;->f:Lcom/sigmob/sdk/downloader/core/cause/b;

    new-instance v6, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v7, "RESPONSE_RESET_RANGE_NOT_FROM_0"

    const/4 v8, 0x6

    invoke-direct {v6, v7, v8}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v6, Lcom/sigmob/sdk/downloader/core/cause/b;->g:Lcom/sigmob/sdk/downloader/core/cause/b;

    new-instance v7, Lcom/sigmob/sdk/downloader/core/cause/b;

    const-string v8, "CONTENT_LENGTH_CHANGED"

    const/4 v9, 0x7

    invoke-direct {v7, v8, v9}, Lcom/sigmob/sdk/downloader/core/cause/b;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lcom/sigmob/sdk/downloader/core/cause/b;->h:Lcom/sigmob/sdk/downloader/core/cause/b;

    filled-new-array/range {v0 .. v7}, [Lcom/sigmob/sdk/downloader/core/cause/b;

    move-result-object v0

    sput-object v0, Lcom/sigmob/sdk/downloader/core/cause/b;->i:[Lcom/sigmob/sdk/downloader/core/cause/b;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/sigmob/sdk/downloader/core/cause/b;
    .locals 1

    const-class v0, Lcom/sigmob/sdk/downloader/core/cause/b;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/sigmob/sdk/downloader/core/cause/b;

    return-object p0
.end method

.method public static values()[Lcom/sigmob/sdk/downloader/core/cause/b;
    .locals 1

    sget-object v0, Lcom/sigmob/sdk/downloader/core/cause/b;->i:[Lcom/sigmob/sdk/downloader/core/cause/b;

    invoke-virtual {v0}, [Lcom/sigmob/sdk/downloader/core/cause/b;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/sigmob/sdk/downloader/core/cause/b;

    return-object v0
.end method
