.class public interface abstract Lcom/beizi/fusion/NativeUnifiedAdListener;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/beizi/fusion/a;


# virtual methods
.method public abstract onAdClick()V
.end method

.method public abstract onAdFailed(I)V
.end method

.method public abstract onAdLoaded(Lcom/beizi/fusion/NativeUnifiedAdResponse;)V
.end method

.method public abstract onAdShown()V
.end method
