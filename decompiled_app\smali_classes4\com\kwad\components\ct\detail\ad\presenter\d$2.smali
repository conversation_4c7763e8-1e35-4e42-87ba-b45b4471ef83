.class final Lcom/kwad/components/ct/detail/ad/presenter/d$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amz:Lcom/kwad/components/ct/detail/ad/presenter/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$2;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$2;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/d;->c(Lcom/kwad/components/ct/detail/ad/presenter/d;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$2;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/d;->d(Lcom/kwad/components/ct/detail/ad/presenter/d;)Lcom/kwad/components/ct/detail/e/a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/e/a;->restart()V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$2;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 20
    .line 21
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/d;->e(Lcom/kwad/components/ct/detail/ad/presenter/d;)Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$2;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 28
    .line 29
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/d;->b(Lcom/kwad/components/ct/detail/ad/presenter/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    const/4 v1, 0x1

    .line 34
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->bG(Z)V

    .line 35
    .line 36
    .line 37
    :cond_1
    return-void
.end method
