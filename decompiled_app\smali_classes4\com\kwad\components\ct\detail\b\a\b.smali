.class public final Lcom/kwad/components/ct/detail/b/a/b;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field private awf:Landroid/widget/TextView;

.field private cc:Lcom/kwad/components/core/widget/KsLogoView;

.field private fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

.field private fc:Ljava/lang/String;

.field private fl:Ljava/lang/Runnable;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/ct/detail/b/a/b$2;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/a/b$2;-><init>(Lcom/kwad/components/ct/detail/b/a/b;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->fl:Ljava/lang/Runnable;

    .line 10
    .line 11
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/a/b;)Ljava/lang/Runnable;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a/b;->fl:Ljava/lang/Runnable;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/a/b;)Landroid/widget/TextView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/a/b;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a/b;->fc:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/b/a/b;)Lcom/kwad/components/core/widget/KsLogoView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a/b;->cc:Lcom/kwad/components/core/widget/KsLogoView;

    .line 2
    .line 3
    return-object p0
.end method

.method private handleAdClick()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 4
    .line 5
    instance-of v0, v0, Lcom/kwad/components/ct/detail/ad/a;

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-static {}, Lcom/kwad/components/core/t/d;->sM()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 21
    .line 22
    iget-object v2, v2, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 23
    .line 24
    invoke-virtual {v0, v2, v1}, Lcom/kwad/components/ct/e/b;->b(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 25
    .line 26
    .line 27
    :cond_0
    new-instance v0, Lcom/kwad/components/core/e/d/a$a;

    .line 28
    .line 29
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-direct {v0, v2}, Lcom/kwad/components/core/e/d/a$a;-><init>(Landroid/content/Context;)V

    .line 34
    .line 35
    .line 36
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 37
    .line 38
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->aC(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/components/core/e/d/a$a;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/b;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 43
    .line 44
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->b(Lcom/kwad/components/core/e/d/c;)Lcom/kwad/components/core/e/d/a$a;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    const/4 v2, 0x2

    .line 49
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->ap(I)Lcom/kwad/components/core/e/d/a$a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    const/4 v2, 0x0

    .line 54
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->aq(Z)Lcom/kwad/components/core/e/d/a$a;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    const/16 v2, 0x19

    .line 59
    .line 60
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->ao(I)Lcom/kwad/components/core/e/d/a$a;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/b;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 65
    .line 66
    invoke-virtual {v2}, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;->getTouchCoords()Lcom/kwad/sdk/utils/ai$a;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->d(Lcom/kwad/sdk/utils/ai$a;)Lcom/kwad/components/core/e/d/a$a;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/a$a;->as(Z)Lcom/kwad/components/core/e/d/a$a;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-static {v0}, Lcom/kwad/components/core/e/d/a;->a(Lcom/kwad/components/core/e/d/a$a;)I

    .line 79
    .line 80
    .line 81
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 4

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 13
    .line 14
    invoke-static {v1}, Lcom/kwad/components/ct/response/a/a;->aN(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->fc:Ljava/lang/String;

    .line 19
    .line 20
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 21
    .line 22
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    const/4 v1, 0x0

    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    new-instance v0, Lcom/kwad/components/core/widget/KsLogoView;

    .line 30
    .line 31
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    const/4 v3, 0x1

    .line 36
    invoke-direct {v0, v2, v3}, Lcom/kwad/components/core/widget/KsLogoView;-><init>(Landroid/content/Context;Z)V

    .line 37
    .line 38
    .line 39
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->cc:Lcom/kwad/components/core/widget/KsLogoView;

    .line 40
    .line 41
    new-instance v2, Lcom/kwad/components/ct/detail/b/a/b$1;

    .line 42
    .line 43
    invoke-direct {v2, p0}, Lcom/kwad/components/ct/detail/b/a/b$1;-><init>(Lcom/kwad/components/ct/detail/b/a/b;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/widget/KsLogoView;->setLogoLoadFinishListener(Lcom/kwad/components/core/widget/KsLogoView$a;)V

    .line 47
    .line 48
    .line 49
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->cc:Lcom/kwad/components/core/widget/KsLogoView;

    .line 50
    .line 51
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 52
    .line 53
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/widget/KsLogoView;->aQ(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 54
    .line 55
    .line 56
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 57
    .line 58
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 59
    .line 60
    .line 61
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 62
    .line 63
    invoke-virtual {v0, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->fc:Ljava/lang/String;

    .line 68
    .line 69
    invoke-static {v0}, Lcom/kwad/sdk/utils/bq;->isNullString(Ljava/lang/String;)Z

    .line 70
    .line 71
    .line 72
    move-result v0

    .line 73
    if-nez v0, :cond_2

    .line 74
    .line 75
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 76
    .line 77
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/a;->aW(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Z

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    if-eqz v0, :cond_1

    .line 82
    .line 83
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 84
    .line 85
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 86
    .line 87
    iget-object v0, v0, Lcom/kwad/components/ct/home/<USER>/kwad/components/ct/api/tube/KSTubeParamInner;

    .line 88
    .line 89
    iget-boolean v0, v0, Lcom/kwad/components/ct/api/tube/KSTubeParamInner;->hideDetailBottomDesc:Z

    .line 90
    .line 91
    if-eqz v0, :cond_1

    .line 92
    .line 93
    goto :goto_0

    .line 94
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 95
    .line 96
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/b;->fc:Ljava/lang/String;

    .line 97
    .line 98
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 99
    .line 100
    .line 101
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 102
    .line 103
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 104
    .line 105
    invoke-static {v2}, Lcom/kwad/components/ct/response/a/a;->aO(Lcom/kwad/components/ct/response/model/CtAdTemplate;)I

    .line 106
    .line 107
    .line 108
    move-result v2

    .line 109
    int-to-float v2, v2

    .line 110
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setTextSize(F)V

    .line 111
    .line 112
    .line 113
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 114
    .line 115
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 116
    .line 117
    .line 118
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 119
    .line 120
    invoke-virtual {v0, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 121
    .line 122
    .line 123
    return-void

    .line 124
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 125
    .line 126
    const/16 v1, 0x8

    .line 127
    .line 128
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 129
    .line 130
    .line 131
    return-void
.end method

.method public final onClick(Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/a/b;->handleAdClick()V

    .line 10
    .line 11
    .line 12
    :cond_0
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_root_container:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 13
    .line 14
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_content_describe:I

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    check-cast v0, Landroid/widget/TextView;

    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 23
    .line 24
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/b;->awf:Landroid/widget/TextView;

    .line 5
    .line 6
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/b;->fl:Ljava/lang/Runnable;

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 9
    .line 10
    .line 11
    return-void
.end method
