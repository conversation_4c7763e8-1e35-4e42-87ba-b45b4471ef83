.class public final Lcom/beizi/ad/model/d$c$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/beizi/ad/model/d$c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field private a:Lcom/beizi/ad/model/e$d;

.field private b:Lcom/beizi/ad/model/e$c;

.field private c:Lcom/beizi/ad/model/d$b;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Lcom/beizi/ad/model/d$b;)Lcom/beizi/ad/model/d$c$a;
    .locals 0

    .line 3
    iput-object p1, p0, Lcom/beizi/ad/model/d$c$a;->c:Lcom/beizi/ad/model/d$b;

    return-object p0
.end method

.method public a(Lcom/beizi/ad/model/e$c;)Lcom/beizi/ad/model/d$c$a;
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/d$c$a;->b:Lcom/beizi/ad/model/e$c;

    return-object p0
.end method

.method public a(Lcom/beizi/ad/model/e$d;)Lcom/beizi/ad/model/d$c$a;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/model/d$c$a;->a:Lcom/beizi/ad/model/e$d;

    return-object p0
.end method

.method public a()Lcom/beizi/ad/model/d$c;
    .locals 2

    .line 4
    new-instance v0, Lcom/beizi/ad/model/d$c;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/beizi/ad/model/d$c;-><init>(Lcom/beizi/ad/model/d$1;)V

    .line 5
    iget-object v1, p0, Lcom/beizi/ad/model/d$c$a;->c:Lcom/beizi/ad/model/d$b;

    invoke-static {v0, v1}, Lcom/beizi/ad/model/d$c;->a(Lcom/beizi/ad/model/d$c;Lcom/beizi/ad/model/d$b;)Lcom/beizi/ad/model/d$b;

    .line 6
    iget-object v1, p0, Lcom/beizi/ad/model/d$c$a;->a:Lcom/beizi/ad/model/e$d;

    invoke-static {v0, v1}, Lcom/beizi/ad/model/d$c;->a(Lcom/beizi/ad/model/d$c;Lcom/beizi/ad/model/e$d;)Lcom/beizi/ad/model/e$d;

    .line 7
    iget-object v1, p0, Lcom/beizi/ad/model/d$c$a;->b:Lcom/beizi/ad/model/e$c;

    invoke-static {v0, v1}, Lcom/beizi/ad/model/d$c;->a(Lcom/beizi/ad/model/d$c;Lcom/beizi/ad/model/e$c;)Lcom/beizi/ad/model/e$c;

    return-object v0
.end method
