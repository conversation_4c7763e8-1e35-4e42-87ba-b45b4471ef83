.class public final synthetic Ltop/cycdm/cycapp/ui/history/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ltop/cycdm/model/j;

.field public final synthetic b:Lkotlin/jvm/functions/Function0;

.field public final synthetic c:I

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/j;->a:Ltop/cycdm/model/j;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/j;->b:Lkotlin/jvm/functions/Function0;

    iput p3, p0, Ltop/cycdm/cycapp/ui/history/j;->c:I

    iput p4, p0, Ltop/cycdm/cycapp/ui/history/j;->d:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/j;->a:Ltop/cycdm/model/j;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/j;->b:Lkotlin/jvm/functions/Function0;

    iget v2, p0, Ltop/cycdm/cycapp/ui/history/j;->c:I

    iget v3, p0, Ltop/cycdm/cycapp/ui/history/j;->d:I

    move-object v4, p1

    check-cast v4, Landroidx/compose/runtime/Composer;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    invoke-static/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->f(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p1

    return-object p1
.end method
