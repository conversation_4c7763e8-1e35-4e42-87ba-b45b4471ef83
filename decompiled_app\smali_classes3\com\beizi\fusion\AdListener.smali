.class public interface abstract Lcom/beizi/fusion/AdListener;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/beizi/fusion/a;


# virtual methods
.method public abstract onAdClicked()V
.end method

.method public abstract onAdClosed()V
.end method

.method public abstract onAdFailedToLoad(I)V
.end method

.method public abstract onAdLoaded()V
.end method

.method public abstract onAdShown()V
.end method

.method public abstract onAdTick(J)V
.end method
