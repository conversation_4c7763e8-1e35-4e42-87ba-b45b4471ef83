.class public Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;
.super Lcom/kwad/sdk/core/view/AdBaseLinearLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$a;,
        Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;
    }
.end annotation


# instance fields
.field private Cx:Landroid/widget/TextView;

.field private anb:Landroid/widget/TextView;

.field private anh:Lcom/kwad/components/ct/detail/photo/comment/h;

.field private ann:Landroid/widget/TextView;

.field private ano:Landroid/view/View;

.field private anp:Landroid/view/View;

.field private anq:Landroid/view/View;

.field private anr:Landroid/animation/AnimatorSet;

.field private ans:Z

.field private ant:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;",
            ">;"
        }
    .end annotation
.end field

.field private anu:Landroid/view/View$OnClickListener;

.field private anv:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$a;

.field private anw:Landroid/view/View;

.field private anx:Landroid/widget/ImageView;

.field private dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

.field private mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/kwad/sdk/core/view/AdBaseLinearLayout;-><init>(Landroid/content/Context;)V

    .line 2
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ant:Ljava/util/List;

    .line 3
    new-instance p1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;

    invoke-direct {p1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anu:Landroid/view/View$OnClickListener;

    .line 4
    new-instance p1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;

    invoke-direct {p1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    .line 5
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->initView()V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p2    # Landroid/util/AttributeSet;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .line 6
    invoke-direct {p0, p1, p2}, Lcom/kwad/sdk/core/view/AdBaseLinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 7
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ant:Ljava/util/List;

    .line 8
    new-instance p1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;

    invoke-direct {p1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anu:Landroid/view/View$OnClickListener;

    .line 9
    new-instance p1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;

    invoke-direct {p1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    .line 10
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->initView()V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p2    # Landroid/util/AttributeSet;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .line 11
    invoke-direct {p0, p1, p2, p3}, Lcom/kwad/sdk/core/view/AdBaseLinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 12
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ant:Ljava/util/List;

    .line 13
    new-instance p1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;

    invoke-direct {p1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anu:Landroid/view/View$OnClickListener;

    .line 14
    new-instance p1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;

    invoke-direct {p1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    .line 15
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->initView()V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->el()V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;IIZ)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2, p3}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->b(IIZ)V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;ZZ)V
    .locals 0

    .line 3
    invoke-direct {p0, p1, p2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->j(ZZ)V

    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    return-object p0
.end method

.method private b(IIZ)V
    .locals 2

    .line 2
    new-instance v0, Lcom/kwad/components/core/e/d/a$a;

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/kwad/components/core/e/d/a$a;-><init>(Landroid/content/Context;)V

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 3
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/a$a;->aC(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/components/core/e/d/a$a;

    move-result-object v0

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 4
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/a$a;->b(Lcom/kwad/components/core/e/d/c;)Lcom/kwad/components/core/e/d/a$a;

    move-result-object v0

    .line 5
    invoke-virtual {v0, p2}, Lcom/kwad/components/core/e/d/a$a;->ap(I)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p2

    .line 6
    invoke-virtual {p2, p3}, Lcom/kwad/components/core/e/d/a$a;->aq(Z)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p2

    .line 7
    invoke-virtual {p2, p1}, Lcom/kwad/components/core/e/d/a$a;->ao(I)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p1

    .line 8
    invoke-virtual {p0}, Lcom/kwad/sdk/core/view/AdBaseLinearLayout;->getTouchCoords()Lcom/kwad/sdk/utils/ai$a;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/kwad/components/core/e/d/a$a;->d(Lcom/kwad/sdk/utils/ai$a;)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p1

    const/4 p2, 0x1

    .line 9
    invoke-virtual {p1, p2}, Lcom/kwad/components/core/e/d/a$a;->as(Z)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p1

    .line 10
    invoke-static {p1}, Lcom/kwad/components/core/e/d/a;->a(Lcom/kwad/components/core/e/d/a$a;)I

    .line 11
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anv:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$a;

    if-eqz p1, :cond_0

    .line 12
    invoke-interface {p1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$a;->onClick()V

    :cond_0
    return-void
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/widget/TextView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Lcom/kwad/sdk/core/response/model/AdInfo;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/view/View$OnClickListener;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anu:Landroid/view/View$OnClickListener;

    .line 2
    .line 3
    return-object p0
.end method

.method private el()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ant:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;

    .line 18
    .line 19
    invoke-interface {v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;->onClose()V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method private initView()V
    .locals 8

    .line 1
    const-string v0, "commentBottomAdPanel"

    .line 2
    .line 3
    const-string v1, "initView"

    .line 4
    .line 5
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-static {}, Lcom/kwad/components/ct/f/d;->Jt()Lcom/kwad/components/ct/f/d;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    const-class v1, Lcom/kwad/components/ct/detail/photo/comment/i;

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/f/d;->a(Ljava/lang/Class;)Lcom/kwad/components/ct/f/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    check-cast v0, Lcom/kwad/components/ct/detail/photo/comment/i;

    .line 19
    .line 20
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/photo/comment/i;->zn()Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 25
    .line 26
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yi()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    const/4 v1, 0x1

    .line 31
    if-ne v0, v1, :cond_0

    .line 32
    .line 33
    move v0, v1

    .line 34
    goto :goto_0

    .line 35
    :cond_0
    const/4 v0, 0x0

    .line 36
    :goto_0
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ans:Z

    .line 37
    .line 38
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iget-boolean v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ans:Z

    .line 47
    .line 48
    if-eqz v2, :cond_1

    .line 49
    .line 50
    sget v2, Lcom/kwad/sdk/R$layout;->ksad_comment_bottom_ad_panel_layout:I

    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_1
    sget v2, Lcom/kwad/sdk/R$layout;->ksad_comment_bottom_ad_panel_layout_2:I

    .line 54
    .line 55
    :goto_1
    invoke-virtual {v0, v2, p0, v1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    .line 56
    .line 57
    .line 58
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ans:Z

    .line 59
    .line 60
    if-nez v0, :cond_2

    .line 61
    .line 62
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_divider:I

    .line 63
    .line 64
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anw:Landroid/view/View;

    .line 69
    .line 70
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 71
    .line 72
    iget-object v1, v1, Lcom/kwad/components/ct/detail/photo/comment/h;->apT:Ljava/lang/String;

    .line 73
    .line 74
    invoke-static {v0, v1}, Lcom/kwad/components/ct/f/g;->c(Landroid/view/View;Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    :cond_2
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_author_name:I

    .line 78
    .line 79
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    check-cast v0, Landroid/widget/TextView;

    .line 84
    .line 85
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anb:Landroid/widget/TextView;

    .line 86
    .line 87
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 88
    .line 89
    iget-object v1, v1, Lcom/kwad/components/ct/detail/photo/comment/h;->apM:Ljava/lang/String;

    .line 90
    .line 91
    invoke-static {v0, v1}, Lcom/kwad/components/ct/f/g;->a(Landroid/widget/TextView;Ljava/lang/String;)V

    .line 92
    .line 93
    .line 94
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_content:I

    .line 95
    .line 96
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 97
    .line 98
    .line 99
    move-result-object v0

    .line 100
    check-cast v0, Landroid/widget/TextView;

    .line 101
    .line 102
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->Cx:Landroid/widget/TextView;

    .line 103
    .line 104
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 105
    .line 106
    iget-object v1, v1, Lcom/kwad/components/ct/detail/photo/comment/h;->apN:Ljava/lang/String;

    .line 107
    .line 108
    invoke-static {v0, v1}, Lcom/kwad/components/ct/f/g;->a(Landroid/widget/TextView;Ljava/lang/String;)V

    .line 109
    .line 110
    .line 111
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_link_btn:I

    .line 112
    .line 113
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    check-cast v0, Landroid/widget/TextView;

    .line 118
    .line 119
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    .line 120
    .line 121
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_link_btn_layout:I

    .line 122
    .line 123
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 124
    .line 125
    .line 126
    move-result-object v0

    .line 127
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anq:Landroid/view/View;

    .line 128
    .line 129
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_close_btn:I

    .line 130
    .line 131
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ano:Landroid/view/View;

    .line 136
    .line 137
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_close_btn_icon:I

    .line 138
    .line 139
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    check-cast v0, Landroid/widget/ImageView;

    .line 144
    .line 145
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anx:Landroid/widget/ImageView;

    .line 146
    .line 147
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 148
    .line 149
    iget v1, v1, Lcom/kwad/components/ct/detail/photo/comment/h;->apU:I

    .line 150
    .line 151
    invoke-static {v0, v1}, Lcom/kwad/components/ct/f/g;->a(Landroid/widget/ImageView;I)V

    .line 152
    .line 153
    .line 154
    sget v0, Lcom/kwad/sdk/R$id;->kasd_comment_bottom_ad_layout:I

    .line 155
    .line 156
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anp:Landroid/view/View;

    .line 161
    .line 162
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$3;

    .line 163
    .line 164
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$3;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    .line 165
    .line 166
    .line 167
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 168
    .line 169
    .line 170
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anq:Landroid/view/View;

    .line 171
    .line 172
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$4;

    .line 173
    .line 174
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$4;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    .line 175
    .line 176
    .line 177
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 178
    .line 179
    .line 180
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anb:Landroid/widget/TextView;

    .line 181
    .line 182
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$5;

    .line 183
    .line 184
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$5;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    .line 185
    .line 186
    .line 187
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 188
    .line 189
    .line 190
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->Cx:Landroid/widget/TextView;

    .line 191
    .line 192
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$6;

    .line 193
    .line 194
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$6;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    .line 195
    .line 196
    .line 197
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 198
    .line 199
    .line 200
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ano:Landroid/view/View;

    .line 201
    .line 202
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$7;

    .line 203
    .line 204
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$7;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    .line 205
    .line 206
    .line 207
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 208
    .line 209
    .line 210
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ans:Z

    .line 211
    .line 212
    if-eqz v0, :cond_3

    .line 213
    .line 214
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 215
    .line 216
    iget-object v0, v0, Lcom/kwad/components/ct/detail/photo/comment/h;->apP:Ljava/lang/String;

    .line 217
    .line 218
    invoke-static {v0}, Lcom/kwad/components/ct/f/g;->parseColor(Ljava/lang/String;)I

    .line 219
    .line 220
    .line 221
    move-result v2

    .line 222
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 223
    .line 224
    .line 225
    move-result-object v0

    .line 226
    const/high16 v1, 0x40c00000    # 6.0f

    .line 227
    .line 228
    invoke-static {v0, v1}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 229
    .line 230
    .line 231
    move-result v3

    .line 232
    const-string v0, "#14000000"

    .line 233
    .line 234
    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    .line 235
    .line 236
    .line 237
    move-result v4

    .line 238
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 239
    .line 240
    .line 241
    move-result-object v0

    .line 242
    const/high16 v1, 0x40800000    # 4.0f

    .line 243
    .line 244
    invoke-static {v0, v1}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 245
    .line 246
    .line 247
    move-result v5

    .line 248
    const/4 v6, 0x0

    .line 249
    const/4 v7, 0x1

    .line 250
    move-object v1, p0

    .line 251
    invoke-static/range {v1 .. v7}, Lcom/kwad/sdk/core/view/d;->a(Landroid/view/View;IIIIII)V

    .line 252
    .line 253
    .line 254
    goto :goto_2

    .line 255
    :cond_3
    move-object v1, p0

    .line 256
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 257
    .line 258
    .line 259
    move-result-object v0

    .line 260
    iget-object v2, v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 261
    .line 262
    iget v2, v2, Lcom/kwad/components/ct/detail/photo/comment/h;->apO:I

    .line 263
    .line 264
    invoke-static {v0, v2}, Lcom/kwad/components/ct/f/g;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 265
    .line 266
    .line 267
    move-result-object v0

    .line 268
    invoke-static {p0, v0}, Lcom/kwad/sdk/core/view/d;->a(Landroid/view/View;Landroid/graphics/drawable/Drawable;)V

    .line 269
    .line 270
    .line 271
    :goto_2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 272
    .line 273
    .line 274
    move-result-object v0

    .line 275
    const/high16 v2, 0x42a00000    # 80.0f

    .line 276
    .line 277
    invoke-static {v0, v2}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 278
    .line 279
    .line 280
    move-result v0

    .line 281
    int-to-float v0, v0

    .line 282
    invoke-virtual {p0, v0}, Landroid/view/View;->setTranslationY(F)V

    .line 283
    .line 284
    .line 285
    return-void
.end method

.method private j(ZZ)V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    if-nez v0, :cond_1

    .line 11
    .line 12
    :goto_0
    return-void

    .line 13
    :cond_1
    const/4 v1, -0x2

    .line 14
    if-eqz p1, :cond_3

    .line 15
    .line 16
    iget-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ans:Z

    .line 17
    .line 18
    if-eqz p1, :cond_2

    .line 19
    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    const/high16 v2, 0x42d00000    # 104.0f

    .line 25
    .line 26
    invoke-static {p1, v2}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 27
    .line 28
    .line 29
    move-result p1

    .line 30
    goto :goto_1

    .line 31
    :cond_2
    move p1, v1

    .line 32
    :goto_1
    iput p1, v0, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 33
    .line 34
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 35
    .line 36
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    .line 37
    .line 38
    const/16 v1, 0xa

    .line 39
    .line 40
    invoke-virtual {p1, v1}, Landroid/widget/TextView;->setMaxEms(I)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_3
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 45
    .line 46
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 47
    .line 48
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    .line 49
    .line 50
    const/4 v1, 0x6

    .line 51
    invoke-virtual {p1, v1}, Landroid/widget/TextView;->setMaxEms(I)V

    .line 52
    .line 53
    .line 54
    :goto_2
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    .line 55
    .line 56
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setSelected(Z)V

    .line 57
    .line 58
    .line 59
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    .line 60
    .line 61
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 62
    .line 63
    .line 64
    return-void
.end method


# virtual methods
.method public final a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;)V
    .locals 1
    .param p1    # Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 23
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ant:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public final a(Lcom/kwad/components/ct/response/model/CtAdTemplate;Lcom/kwad/components/core/e/d/c;)V
    .locals 1
    .param p2    # Lcom/kwad/components/core/e/d/c;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p1, :cond_0

    return-void

    .line 4
    :cond_0
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 5
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    move-result-object v0

    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 6
    iput-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 7
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->Cx:Landroid/widget/TextView;

    invoke-static {p1}, Lcom/kwad/components/ct/response/a/a;->aQ(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 8
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anb:Landroid/widget/TextView;

    invoke-static {p1}, Lcom/kwad/components/ct/response/a/a;->aL(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    const/4 p2, 0x0

    .line 9
    invoke-direct {p0, p2, p2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->j(ZZ)V

    .line 10
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ann:Landroid/widget/TextView;

    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    move-result-object p1

    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/a;->aI(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 11
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    if-eqz p1, :cond_1

    .line 12
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    invoke-virtual {p1, p2}, Lcom/kwad/components/core/e/d/c;->b(Lcom/kwad/sdk/api/KsAppDownloadListener;)V

    .line 13
    :cond_1
    iget-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ans:Z

    if-eqz p1, :cond_2

    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p1

    check-cast p1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p2

    const/high16 v0, 0x41000000    # 8.0f

    invoke-static {p2, v0}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    move-result p2

    .line 16
    invoke-virtual {p1, p2, p2, p2, p2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 17
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    goto :goto_0

    .line 18
    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p1

    check-cast p1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 19
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p2

    const/4 v0, 0x0

    invoke-static {p2, v0}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    move-result p2

    .line 20
    invoke-virtual {p1, p2, p2, p2, p2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 21
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 22
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    return-void
.end method

.method public final b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;)V
    .locals 1
    .param p1    # Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$b;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ant:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public final k(ZZ)V
    .locals 4

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    const/16 p2, 0x8

    .line 6
    .line 7
    invoke-virtual {p0, p2}, Landroid/view/View;->setVisibility(I)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 11
    .line 12
    if-eqz p2, :cond_1

    .line 13
    .line 14
    invoke-virtual {p2}, Landroid/animation/AnimatorSet;->isRunning()Z

    .line 15
    .line 16
    .line 17
    move-result p2

    .line 18
    if-eqz p2, :cond_1

    .line 19
    .line 20
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 21
    .line 22
    invoke-virtual {p2}, Landroid/animation/Animator;->removeAllListeners()V

    .line 23
    .line 24
    .line 25
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 26
    .line 27
    invoke-virtual {p2}, Landroid/animation/AnimatorSet;->cancel()V

    .line 28
    .line 29
    .line 30
    :cond_1
    new-instance p2, Landroid/animation/AnimatorSet;

    .line 31
    .line 32
    invoke-direct {p2}, Landroid/animation/AnimatorSet;-><init>()V

    .line 33
    .line 34
    .line 35
    iput-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 36
    .line 37
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 38
    .line 39
    .line 40
    move-result-object p2

    .line 41
    const/high16 v2, 0x42a00000    # 80.0f

    .line 42
    .line 43
    invoke-static {p2, v2}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 44
    .line 45
    .line 46
    move-result p2

    .line 47
    new-instance v2, Ljava/lang/StringBuilder;

    .line 48
    .line 49
    const-string v3, "bottomViewAnimate + isShow : "

    .line 50
    .line 51
    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 55
    .line 56
    .line 57
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v2

    .line 61
    const-string v3, "commentBottomAdPanel"

    .line 62
    .line 63
    invoke-static {v3, v2}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 64
    .line 65
    .line 66
    sget-object v2, Landroid/view/View;->TRANSLATION_Y:Landroid/util/Property;

    .line 67
    .line 68
    invoke-virtual {v2}, Landroid/util/Property;->getName()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v2

    .line 72
    if-eqz p1, :cond_2

    .line 73
    .line 74
    const/4 p2, 0x0

    .line 75
    goto :goto_0

    .line 76
    :cond_2
    int-to-float p2, p2

    .line 77
    :goto_0
    new-array v3, v1, [F

    .line 78
    .line 79
    aput p2, v3, v0

    .line 80
    .line 81
    invoke-static {p0, v2, v3}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Ljava/lang/String;[F)Landroid/animation/ObjectAnimator;

    .line 82
    .line 83
    .line 84
    move-result-object p2

    .line 85
    if-eqz p1, :cond_3

    .line 86
    .line 87
    const-wide/16 v2, 0x15e

    .line 88
    .line 89
    goto :goto_1

    .line 90
    :cond_3
    const-wide/16 v2, 0x12c

    .line 91
    .line 92
    :goto_1
    invoke-virtual {p2, v2, v3}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 93
    .line 94
    .line 95
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 96
    .line 97
    new-array v1, v1, [Landroid/animation/Animator;

    .line 98
    .line 99
    aput-object p2, v1, v0

    .line 100
    .line 101
    invoke-virtual {v2, v1}, Landroid/animation/AnimatorSet;->playTogether([Landroid/animation/Animator;)V

    .line 102
    .line 103
    .line 104
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 105
    .line 106
    invoke-virtual {p2}, Landroid/animation/Animator;->removeAllListeners()V

    .line 107
    .line 108
    .line 109
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 110
    .line 111
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;

    .line 112
    .line 113
    invoke-direct {v0, p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;Z)V

    .line 114
    .line 115
    .line 116
    invoke-virtual {p2, v0}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 117
    .line 118
    .line 119
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anr:Landroid/animation/AnimatorSet;

    .line 120
    .line 121
    invoke-virtual {p1}, Landroid/animation/AnimatorSet;->start()V

    .line 122
    .line 123
    .line 124
    return-void
.end method

.method public setAdClickListener(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anv:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$a;

    .line 2
    .line 3
    return-void
.end method

.method public setName(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->anb:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final yf()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/c;->c(Lcom/kwad/sdk/api/KsAppDownloadListener;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->ant:Ljava/util/List;

    .line 11
    .line 12
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 13
    .line 14
    .line 15
    return-void
.end method
