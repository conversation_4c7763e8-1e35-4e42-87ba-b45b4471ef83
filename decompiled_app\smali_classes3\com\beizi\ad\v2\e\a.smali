.class public Lcom/beizi/ad/v2/e/a;
.super Lcom/beizi/ad/v2/a/a;
.source "SourceFile"


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/a/a;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/beizi/ad/v2/e/b;

    .line 5
    .line 6
    invoke-direct {v0, p1}, Lcom/beizi/ad/v2/e/b;-><init>(Landroid/content/Context;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public a(Landroid/content/Context;)V
    .locals 2

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 7
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/e/b;

    if-nez v1, :cond_1

    :goto_0
    return-void

    .line 8
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v0, p1}, Lcom/beizi/ad/v2/e/b;->a(Landroid/content/Context;)V

    return-void
.end method

.method public a(Lcom/beizi/ad/b;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez p1, :cond_0

    return-void

    .line 2
    :cond_0
    invoke-virtual {p1}, Lcom/beizi/ad/v2/a/b;->b()V

    return-void
.end method

.method public a(Lcom/beizi/ad/j;)V
    .locals 2

    .line 3
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 4
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/e/b;

    if-nez v1, :cond_1

    :goto_0
    return-void

    .line 5
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v0, p1}, Lcom/beizi/ad/v2/e/b;->a(Lcom/beizi/ad/j;)V

    return-void
.end method
