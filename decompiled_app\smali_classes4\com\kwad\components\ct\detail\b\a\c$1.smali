.class final Lcom/kwad/components/ct/detail/b/a/c$1;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/a/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awj:Lcom/kwad/components/ct/detail/b/a/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/a/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/a/c$1;->awj:Lcom/kwad/components/ct/detail/b/a/c;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pR()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c$1;->awj:Lcom/kwad/components/ct/detail/b/a/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a/c;->a(Lcom/kwad/components/ct/detail/b/a/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
