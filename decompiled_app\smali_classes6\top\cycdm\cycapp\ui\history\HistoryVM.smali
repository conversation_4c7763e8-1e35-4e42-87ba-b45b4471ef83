.class public final Ltop/cycdm/cycapp/ui/history/HistoryVM;
.super Ltop/cycdm/cycapp/BaseVM;
.source "SourceFile"


# annotations
.annotation build Landroidx/compose/runtime/internal/StabilityInferred;
    parameters = 0x0
.end annotation

.annotation build Ldagger/hilt/android/lifecycle/HiltViewModel;
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ltop/cycdm/cycapp/BaseVM<",
        "Ltop/cycdm/cycapp/ui/history/w;",
        "Ltop/cycdm/cycapp/ui/history/a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010!\n\u0002\u0008\u0003\u0008\u0007\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B/\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u000f\u0010\u000f\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J \u0010\u0013\u001a\u00020\u0012*\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0011H\u0096@\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\r\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0015\u0010\u001a\u001a\u00020\u00152\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u001a\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0008\u0010\u001cR\u0014\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u001dR\u0014\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u001eR&\u0010\"\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020!0 0\u001f8\u0000X\u0080\u0004\u00a2\u0006\u000c\n\u0004\u0008\"\u0010#\u001a\u0004\u0008$\u0010%R\u001a\u0010\'\u001a\u0008\u0012\u0004\u0012\u00020\u00180&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(\u00a8\u0006)"
    }
    d2 = {
        "Ltop/cycdm/cycapp/ui/history/HistoryVM;",
        "Ltop/cycdm/cycapp/BaseVM;",
        "Ltop/cycdm/cycapp/ui/history/w;",
        "Ltop/cycdm/cycapp/ui/history/a;",
        "Landroidx/lifecycle/SavedStateHandle;",
        "savedStateHandle",
        "Lkotlin/Function0;",
        "Ltop/cycdm/cycapp/ui/common/page/HistoryPagingSource;",
        "historySourceFactor",
        "Lg8/g;",
        "userDataRep",
        "Lg8/h;",
        "userRep",
        "<init>",
        "(Landroidx/lifecycle/SavedStateHandle;Lkotlin/jvm/functions/Function0;Lg8/g;Lg8/h;)V",
        "createInitialState",
        "()Ltop/cycdm/cycapp/ui/history/w;",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/t;",
        "initData",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lkotlinx/coroutines/w1;",
        "clearHistory",
        "()Lkotlinx/coroutines/w1;",
        "",
        "id",
        "deleteHistory",
        "(I)Lkotlinx/coroutines/w1;",
        "Lkotlin/jvm/functions/Function0;",
        "Lg8/g;",
        "Lg8/h;",
        "Lkotlinx/coroutines/flow/d;",
        "Landroidx/paging/PagingData;",
        "Ltop/cycdm/model/j;",
        "historyPager",
        "Lkotlinx/coroutines/flow/d;",
        "getHistoryPager$app_adRelease",
        "()Lkotlinx/coroutines/flow/d;",
        "",
        "_deleteList",
        "Ljava/util/List;",
        "app_adRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final $stable:I = 0x8


# instance fields
.field private final _deleteList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final historyPager:Lkotlinx/coroutines/flow/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/d;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final historySourceFactor:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Ltop/cycdm/cycapp/ui/common/page/HistoryPagingSource;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final userDataRep:Lg8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final userRep:Lg8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroidx/lifecycle/SavedStateHandle;Lkotlin/jvm/functions/Function0;Lg8/g;Lg8/h;)V
    .locals 10
    .param p1    # Landroidx/lifecycle/SavedStateHandle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lg8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lg8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/lifecycle/SavedStateHandle;",
            "Lkotlin/jvm/functions/Function0<",
            "Ltop/cycdm/cycapp/ui/common/page/HistoryPagingSource;",
            ">;",
            "Lg8/g;",
            "Lg8/h;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljavax/inject/Inject;
    .end annotation

    .line 1
    invoke-direct {p0, p1}, Ltop/cycdm/cycapp/BaseVM;-><init>(Landroidx/lifecycle/SavedStateHandle;)V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->historySourceFactor:Lkotlin/jvm/functions/Function0;

    .line 5
    .line 6
    iput-object p3, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->userDataRep:Lg8/g;

    .line 7
    .line 8
    iput-object p4, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->userRep:Lg8/h;

    .line 9
    .line 10
    new-instance v0, Landroidx/paging/Pager;

    .line 11
    .line 12
    new-instance v1, Landroidx/paging/PagingConfig;

    .line 13
    .line 14
    const/16 v8, 0x38

    .line 15
    .line 16
    const/4 v9, 0x0

    .line 17
    const/4 v2, 0x1

    .line 18
    const/16 v3, 0x14

    .line 19
    .line 20
    const/4 v4, 0x0

    .line 21
    const/4 v5, 0x0

    .line 22
    const/4 v6, 0x0

    .line 23
    const/4 v7, 0x0

    .line 24
    invoke-direct/range {v1 .. v9}, Landroidx/paging/PagingConfig;-><init>(IIZIIIILkotlin/jvm/internal/n;)V

    .line 25
    .line 26
    .line 27
    new-instance v3, Ltop/cycdm/cycapp/ui/history/x;

    .line 28
    .line 29
    invoke-direct {v3, p0}, Ltop/cycdm/cycapp/ui/history/x;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;)V

    .line 30
    .line 31
    .line 32
    const/4 v4, 0x2

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v2, 0x0

    .line 35
    invoke-direct/range {v0 .. v5}, Landroidx/paging/Pager;-><init>(Landroidx/paging/PagingConfig;Ljava/lang/Object;Lkotlin/jvm/functions/Function0;ILkotlin/jvm/internal/n;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {v0}, Landroidx/paging/Pager;->getFlow()Lkotlinx/coroutines/flow/d;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    invoke-static {p0}, Landroidx/lifecycle/ViewModelKt;->getViewModelScope(Landroidx/lifecycle/ViewModel;)Lkotlinx/coroutines/o0;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    invoke-static {p1, p2}, Landroidx/paging/CachedPagingDataKt;->cachedIn(Lkotlinx/coroutines/flow/d;Lkotlinx/coroutines/o0;)Lkotlinx/coroutines/flow/d;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->historyPager:Lkotlinx/coroutines/flow/d;

    .line 51
    .line 52
    new-instance p1, Ljava/util/ArrayList;

    .line 53
    .line 54
    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 55
    .line 56
    .line 57
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->_deleteList:Ljava/util/List;

    .line 58
    .line 59
    return-void
.end method

.method public static final synthetic access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lg8/g;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->userDataRep:Lg8/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getUserRep$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lg8/h;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->userRep:Lg8/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$get_deleteList$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->_deleteList:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->historyPager$lambda$0(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Landroidx/paging/PagingSource;

    move-result-object p0

    return-object p0
.end method

.method private static final historyPager$lambda$0(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->historySourceFactor:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Landroidx/paging/PagingSource;

    .line 8
    .line 9
    return-object p0
.end method


# virtual methods
.method public final clearHistory()Lkotlinx/coroutines/w1;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;Lkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public bridge synthetic createInitialState()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->createInitialState()Ltop/cycdm/cycapp/ui/history/w;

    move-result-object v0

    return-object v0
.end method

.method public createInitialState()Ltop/cycdm/cycapp/ui/history/w;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 2
    new-instance v0, Ltop/cycdm/cycapp/ui/history/w;

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2, v1}, Ltop/cycdm/cycapp/ui/history/w;-><init>(Ljava/util/List;ILkotlin/jvm/internal/n;)V

    return-object v0
.end method

.method public final deleteHistory(I)Lkotlinx/coroutines/w1;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, p1, v1}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;ILkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 p1, 0x1

    .line 8
    const/4 v2, 0x0

    .line 9
    invoke-static {p0, v2, v0, p1, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method

.method public final getHistoryPager$app_adRelease()Lkotlinx/coroutines/flow/d;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/d;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM;->historyPager:Lkotlinx/coroutines/flow/d;

    .line 2
    .line 3
    return-object v0
.end method

.method public initData(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lorg/orbitmvi/orbit/syntax/simple/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    .line 1
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 2
    .line 3
    return-object p1
.end method
