.class public final synthetic Ltop/cycdm/cycapp/ui/email/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/email/EmailVM;

.field public final synthetic b:Landroidx/compose/runtime/MutableState;

.field public final synthetic c:Landroidx/compose/runtime/MutableState;


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/f;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/f;->b:Landroidx/compose/runtime/MutableState;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/email/f;->c:Landroidx/compose/runtime/MutableState;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/f;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/f;->b:Landroidx/compose/runtime/MutableState;

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/f;->c:Landroidx/compose/runtime/MutableState;

    invoke-static {v0, v1, v2}, Ltop/cycdm/cycapp/ui/email/EmailScreenKt;->m(Ltop/cycdm/cycapp/ui/email/EmailVM;Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;)Lkotlin/t;

    move-result-object v0

    return-object v0
.end method
