.class public final Lcom/kwad/components/ct/detail/ad/presenter/c;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private aec:Lcom/kwad/components/core/webview/jshandler/bn$a;

.field private alX:Lcom/kwad/components/ct/detail/e/a;

.field private amo:Lcom/kwad/components/core/j/a;

.field private amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

.field private ams:I

.field private amt:Z

.field private amu:Lcom/kwad/components/core/webview/jshandler/bi$b;

.field private es:Lcom/kwad/components/core/webview/a;

.field private et:Lcom/kwad/sdk/core/webview/b;

.field private eu:I

.field private ev:Lcom/kwad/components/core/webview/jshandler/ba;

.field private ey:Lcom/kwad/components/core/webview/jshandler/al$b;

.field private ez:Lcom/kwad/components/core/webview/jshandler/at$b;

.field private fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

.field private time:J


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ams:I

    .line 6
    .line 7
    const/4 v0, -0x1

    .line 8
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->eu:I

    .line 9
    .line 10
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;

    .line 11
    .line 12
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 13
    .line 14
    .line 15
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 16
    .line 17
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;

    .line 18
    .line 19
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 20
    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amo:Lcom/kwad/components/core/j/a;

    .line 23
    .line 24
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/c$6;

    .line 25
    .line 26
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$6;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 27
    .line 28
    .line 29
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amu:Lcom/kwad/components/core/webview/jshandler/bi$b;

    .line 30
    .line 31
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/c$7;

    .line 32
    .line 33
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$7;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 34
    .line 35
    .line 36
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->aec:Lcom/kwad/components/core/webview/jshandler/bn$a;

    .line 37
    .line 38
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/c$8;

    .line 39
    .line 40
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$8;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 41
    .line 42
    .line 43
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ey:Lcom/kwad/components/core/webview/jshandler/al$b;

    .line 44
    .line 45
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/c$9;

    .line 46
    .line 47
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$9;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 48
    .line 49
    .line 50
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ez:Lcom/kwad/components/core/webview/jshandler/at$b;

    .line 51
    .line 52
    return-void
.end method

.method public static synthetic A(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->xI()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic B(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic C(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic D(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic E(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic F(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic G(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic H(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/e/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic I(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->nk()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic J(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic K(Lcom/kwad/components/ct/detail/ad/presenter/c;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->time:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/c;)I
    .locals 2

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ams:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ams:I

    return v0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/c;I)I
    .locals 0

    const/4 p1, 0x0

    .line 2
    iput p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ams:I

    return p1
.end method

.method private a(Lcom/kwad/components/core/webview/a;)V
    .locals 5

    .line 4
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ac;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    new-instance v3, Lcom/kwad/components/ct/detail/ad/presenter/c$3;

    invoke-direct {v3, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$3;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    const/4 v4, 0x0

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/kwad/components/core/webview/jshandler/ac;-><init>(Lcom/kwad/sdk/core/webview/b;Lcom/kwad/components/core/e/d/c;Lcom/kwad/sdk/core/webview/d/a/a;B)V

    .line 5
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 6
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/z;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    new-instance v3, Lcom/kwad/components/ct/detail/ad/presenter/c$4;

    invoke-direct {v3, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$4;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    invoke-direct {v0, v1, v2, v3}, Lcom/kwad/components/core/webview/jshandler/z;-><init>(Lcom/kwad/sdk/core/webview/b;Lcom/kwad/components/core/e/d/c;Lcom/kwad/sdk/core/webview/d/a/a;)V

    .line 7
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 8
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ag;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/ag;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 9
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/aj;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/aj;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 10
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ae;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/ae;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 11
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/at;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ez:Lcom/kwad/components/core/webview/jshandler/at$b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 12
    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/b;->dh(Lcom/kwad/sdk/core/response/model/AdTemplate;)Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lcom/kwad/components/core/webview/jshandler/at;-><init>(Lcom/kwad/components/core/webview/jshandler/at$b;Ljava/lang/String;)V

    .line 13
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 14
    new-instance v0, Lcom/kwad/components/core/webview/tachikoma/b/f;

    invoke-direct {v0}, Lcom/kwad/components/core/webview/tachikoma/b/f;-><init>()V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 15
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ba;

    invoke-direct {v0}, Lcom/kwad/components/core/webview/jshandler/ba;-><init>()V

    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ev:Lcom/kwad/components/core/webview/jshandler/ba;

    .line 16
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 17
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/bd;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    invoke-direct {v0, v1, v2}, Lcom/kwad/components/core/webview/jshandler/bd;-><init>(Lcom/kwad/sdk/core/webview/b;Lcom/kwad/components/core/e/d/c;)V

    .line 18
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 19
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/al;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ey:Lcom/kwad/components/core/webview/jshandler/al$b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/al;-><init>(Lcom/kwad/components/core/webview/jshandler/al$b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 20
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/an;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/an;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    .line 21
    new-instance v1, Lcom/kwad/components/core/webview/jshandler/o;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v1, v2}, Lcom/kwad/components/core/webview/jshandler/o;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v1}, Lcom/kwad/components/core/webview/a;->b(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 22
    new-instance v1, Lcom/kwad/components/core/webview/jshandler/n;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v1, v2}, Lcom/kwad/components/core/webview/jshandler/n;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v1}, Lcom/kwad/components/core/webview/a;->b(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 23
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/c$5;

    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/c$5;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    invoke-virtual {v0, v1}, Lcom/kwad/components/core/webview/jshandler/an;->a(Lcom/kwad/components/core/webview/jshandler/an$a;)V

    .line 24
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 25
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/bn;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->aec:Lcom/kwad/components/core/webview/jshandler/bn$a;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/bn;-><init>(Lcom/kwad/components/core/webview/jshandler/bn$a;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 26
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/bi;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amu:Lcom/kwad/components/core/webview/jshandler/bi$b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/bi;-><init>(Lcom/kwad/components/core/webview/jshandler/bi$b;)V

    .line 27
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/c;Z)V
    .locals 0

    .line 3
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/c;->bn(Z)V

    return-void
.end method

.method private aC()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    .line 2
    .line 3
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 4
    .line 5
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/core/webview/b;->setAdTemplate(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    .line 11
    .line 12
    const/4 v1, 0x0

    .line 13
    iput v1, v0, Lcom/kwad/sdk/core/webview/b;->mScreenOrientation:I

    .line 14
    .line 15
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 16
    .line 17
    iput-object v1, v0, Lcom/kwad/sdk/core/webview/b;->bIX:Lcom/kwad/sdk/widget/i;

    .line 18
    .line 19
    iput-object v1, v0, Lcom/kwad/sdk/core/webview/b;->SI:Landroid/view/ViewGroup;

    .line 20
    .line 21
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 22
    .line 23
    iput-object v1, v0, Lcom/kwad/sdk/core/webview/b;->Sc:Landroid/webkit/WebView;

    .line 24
    .line 25
    return-void
.end method

.method private aE()V
    .locals 3
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "SetJavaScriptEnabled",
            "AddJavascriptInterface",
            "JavascriptInterface"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->aF()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/core/webview/a;

    .line 5
    .line 6
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 7
    .line 8
    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/a;-><init>(Landroid/webkit/WebView;)V

    .line 9
    .line 10
    .line 11
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->es:Lcom/kwad/components/core/webview/a;

    .line 12
    .line 13
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->a(Lcom/kwad/components/core/webview/a;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 17
    .line 18
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->es:Lcom/kwad/components/core/webview/a;

    .line 19
    .line 20
    const-string v2, "KwaiAd"

    .line 21
    .line 22
    invoke-virtual {v0, v1, v2}, Landroid/webkit/WebView;->addJavascriptInterface(Ljava/lang/Object;Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method private aF()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->es:Lcom/kwad/components/core/webview/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/kwad/components/core/webview/a;->destroy()V

    .line 6
    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->es:Lcom/kwad/components/core/webview/a;

    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method private aM()V
    .locals 3

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->eu:I

    .line 2
    .line 3
    const/4 v1, -0x1

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    const-string v0, "timeout"

    .line 7
    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/4 v1, 0x1

    .line 10
    if-eq v0, v1, :cond_1

    .line 11
    .line 12
    const-string v0, "h5error"

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_1
    const-string v0, "others"

    .line 16
    .line 17
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    .line 18
    .line 19
    const-string v2, "show webCard fail, reason: "

    .line 20
    .line 21
    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 25
    .line 26
    .line 27
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    const-string v1, "AdPlayEndWebPresenter"

    .line 32
    .line 33
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->w(Ljava/lang/String;Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/c;I)I
    .locals 0

    .line 1
    iput p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->eu:I

    return p1
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 2
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    return-object p0
.end method

.method private bn(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 4
    .line 5
    const/16 v1, 0x9

    .line 6
    .line 7
    invoke-virtual {v0, p1, v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->h(ZI)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/c;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ams:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic h(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic i(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private initView()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundColor(I)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 14
    .line 15
    invoke-virtual {v0}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {v0, v1}, Landroid/graphics/drawable/Drawable;->setAlpha(I)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static synthetic j(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic k(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic l(Lcom/kwad/components/ct/detail/ad/presenter/c;)Landroid/content/Context;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic m(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic n(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private nk()V
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->bn(Z)V

    .line 3
    .line 4
    .line 5
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 6
    .line 7
    const/16 v1, 0x32

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-static {v0, v1, v2}, Lcom/kwad/sdk/utils/ca;->a(Landroid/view/View;IZ)Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ev:Lcom/kwad/components/core/webview/jshandler/ba;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    invoke-virtual {v0}, Lcom/kwad/components/core/webview/jshandler/ba;->ux()V

    .line 22
    .line 23
    .line 24
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 25
    .line 26
    const/4 v1, 0x4

    .line 27
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ev:Lcom/kwad/components/core/webview/jshandler/ba;

    .line 31
    .line 32
    if-eqz v0, :cond_2

    .line 33
    .line 34
    invoke-virtual {v0}, Lcom/kwad/components/core/webview/jshandler/ba;->uy()V

    .line 35
    .line 36
    .line 37
    :cond_2
    :goto_0
    return-void
.end method

.method public static synthetic o(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic p(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic q(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic r(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic s(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic t(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic u(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic v(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->xJ()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic w(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic x(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private xI()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->aE()V

    .line 8
    .line 9
    .line 10
    const/4 v0, -0x1

    .line 11
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->eu:I

    .line 12
    .line 13
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 14
    .line 15
    .line 16
    move-result-wide v0

    .line 17
    iput-wide v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->time:J

    .line 18
    .line 19
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 20
    .line 21
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 22
    .line 23
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/b;->dh(Lcom/kwad/sdk/core/response/model/AdTemplate;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v0, v1}, Landroid/webkit/WebView;->loadUrl(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private xJ()V
    .locals 2

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->eu:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-ne v0, v1, :cond_2

    .line 5
    .line 6
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ev:Lcom/kwad/components/core/webview/jshandler/ba;

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-virtual {v0}, Lcom/kwad/components/core/webview/jshandler/ba;->uv()V

    .line 11
    .line 12
    .line 13
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->ev:Lcom/kwad/components/core/webview/jshandler/ba;

    .line 20
    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    invoke-virtual {v0}, Lcom/kwad/components/core/webview/jshandler/ba;->uw()V

    .line 24
    .line 25
    .line 26
    :cond_1
    return-void

    .line 27
    :cond_2
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->aM()V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static synthetic y(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->aF()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic z(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/sdk/core/webview/KSApiWebView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/b;->di(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    xor-int/lit8 v0, v0, 0x1

    .line 15
    .line 16
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amt:Z

    .line 17
    .line 18
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 19
    .line 20
    const/16 v1, 0x8

    .line 21
    .line 22
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amt:Z

    .line 26
    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    return-void

    .line 30
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 31
    .line 32
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 33
    .line 34
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 35
    .line 36
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 37
    .line 38
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 39
    .line 40
    new-instance v0, Lcom/kwad/sdk/core/webview/b;

    .line 41
    .line 42
    invoke-direct {v0}, Lcom/kwad/sdk/core/webview/b;-><init>()V

    .line 43
    .line 44
    .line 45
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->et:Lcom/kwad/sdk/core/webview/b;

    .line 46
    .line 47
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->initView()V

    .line 48
    .line 49
    .line 50
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->aC()V

    .line 51
    .line 52
    .line 53
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 54
    .line 55
    if-eqz v0, :cond_1

    .line 56
    .line 57
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 58
    .line 59
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 60
    .line 61
    .line 62
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 63
    .line 64
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 65
    .line 66
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amo:Lcom/kwad/components/core/j/a;

    .line 67
    .line 68
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 69
    .line 70
    .line 71
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_root_container:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 13
    .line 14
    sget v0, Lcom/kwad/sdk/R$id;->ksad_play_end_web_card:I

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    check-cast v0, Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 23
    .line 24
    return-void
.end method

.method public final onDestroy()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onDestroy()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/kwad/sdk/core/webview/KSApiWebView;->release()V

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amt:Z

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 10
    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 14
    .line 15
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 19
    .line 20
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 21
    .line 22
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c;->amo:Lcom/kwad/components/core/j/a;

    .line 23
    .line 24
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    return-void
.end method
