.class public Lcom/sigmob/sdk/base/views/r$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/sigmob/sdk/base/views/r;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# static fields
.field public static final a:I = 0x3c

.field public static final b:I = 0x0

.field public static final c:I = 0x5

.field public static final d:I = -0x1

.field public static final e:F = 8.0f

.field public static final f:Landroid/graphics/Paint$Cap;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroid/graphics/Paint$Cap;->ROUND:Landroid/graphics/Paint$Cap;

    sput-object v0, Lcom/sigmob/sdk/base/views/r$a;->f:Landroid/graphics/Paint$Cap;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
