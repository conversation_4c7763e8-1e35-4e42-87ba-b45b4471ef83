.class final Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/a/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

.field amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method private xK()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)Z

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 8
    .line 9
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->c(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)Z

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 13
    .line 14
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->j(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Landroid/view/ViewGroup;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-virtual {v0, v1}, Landroid/view/View;->setTranslationX(F)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 23
    .line 24
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->i(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Landroid/widget/FrameLayout;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 29
    .line 30
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->k(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    neg-int v1, v1

    .line 35
    int-to-float v1, v1

    .line 36
    invoke-virtual {v0, v1}, Landroid/view/View;->setTranslationX(F)V

    .line 37
    .line 38
    .line 39
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 40
    .line 41
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->e(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 46
    .line 47
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->l(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Ljava/lang/Runnable;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method private xL()V
    .locals 2

    .line 1
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8$1;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;)V

    .line 4
    .line 5
    .line 6
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 9
    .line 10
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->i(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Landroid/widget/FrameLayout;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 19
    .line 20
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 0

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pQ()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->xK()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->xL()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final pR()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pR()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->xK()V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->g(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 13
    .line 14
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->h(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 18
    .line 19
    if-eqz v0, :cond_0

    .line 20
    .line 21
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 22
    .line 23
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->i(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Landroid/widget/FrameLayout;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 32
    .line 33
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 34
    .line 35
    .line 36
    :cond_0
    return-void
.end method
