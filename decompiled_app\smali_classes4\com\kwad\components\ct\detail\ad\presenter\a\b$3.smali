.class final Lcom/kwad/components/ct/detail/ad/presenter/a/b$3;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/a/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$3;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$3;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->b(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method
