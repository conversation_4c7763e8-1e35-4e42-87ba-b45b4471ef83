.class public final synthetic Ltop/cycdm/cycapp/ui/history/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/MutableState;


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/runtime/MutableState;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/s;->a:Landroidx/compose/runtime/MutableState;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/s;->a:Landroidx/compose/runtime/MutableState;

    invoke-static {v0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->m(Landroidx/compose/runtime/MutableState;)<PERSON><PERSON><PERSON>/t;

    move-result-object v0

    return-object v0
.end method
