.class final Lcom/kwad/components/ct/detail/ad/presenter/c$1;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amv:Lcom/kwad/components/ct/detail/ad/presenter/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->a(Lcom/kwad/components/ct/detail/ad/presenter/c;)I

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->b(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/b;->dg(Lcom/kwad/sdk/core/response/model/AdTemplate;)I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    const/4 v1, 0x1

    .line 17
    if-gtz v0, :cond_0

    .line 18
    .line 19
    move v0, v1

    .line 20
    :cond_0
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 21
    .line 22
    invoke-static {v2}, Lcom/kwad/components/ct/detail/ad/presenter/c;->c(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    iget-object v2, v2, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 27
    .line 28
    invoke-virtual {v2}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->Cm()Z

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    if-eqz v2, :cond_1

    .line 33
    .line 34
    goto/16 :goto_1

    .line 35
    .line 36
    :cond_1
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 37
    .line 38
    invoke-static {v2}, Lcom/kwad/components/ct/detail/ad/presenter/c;->d(Lcom/kwad/components/ct/detail/ad/presenter/c;)I

    .line 39
    .line 40
    .line 41
    move-result v2

    .line 42
    const/4 v3, 0x0

    .line 43
    if-eq v2, v1, :cond_3

    .line 44
    .line 45
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 46
    .line 47
    invoke-static {v2}, Lcom/kwad/components/ct/detail/ad/presenter/c;->d(Lcom/kwad/components/ct/detail/ad/presenter/c;)I

    .line 48
    .line 49
    .line 50
    move-result v2

    .line 51
    sub-int/2addr v2, v1

    .line 52
    rem-int/2addr v2, v0

    .line 53
    if-nez v2, :cond_2

    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 57
    .line 58
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->w(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 63
    .line 64
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 65
    .line 66
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->x(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    iput-boolean v1, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 71
    .line 72
    return-void

    .line 73
    :cond_3
    :goto_0
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yl()Z

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    if-eqz v0, :cond_5

    .line 78
    .line 79
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yj()Z

    .line 80
    .line 81
    .line 82
    move-result v0

    .line 83
    if-eqz v0, :cond_5

    .line 84
    .line 85
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 86
    .line 87
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->e(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 92
    .line 93
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->hasNext()Z

    .line 94
    .line 95
    .line 96
    move-result v0

    .line 97
    if-eqz v0, :cond_4

    .line 98
    .line 99
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 100
    .line 101
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->f(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    iput-boolean v1, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 106
    .line 107
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 108
    .line 109
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->g(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 114
    .line 115
    return-void

    .line 116
    :cond_4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 117
    .line 118
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->h(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    iput-boolean v1, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 123
    .line 124
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 125
    .line 126
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->i(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 131
    .line 132
    return-void

    .line 133
    :cond_5
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yk()Z

    .line 134
    .line 135
    .line 136
    move-result v0

    .line 137
    if-eqz v0, :cond_7

    .line 138
    .line 139
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 140
    .line 141
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->b(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 146
    .line 147
    .line 148
    move-result-object v0

    .line 149
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/a;->aJ(Lcom/kwad/sdk/core/response/model/AdInfo;)Z

    .line 150
    .line 151
    .line 152
    move-result v0

    .line 153
    if-nez v0, :cond_7

    .line 154
    .line 155
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 156
    .line 157
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->j(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 158
    .line 159
    .line 160
    move-result-object v0

    .line 161
    iput-boolean v1, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 162
    .line 163
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 164
    .line 165
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->k(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 166
    .line 167
    .line 168
    move-result-object v0

    .line 169
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 170
    .line 171
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 172
    .line 173
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->l(Lcom/kwad/components/ct/detail/ad/presenter/c;)Landroid/content/Context;

    .line 174
    .line 175
    .line 176
    move-result-object v0

    .line 177
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 178
    .line 179
    invoke-static {v2}, Lcom/kwad/components/ct/detail/ad/presenter/c;->b(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 180
    .line 181
    .line 182
    move-result-object v2

    .line 183
    invoke-static {v0, v2}, Lcom/kwad/components/core/page/AdWebViewActivityProxy;->launch(Landroid/content/Context;Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 184
    .line 185
    .line 186
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 187
    .line 188
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->b(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 189
    .line 190
    .line 191
    move-result-object v0

    .line 192
    const/16 v2, 0x6d

    .line 193
    .line 194
    const/4 v3, 0x0

    .line 195
    invoke-static {v0, v2, v3}, Lcom/kwad/sdk/core/adlog/c;->a(Lcom/kwad/sdk/core/response/model/AdTemplate;ILcom/kwad/sdk/utils/ai$a;)V

    .line 196
    .line 197
    .line 198
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 199
    .line 200
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->m(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 205
    .line 206
    instance-of v0, v0, Lcom/kwad/components/ct/detail/ad/a;

    .line 207
    .line 208
    if-eqz v0, :cond_6

    .line 209
    .line 210
    invoke-static {}, Lcom/kwad/components/core/t/d;->sM()Z

    .line 211
    .line 212
    .line 213
    move-result v0

    .line 214
    if-eqz v0, :cond_6

    .line 215
    .line 216
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 217
    .line 218
    .line 219
    move-result-object v0

    .line 220
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 221
    .line 222
    invoke-static {v2}, Lcom/kwad/components/ct/detail/ad/presenter/c;->n(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 223
    .line 224
    .line 225
    move-result-object v2

    .line 226
    iget-object v2, v2, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 227
    .line 228
    invoke-virtual {v0, v2, v1}, Lcom/kwad/components/ct/e/b;->b(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 229
    .line 230
    .line 231
    :cond_6
    :goto_1
    return-void

    .line 232
    :cond_7
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yl()Z

    .line 233
    .line 234
    .line 235
    move-result v0

    .line 236
    if-nez v0, :cond_9

    .line 237
    .line 238
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yj()Z

    .line 239
    .line 240
    .line 241
    move-result v0

    .line 242
    if-eqz v0, :cond_9

    .line 243
    .line 244
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 245
    .line 246
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->o(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 247
    .line 248
    .line 249
    move-result-object v0

    .line 250
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 251
    .line 252
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->hasNext()Z

    .line 253
    .line 254
    .line 255
    move-result v0

    .line 256
    if-eqz v0, :cond_8

    .line 257
    .line 258
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 259
    .line 260
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->p(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 261
    .line 262
    .line 263
    move-result-object v0

    .line 264
    iput-boolean v1, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 265
    .line 266
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 267
    .line 268
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->q(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 269
    .line 270
    .line 271
    move-result-object v0

    .line 272
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 273
    .line 274
    return-void

    .line 275
    :cond_8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 276
    .line 277
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->r(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 278
    .line 279
    .line 280
    move-result-object v0

    .line 281
    iput-boolean v1, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 282
    .line 283
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 284
    .line 285
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->s(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 286
    .line 287
    .line 288
    move-result-object v0

    .line 289
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 290
    .line 291
    return-void

    .line 292
    :cond_9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 293
    .line 294
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->t(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 295
    .line 296
    .line 297
    move-result-object v0

    .line 298
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 299
    .line 300
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 301
    .line 302
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->u(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 303
    .line 304
    .line 305
    move-result-object v0

    .line 306
    iput-boolean v3, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 307
    .line 308
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$1;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 309
    .line 310
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->v(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 311
    .line 312
    .line 313
    return-void
.end method
