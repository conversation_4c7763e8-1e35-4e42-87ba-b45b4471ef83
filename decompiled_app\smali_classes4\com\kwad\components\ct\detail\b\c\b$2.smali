.class final Lcom/kwad/components/ct/detail/b/c/b$2;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awL:Lcom/kwad/components/ct/detail/b/c/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 4

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pQ()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/b;->BY()Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    const-string v1, "DetailLogVideoPresenter"

    .line 9
    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    new-instance v0, Ljava/lang/StringBuilder;

    .line 13
    .line 14
    const-string v2, "position: "

    .line 15
    .line 16
    invoke-direct {v0, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 20
    .line 21
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/c/b;->h(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 26
    .line 27
    .line 28
    const-string v2, " becomesAttachedOnPageSelected"

    .line 29
    .line 30
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 41
    .line 42
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    .line 43
    .line 44
    .line 45
    move-result-wide v2

    .line 46
    invoke-static {v0, v2, v3}, Lcom/kwad/components/ct/detail/b/c/b;->a(Lcom/kwad/components/ct/detail/b/c/b;J)J

    .line 47
    .line 48
    .line 49
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 50
    .line 51
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->n(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/core/widget/a/b;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    if-nez v0, :cond_1

    .line 56
    .line 57
    const-string v0, "mVisibleHelper is null"

    .line 58
    .line 59
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->w(Ljava/lang/String;Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    return-void

    .line 63
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 64
    .line 65
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->n(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/core/widget/a/b;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 70
    .line 71
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/b;->o(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/sdk/core/i/c;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/widget/a/a;->a(Lcom/kwad/sdk/core/i/c;)V

    .line 76
    .line 77
    .line 78
    return-void
.end method

.method public final pR()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pR()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/b;->BY()Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    const-string v1, "DetailLogVideoPresenter"

    .line 9
    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    new-instance v0, Ljava/lang/StringBuilder;

    .line 13
    .line 14
    const-string v2, "position: "

    .line 15
    .line 16
    invoke-direct {v0, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 20
    .line 21
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/c/b;->h(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 26
    .line 27
    .line 28
    const-string v2, " becomesDetachedOnPageSelected"

    .line 29
    .line 30
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 41
    .line 42
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->n(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/core/widget/a/b;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    if-nez v0, :cond_1

    .line 47
    .line 48
    const-string v0, "mVisibleHelper is null"

    .line 49
    .line 50
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->w(Ljava/lang/String;Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 55
    .line 56
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->n(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/core/widget/a/b;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 61
    .line 62
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/b;->o(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/sdk/core/i/c;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/widget/a/a;->b(Lcom/kwad/sdk/core/i/c;)V

    .line 67
    .line 68
    .line 69
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$2;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 70
    .line 71
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->p(Lcom/kwad/components/ct/detail/b/c/b;)V

    .line 72
    .line 73
    .line 74
    return-void
.end method
