.class final Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/email/EmailVM;->sendEmail(Ljava/lang/String;)Lkotlinx/coroutines/w1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Ltop/cycdm/cycapp/ui/email/x;",
        "Ltop/cycdm/cycapp/ui/email/b;",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.email.EmailVM$sendEmail$1"
    f = "EmailVM.kt"
    i = {
        0x2,
        0x3,
        0x3,
        0x5,
        0x5,
        0x5
    }
    l = {
        0x22,
        0x26,
        0x29,
        0x2a,
        0x2c,
        0x2f,
        0x37
    }
    m = "invokeSuspend"
    n = {
        "$this$intent",
        "$this$intent",
        "token",
        "$this$intent",
        "token",
        "userId"
    }
    s = {
        "L$0",
        "L$0",
        "L$1",
        "L$0",
        "L$1",
        "I$0"
    }
.end annotation


# instance fields
.field final synthetic $email:Ljava/lang/String;

.field I$0:I

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ltop/cycdm/cycapp/ui/email/EmailVM;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ltop/cycdm/cycapp/ui/email/EmailVM;",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->$email:Ljava/lang/String;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->invokeSuspend$lambda$0(Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;

    move-result-object p0

    return-object p0
.end method

.method private static final invokeSuspend$lambda$0(Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Ltop/cycdm/cycapp/ui/email/x;

    .line 6
    .line 7
    sget-object v0, Ltop/cycdm/cycapp/utils/h$b;->a:Ltop/cycdm/cycapp/utils/h$b;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    const/4 v2, 0x2

    .line 11
    invoke-static {p0, v0, v1, v2, v1}, Ltop/cycdm/cycapp/ui/email/x;->b(Ltop/cycdm/cycapp/ui/email/x;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/email/x;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->$email:Ljava/lang/String;

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    invoke-direct {v0, v1, v2, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;-><init>(Ljava/lang/String;Ltop/cycdm/cycapp/ui/email/EmailVM;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    packed-switch v1, :pswitch_data_0

    .line 9
    .line 10
    .line 11
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 12
    .line 13
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 14
    .line 15
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    throw p1

    .line 19
    :pswitch_0
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto/16 :goto_5

    .line 23
    .line 24
    :pswitch_1
    iget v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->I$0:I

    .line 25
    .line 26
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$1:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v3, Ljava/lang/String;

    .line 29
    .line 30
    iget-object v4, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v4, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 33
    .line 34
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    goto/16 :goto_4

    .line 38
    .line 39
    :pswitch_2
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    goto/16 :goto_8

    .line 43
    .line 44
    :pswitch_3
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$1:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v1, Ljava/lang/String;

    .line 47
    .line 48
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 49
    .line 50
    check-cast v3, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 51
    .line 52
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    move-object v4, v3

    .line 56
    move-object v3, v1

    .line 57
    goto/16 :goto_3

    .line 58
    .line 59
    :pswitch_4
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 62
    .line 63
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    goto :goto_2

    .line 67
    :pswitch_5
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    goto :goto_1

    .line 71
    :pswitch_6
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 72
    .line 73
    .line 74
    goto :goto_0

    .line 75
    :pswitch_7
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 76
    .line 77
    .line 78
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 79
    .line 80
    move-object v1, p1

    .line 81
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 82
    .line 83
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->$email:Ljava/lang/String;

    .line 84
    .line 85
    invoke-static {p1}, Lkotlin/text/g0;->v0(Ljava/lang/CharSequence;)Z

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    if-eqz p1, :cond_1

    .line 90
    .line 91
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 92
    .line 93
    const/4 v2, 0x1

    .line 94
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 95
    .line 96
    const-string v2, "\u90ae\u7bb1\u4e3a\u7a7a"

    .line 97
    .line 98
    invoke-static {p1, v1, v2, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    if-ne p1, v0, :cond_0

    .line 103
    .line 104
    goto/16 :goto_7

    .line 105
    .line 106
    :cond_0
    :goto_0
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 107
    .line 108
    return-object p1

    .line 109
    :cond_1
    sget-object p1, Landroidx/core/util/PatternsCompat;->EMAIL_ADDRESS:Ljava/util/regex/Pattern;

    .line 110
    .line 111
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->$email:Ljava/lang/String;

    .line 112
    .line 113
    invoke-virtual {p1, v3}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    invoke-virtual {p1}, Ljava/util/regex/Matcher;->matches()Z

    .line 118
    .line 119
    .line 120
    move-result p1

    .line 121
    if-nez p1, :cond_3

    .line 122
    .line 123
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 124
    .line 125
    const/4 v2, 0x2

    .line 126
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 127
    .line 128
    const-string v2, "\u90ae\u7bb1\u683c\u5f0f\u4e0d\u6b63\u786e"

    .line 129
    .line 130
    invoke-static {p1, v1, v2, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    if-ne p1, v0, :cond_2

    .line 135
    .line 136
    goto/16 :goto_7

    .line 137
    .line 138
    :cond_2
    :goto_1
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 139
    .line 140
    return-object p1

    .line 141
    :cond_3
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 142
    .line 143
    invoke-static {p1}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/g;

    .line 144
    .line 145
    .line 146
    move-result-object p1

    .line 147
    invoke-interface {p1}, Lg8/g;->a()Lkotlinx/coroutines/flow/d;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 152
    .line 153
    const/4 v3, 0x3

    .line 154
    iput v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 155
    .line 156
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/f;->B(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 157
    .line 158
    .line 159
    move-result-object p1

    .line 160
    if-ne p1, v0, :cond_4

    .line 161
    .line 162
    goto/16 :goto_7

    .line 163
    .line 164
    :cond_4
    :goto_2
    check-cast p1, Ljava/lang/String;

    .line 165
    .line 166
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 167
    .line 168
    invoke-static {v3}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/g;

    .line 169
    .line 170
    .line 171
    move-result-object v3

    .line 172
    invoke-interface {v3}, Lg8/g;->getId()Lkotlinx/coroutines/flow/d;

    .line 173
    .line 174
    .line 175
    move-result-object v3

    .line 176
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 177
    .line 178
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$1:Ljava/lang/Object;

    .line 179
    .line 180
    const/4 v4, 0x4

    .line 181
    iput v4, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 182
    .line 183
    invoke-static {v3, p0}, Lkotlinx/coroutines/flow/f;->B(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 184
    .line 185
    .line 186
    move-result-object v3

    .line 187
    if-ne v3, v0, :cond_5

    .line 188
    .line 189
    goto/16 :goto_7

    .line 190
    .line 191
    :cond_5
    move-object v4, v3

    .line 192
    move-object v3, p1

    .line 193
    move-object p1, v4

    .line 194
    move-object v4, v1

    .line 195
    :goto_3
    check-cast p1, Ljava/lang/Number;

    .line 196
    .line 197
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 198
    .line 199
    .line 200
    move-result v1

    .line 201
    const-string p1, ""

    .line 202
    .line 203
    invoke-static {v3, p1}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 204
    .line 205
    .line 206
    move-result p1

    .line 207
    if-nez p1, :cond_9

    .line 208
    .line 209
    const/4 p1, -0x1

    .line 210
    if-ne v1, p1, :cond_6

    .line 211
    .line 212
    goto :goto_6

    .line 213
    :cond_6
    new-instance p1, Ltop/cycdm/cycapp/ui/email/z;

    .line 214
    .line 215
    invoke-direct {p1}, Ltop/cycdm/cycapp/ui/email/z;-><init>()V

    .line 216
    .line 217
    .line 218
    iput-object v4, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 219
    .line 220
    iput-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$1:Ljava/lang/Object;

    .line 221
    .line 222
    iput v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->I$0:I

    .line 223
    .line 224
    const/4 v5, 0x6

    .line 225
    iput v5, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 226
    .line 227
    invoke-static {v4, p1, p0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 228
    .line 229
    .line 230
    move-result-object p1

    .line 231
    if-ne p1, v0, :cond_7

    .line 232
    .line 233
    goto :goto_7

    .line 234
    :cond_7
    :goto_4
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 235
    .line 236
    invoke-static {p1}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$getUserRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/h;

    .line 237
    .line 238
    .line 239
    move-result-object p1

    .line 240
    iget-object v5, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->$email:Ljava/lang/String;

    .line 241
    .line 242
    sget-object v6, Ltop/cycdm/model/FeatureType;->Bind:Ltop/cycdm/model/FeatureType;

    .line 243
    .line 244
    invoke-interface {p1, v3, v1, v5, v6}, Lg8/h;->g(Ljava/lang/String;ILjava/lang/String;Ltop/cycdm/model/FeatureType;)Lkotlinx/coroutines/flow/d;

    .line 245
    .line 246
    .line 247
    move-result-object p1

    .line 248
    new-instance v1, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;

    .line 249
    .line 250
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 251
    .line 252
    invoke-direct {v1, v3, v4, v2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$2;-><init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    .line 253
    .line 254
    .line 255
    invoke-static {p1, v1}, Lkotlinx/coroutines/flow/f;->i(Lkotlinx/coroutines/flow/d;Lkotlin/jvm/functions/Function3;)Lkotlinx/coroutines/flow/d;

    .line 256
    .line 257
    .line 258
    move-result-object p1

    .line 259
    new-instance v1, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;

    .line 260
    .line 261
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 262
    .line 263
    invoke-direct {v1, v3, v4}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;-><init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;)V

    .line 264
    .line 265
    .line 266
    iput-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 267
    .line 268
    iput-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$1:Ljava/lang/Object;

    .line 269
    .line 270
    const/4 v2, 0x7

    .line 271
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 272
    .line 273
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/d;->collect(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 274
    .line 275
    .line 276
    move-result-object p1

    .line 277
    if-ne p1, v0, :cond_8

    .line 278
    .line 279
    goto :goto_7

    .line 280
    :cond_8
    :goto_5
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 281
    .line 282
    return-object p1

    .line 283
    :cond_9
    :goto_6
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 284
    .line 285
    iput-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$0:Ljava/lang/Object;

    .line 286
    .line 287
    iput-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->L$1:Ljava/lang/Object;

    .line 288
    .line 289
    const/4 v1, 0x5

    .line 290
    iput v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->label:I

    .line 291
    .line 292
    const-string v1, "\u8bf7\u5148\u767b\u9646\u8d26\u53f7"

    .line 293
    .line 294
    invoke-static {p1, v4, v1, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 295
    .line 296
    .line 297
    move-result-object p1

    .line 298
    if-ne p1, v0, :cond_a

    .line 299
    .line 300
    :goto_7
    return-object v0

    .line 301
    :cond_a
    :goto_8
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 302
    .line 303
    return-object p1

    .line 304
    nop

    .line 305
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
