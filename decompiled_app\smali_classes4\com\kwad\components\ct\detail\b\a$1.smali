.class final Lcom/kwad/components/ct/detail/b/a$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avc:Lcom/kwad/components/ct/detail/b/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 9

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->a(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 8
    .line 9
    if-eqz v0, :cond_4

    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->b(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 18
    .line 19
    invoke-virtual {v0}, Lcom/kwad/sdk/api/core/fragment/KsFragment;->isAdded()Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_4

    .line 24
    .line 25
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 26
    .line 27
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->c(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 32
    .line 33
    invoke-virtual {v0}, Lcom/kwad/sdk/api/core/fragment/KsFragment;->getActivity()Landroid/app/Activity;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    if-nez v0, :cond_0

    .line 38
    .line 39
    goto/16 :goto_2

    .line 40
    .line 41
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 42
    .line 43
    invoke-virtual {v0}, Lcom/kwad/sdk/mvp/Presenter;->getRootView()Landroid/view/View;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 52
    .line 53
    invoke-virtual {v1}, Lcom/kwad/sdk/mvp/Presenter;->getRootView()Landroid/view/View;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    invoke-virtual {v1}, Landroid/view/View;->getHeight()I

    .line 58
    .line 59
    .line 60
    move-result v1

    .line 61
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 62
    .line 63
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/a;->d(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    invoke-static {}, Lcom/kwad/components/ct/a/b;->wB()Z

    .line 68
    .line 69
    .line 70
    move-result v3

    .line 71
    invoke-static {v2, v3}, Lcom/kwad/components/ct/response/a/a;->f(Lcom/kwad/components/ct/response/model/CtAdTemplate;Z)Lcom/kwad/sdk/core/response/model/b;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    iget-object v3, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 76
    .line 77
    invoke-static {v3}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 78
    .line 79
    .line 80
    move-result-object v3

    .line 81
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 82
    .line 83
    .line 84
    move-result-object v3

    .line 85
    invoke-virtual {v2}, Lcom/kwad/sdk/core/response/model/b;->getWidth()I

    .line 86
    .line 87
    .line 88
    move-result v4

    .line 89
    invoke-virtual {v2}, Lcom/kwad/sdk/core/response/model/b;->getHeight()I

    .line 90
    .line 91
    .line 92
    move-result v5

    .line 93
    const/4 v6, -0x1

    .line 94
    if-eqz v4, :cond_3

    .line 95
    .line 96
    if-nez v5, :cond_1

    .line 97
    .line 98
    goto :goto_0

    .line 99
    :cond_1
    iget-object v7, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 100
    .line 101
    invoke-static {v7}, Lcom/kwad/components/ct/detail/b/a;->d(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 102
    .line 103
    .line 104
    move-result-object v7

    .line 105
    iget-object v7, v7, Lcom/kwad/components/ct/response/model/CtAdTemplate;->photoInfo:Lcom/kwad/components/ct/response/model/CtPhotoInfo;

    .line 106
    .line 107
    iget-object v7, v7, Lcom/kwad/sdk/core/response/model/PhotoInfo;->videoInfo:Lcom/kwad/sdk/core/response/model/PhotoInfo$VideoInfo;

    .line 108
    .line 109
    const/4 v8, 0x0

    .line 110
    invoke-static {v8, v0, v1, v7}, Lcom/kwad/components/ct/response/a/c;->a(Landroid/graphics/Matrix;IILcom/kwad/sdk/core/response/model/PhotoInfo$VideoInfo;)Z

    .line 111
    .line 112
    .line 113
    move-result v1

    .line 114
    if-eqz v1, :cond_2

    .line 115
    .line 116
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 117
    .line 118
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    iput v6, v3, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 127
    .line 128
    iput v6, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 129
    .line 130
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 131
    .line 132
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    invoke-virtual {v1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 137
    .line 138
    .line 139
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 140
    .line 141
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    sget-object v1, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    .line 146
    .line 147
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 148
    .line 149
    .line 150
    goto :goto_1

    .line 151
    :cond_2
    iput v0, v3, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 152
    .line 153
    int-to-float v1, v5

    .line 154
    int-to-float v4, v4

    .line 155
    const/high16 v5, 0x3f800000    # 1.0f

    .line 156
    .line 157
    mul-float/2addr v4, v5

    .line 158
    div-float/2addr v1, v4

    .line 159
    int-to-float v0, v0

    .line 160
    mul-float/2addr v1, v0

    .line 161
    float-to-int v0, v1

    .line 162
    iput v0, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 163
    .line 164
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 165
    .line 166
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 167
    .line 168
    .line 169
    move-result-object v0

    .line 170
    invoke-virtual {v0, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 171
    .line 172
    .line 173
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 174
    .line 175
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 176
    .line 177
    .line 178
    move-result-object v0

    .line 179
    sget-object v1, Landroid/widget/ImageView$ScaleType;->FIT_CENTER:Landroid/widget/ImageView$ScaleType;

    .line 180
    .line 181
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 182
    .line 183
    .line 184
    goto :goto_1

    .line 185
    :cond_3
    :goto_0
    iput v6, v3, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 186
    .line 187
    iput v6, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 188
    .line 189
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 190
    .line 191
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    invoke-virtual {v0, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 196
    .line 197
    .line 198
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 199
    .line 200
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    sget-object v1, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    .line 205
    .line 206
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 207
    .line 208
    .line 209
    :goto_1
    invoke-virtual {v2}, Lcom/kwad/sdk/core/response/model/b;->getUrl()Ljava/lang/String;

    .line 210
    .line 211
    .line 212
    move-result-object v0

    .line 213
    :try_start_0
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 214
    .line 215
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/a;->g(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 216
    .line 217
    .line 218
    move-result-object v1

    .line 219
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 220
    .line 221
    invoke-static {v1}, Lcom/kwad/sdk/glide/c;->h(Lcom/kwad/sdk/api/core/fragment/KsFragment;)Lcom/kwad/sdk/glide/g;

    .line 222
    .line 223
    .line 224
    move-result-object v1

    .line 225
    invoke-virtual {v1, v0}, Lcom/kwad/sdk/glide/g;->hh(Ljava/lang/String;)Lcom/kwad/sdk/glide/f;

    .line 226
    .line 227
    .line 228
    move-result-object v1

    .line 229
    new-instance v2, Lcom/kwad/components/ct/b/a;

    .line 230
    .line 231
    iget-object v3, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 232
    .line 233
    invoke-static {v3}, Lcom/kwad/components/ct/detail/b/a;->d(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 234
    .line 235
    .line 236
    move-result-object v3

    .line 237
    invoke-direct {v2, v0, v3}, Lcom/kwad/components/ct/b/a;-><init>(Ljava/lang/String;Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 238
    .line 239
    .line 240
    invoke-virtual {v1, v2}, Lcom/kwad/sdk/glide/f;->a(Lcom/kwad/sdk/glide/request/h;)Lcom/kwad/sdk/glide/f;

    .line 241
    .line 242
    .line 243
    move-result-object v1

    .line 244
    new-instance v2, Lcom/kwad/components/ct/detail/b/a$1$1;

    .line 245
    .line 246
    invoke-direct {v2, p0, v0}, Lcom/kwad/components/ct/detail/b/a$1$1;-><init>(Lcom/kwad/components/ct/detail/b/a$1;Ljava/lang/String;)V

    .line 247
    .line 248
    .line 249
    invoke-virtual {v1, v2}, Lcom/kwad/sdk/glide/f;->b(Lcom/kwad/sdk/glide/request/h;)Lcom/kwad/sdk/glide/f;

    .line 250
    .line 251
    .line 252
    move-result-object v0

    .line 253
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 254
    .line 255
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 256
    .line 257
    .line 258
    move-result-object v1

    .line 259
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/glide/f;->b(Landroid/widget/ImageView;)Lcom/kwad/sdk/glide/request/a/k;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 260
    .line 261
    .line 262
    return-void

    .line 263
    :catchall_0
    move-exception v0

    .line 264
    invoke-static {v0}, Lcom/kwad/sdk/core/d/c;->printStackTraceOnly(Ljava/lang/Throwable;)V

    .line 265
    .line 266
    .line 267
    :cond_4
    :goto_2
    return-void
.end method
