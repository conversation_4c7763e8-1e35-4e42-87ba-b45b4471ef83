.class public final synthetic Ltop/cycdm/cycapp/ui/history/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/history/HistoryVM;


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/x;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/x;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    invoke-static {v0}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->c(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Landroidx/paging/PagingSource;

    move-result-object v0

    return-object v0
.end method
