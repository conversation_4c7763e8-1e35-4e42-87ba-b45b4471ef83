.class public interface abstract Lcom/sigmob/sdk/downloader/core/breakpoint/g;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract a(I)Lcom/sigmob/sdk/downloader/core/breakpoint/c;
.end method

.method public abstract a(Lcom/sigmob/sdk/downloader/f;)Lcom/sigmob/sdk/downloader/core/breakpoint/c;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract a(Lcom/sigmob/sdk/downloader/f;Lcom/sigmob/sdk/downloader/core/breakpoint/c;)Lcom/sigmob/sdk/downloader/core/breakpoint/c;
.end method

.method public abstract a(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract a()Z
.end method

.method public abstract a(Lcom/sigmob/sdk/downloader/core/breakpoint/c;)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract b(Lcom/sigmob/sdk/downloader/f;)I
.end method

.method public abstract b(I)V
.end method

.method public abstract c(I)Z
.end method
