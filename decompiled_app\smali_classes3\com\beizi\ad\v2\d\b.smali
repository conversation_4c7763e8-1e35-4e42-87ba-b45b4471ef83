.class public Lcom/beizi/ad/v2/d/b;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private a(Ljava/net/URL;)Ljava/net/HttpURLConnection;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 79
    invoke-virtual {p1}, Ljava/net/URL;->openConnection()Ljava/net/URLConnection;

    move-result-object p1

    check-cast p1, Ljava/net/HttpURLConnection;

    const/16 v0, 0x1388

    .line 80
    invoke-virtual {p1, v0}, Ljava/net/URLConnection;->setConnectTimeout(I)V

    .line 81
    invoke-virtual {p1, v0}, Ljava/net/URLConnection;->setReadTimeout(I)V

    const/4 v0, 0x1

    .line 82
    invoke-virtual {p1, v0}, Ljava/net/URLConnection;->setDoOutput(Z)V

    .line 83
    invoke-virtual {p1, v0}, Ljava/net/URLConnection;->setDoInput(Z)V

    const/4 v0, 0x0

    .line 84
    invoke-virtual {p1, v0}, Ljava/net/URLConnection;->setUseCaches(Z)V

    .line 85
    const-string v0, "POST"

    invoke-virtual {p1, v0}, Ljava/net/HttpURLConnection;->setRequestMethod(Ljava/lang/String;)V

    return-object p1
.end method

.method private a(Ljava/net/HttpURLConnection;[B)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 86
    invoke-static {}, Lcom/beizi/ad/internal/h;->a()Lcom/beizi/ad/internal/h;

    move-result-object v0

    iget-object v0, v0, Lcom/beizi/ad/internal/h;->e:Ljava/lang/String;

    const-string v1, "User-Agent"

    invoke-virtual {p1, v1, v0}, Ljava/net/URLConnection;->setRequestProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 87
    const-string v0, "Content-Type"

    const-string v1, "application/json"

    invoke-virtual {p1, v0, v1}, Ljava/net/URLConnection;->setRequestProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 88
    const-string v0, "Accept"

    invoke-virtual {p1, v0, v1}, Ljava/net/URLConnection;->setRequestProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 89
    invoke-static {}, Lcom/beizi/ad/internal/h/y;->a()Ljava/lang/String;

    move-result-object v0

    .line 90
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_0

    .line 91
    const-string v1, "Cookie"

    invoke-virtual {p1, v1, v0}, Ljava/net/URLConnection;->setRequestProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 92
    :cond_0
    array-length v0, p2

    invoke-static {v0}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v0

    const-string v1, "Connect-Length"

    invoke-virtual {p1, v1, v0}, Ljava/net/URLConnection;->setRequestProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 93
    array-length v0, p2

    invoke-virtual {p1, v0}, Ljava/net/HttpURLConnection;->setFixedLengthStreamingMode(I)V

    .line 94
    invoke-virtual {p1}, Ljava/net/URLConnection;->getOutputStream()Ljava/io/OutputStream;

    move-result-object p1

    .line 95
    invoke-virtual {p1, p2}, Ljava/io/OutputStream;->write([B)V

    .line 96
    invoke-virtual {p1}, Ljava/io/OutputStream;->flush()V

    .line 97
    invoke-virtual {p1}, Ljava/io/OutputStream;->close()V

    return-void
.end method

.method private a(I)Z
    .locals 1

    .line 1
    const/16 v0, 0xc8

    if-eq p1, v0, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 p1, 0x1

    return p1
.end method

.method private b(Ljava/lang/String;Ljava/lang/String;Z)[B
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lcom/beizi/ad/v2/d/b;->a(Ljava/lang/String;Ljava/lang/String;Z)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    if-eqz p2, :cond_0

    .line 10
    .line 11
    const/4 p1, 0x0

    .line 12
    return-object p1

    .line 13
    :cond_0
    invoke-virtual {p1}, Ljava/lang/String;->getBytes()[B

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method


# virtual methods
.method public a(Ljava/lang/String;Ljava/lang/String;Z)Ljava/lang/String;
    .locals 8

    .line 20
    invoke-static {}, Lcom/beizi/ad/internal/h;->a()Lcom/beizi/ad/internal/h;

    move-result-object v0

    invoke-virtual {v0}, Lcom/beizi/ad/internal/h;->e()Landroid/content/Context;

    move-result-object v0

    .line 21
    invoke-static {}, Lcom/beizi/ad/internal/h;->a()Lcom/beizi/ad/internal/h;

    move-result-object v1

    .line 22
    invoke-static {}, Lcom/beizi/ad/internal/h/a;->a()Lcom/beizi/ad/internal/h/a;

    move-result-object v2

    .line 23
    new-instance v3, Lcom/beizi/ad/model/d$a$a;

    invoke-direct {v3}, Lcom/beizi/ad/model/d$a$a;-><init>()V

    .line 24
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->d()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->a(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 25
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->e()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->j(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 26
    invoke-static {v0}, Lcom/beizi/ad/lance/a/n;->a(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->l(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 27
    invoke-static {v0}, Lcom/beizi/ad/lance/a/n;->b(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->m(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 28
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->b()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->o(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    const-string v4, ""

    .line 29
    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->b(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    iget-object v4, v2, Lcom/beizi/ad/internal/h/a;->d:Ljava/lang/String;

    .line 30
    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    sget-object v4, Lcom/beizi/ad/model/e$e;->c:Lcom/beizi/ad/model/e$e;

    .line 31
    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->a(Lcom/beizi/ad/model/e$e;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 32
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->n()Lcom/beizi/ad/model/e$b;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->a(Lcom/beizi/ad/model/e$b;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    iget-object v4, v2, Lcom/beizi/ad/internal/h/a;->e:Ljava/lang/String;

    .line 33
    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->d(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    iget-object v4, v2, Lcom/beizi/ad/internal/h/a;->f:Ljava/lang/String;

    .line 34
    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->e(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    iget-object v4, v2, Lcom/beizi/ad/internal/h/a;->g:Ljava/lang/String;

    .line 35
    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->f(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 36
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->g()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->g(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 37
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->h()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->h(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    iget-object v4, v2, Lcom/beizi/ad/internal/h/a;->h:Ljava/lang/String;

    .line 38
    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->i(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 39
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->f()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->k(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 40
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->k()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->p(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 41
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->m()Z

    move-result v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->a(Z)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 42
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->i()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->q(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 43
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->j()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->r(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 44
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->l()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$a$a;->s(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v3

    .line 45
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/a;->c()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Lcom/beizi/ad/model/d$a$a;->t(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 46
    invoke-static {}, Lcom/beizi/ad/internal/h;->a()Lcom/beizi/ad/internal/h;

    move-result-object v3

    invoke-virtual {v3}, Lcom/beizi/ad/internal/h;->o()I

    move-result v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->a(I)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 47
    invoke-virtual {v2}, Lcom/beizi/ad/model/d$a$a;->a()Lcom/beizi/ad/model/d$a;

    move-result-object v2

    .line 48
    invoke-static {}, Lcom/beizi/ad/internal/h/s;->a()Lcom/beizi/ad/internal/h/s;

    move-result-object v3

    .line 49
    invoke-virtual {v3}, Lcom/beizi/ad/internal/h/s;->d()V

    .line 50
    new-instance v4, Lcom/beizi/ad/model/d$c$a;

    invoke-direct {v4}, Lcom/beizi/ad/model/d$c$a;-><init>()V

    .line 51
    invoke-virtual {v3}, Lcom/beizi/ad/internal/h/s;->b()Lcom/beizi/ad/model/e$d;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/d$c$a;->a(Lcom/beizi/ad/model/e$d;)Lcom/beizi/ad/model/d$c$a;

    .line 52
    invoke-virtual {v3}, Lcom/beizi/ad/internal/h/s;->c()Lcom/beizi/ad/model/e$c;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/d$c$a;->a(Lcom/beizi/ad/model/e$c;)Lcom/beizi/ad/model/d$c$a;

    .line 53
    iget-object v5, v3, Lcom/beizi/ad/internal/h/s;->b:Ljava/lang/String;

    invoke-static {v5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v5

    if-nez v5, :cond_0

    iget-object v5, v3, Lcom/beizi/ad/internal/h/s;->c:Ljava/lang/String;

    invoke-static {v5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v5

    if-nez v5, :cond_0

    .line 54
    new-instance v5, Lcom/beizi/ad/model/d$b$a;

    invoke-direct {v5}, Lcom/beizi/ad/model/d$b$a;-><init>()V

    iget-object v6, v3, Lcom/beizi/ad/internal/h/s;->c:Ljava/lang/String;

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/d$b$a;->b(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;

    move-result-object v5

    iget-object v6, v3, Lcom/beizi/ad/internal/h/s;->b:Ljava/lang/String;

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/d$b$a;->a(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;

    move-result-object v5

    iget-object v6, v3, Lcom/beizi/ad/internal/h/s;->d:Ljava/lang/String;

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/d$b$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;

    move-result-object v5

    iget-wide v6, v3, Lcom/beizi/ad/internal/h/s;->e:J

    invoke-virtual {v5, v6, v7}, Lcom/beizi/ad/model/d$b$a;->a(J)Lcom/beizi/ad/model/d$b$a;

    move-result-object v3

    invoke-virtual {v3}, Lcom/beizi/ad/model/d$b$a;->a()Lcom/beizi/ad/model/d$b;

    move-result-object v3

    invoke-virtual {v4, v3}, Lcom/beizi/ad/model/d$c$a;->a(Lcom/beizi/ad/model/d$b;)Lcom/beizi/ad/model/d$c$a;

    .line 55
    :cond_0
    new-instance v3, Lcom/beizi/ad/model/a$b$a;

    invoke-direct {v3}, Lcom/beizi/ad/model/a$b$a;-><init>()V

    const-string v5, "5.2.1.3"

    .line 56
    invoke-virtual {v3, v5}, Lcom/beizi/ad/model/a$b$a;->a(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v3

    sget-object v5, Lcom/beizi/ad/model/e$i;->b:Lcom/beizi/ad/model/e$i;

    .line 57
    invoke-virtual {v3, v5}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/e$i;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v3

    .line 58
    invoke-static {v0}, Lcom/beizi/ad/internal/h/t;->c(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v5}, Lcom/beizi/ad/model/a$b$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v3

    sget-object v5, Lcom/beizi/ad/model/e$g;->b:Lcom/beizi/ad/model/e$g;

    .line 59
    invoke-virtual {v3, v5}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/e$g;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v3

    .line 60
    invoke-static {}, Lcom/beizi/ad/lance/a/q;->c()J

    move-result-wide v5

    invoke-virtual {v3, v5, v6}, Lcom/beizi/ad/model/a$b$a;->a(J)Lcom/beizi/ad/model/a$b$a;

    move-result-object v3

    .line 61
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h;->d()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v3, v1}, Lcom/beizi/ad/model/a$b$a;->b(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 62
    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/d$a;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 63
    invoke-virtual {v4}, Lcom/beizi/ad/model/d$c$a;->a()Lcom/beizi/ad/model/d$c;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/d$c;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 64
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->d(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/a$b$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 65
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->c(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/a$b$a;->d(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 66
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->a(Landroid/content/Context;)J

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Lcom/beizi/ad/model/a$b$a;->b(J)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 67
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->b(Landroid/content/Context;)J

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Lcom/beizi/ad/model/a$b$a;->c(J)Lcom/beizi/ad/model/a$b$a;

    move-result-object v0

    .line 68
    new-instance v1, Lcom/beizi/ad/model/a$a$a;

    invoke-direct {v1}, Lcom/beizi/ad/model/a$a$a;-><init>()V

    .line 69
    invoke-virtual {v1, p1}, Lcom/beizi/ad/model/a$a$a;->a(Ljava/lang/String;)Lcom/beizi/ad/model/a$a$a;

    move-result-object p1

    .line 70
    invoke-virtual {p1, p2}, Lcom/beizi/ad/model/a$a$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/a$a$a;

    move-result-object p1

    .line 71
    invoke-virtual {p1, p3}, Lcom/beizi/ad/model/a$a$a;->a(I)Lcom/beizi/ad/model/a$a$a;

    move-result-object p1

    .line 72
    invoke-virtual {p1}, Lcom/beizi/ad/model/a$a$a;->a()Lcom/beizi/ad/model/a$a;

    move-result-object p1

    .line 73
    invoke-virtual {v0, p1}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/a$a;)V

    .line 74
    invoke-virtual {v0}, Lcom/beizi/ad/model/a$b$a;->a()Lcom/beizi/ad/model/a$b;

    move-result-object p1

    .line 75
    invoke-static {}, Lcom/beizi/ad/lance/a/l;->a()Ljava/lang/String;

    move-result-object p2

    .line 76
    invoke-virtual {p1}, Lcom/beizi/ad/model/a$b;->toString()Ljava/lang/String;

    move-result-object p3

    .line 77
    invoke-static {p2, p3}, Lcom/beizi/ad/lance/a/a;->a(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 78
    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "sdkRequest:"

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lcom/beizi/ad/model/a$b;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p3, "lance"

    invoke-static {p3, p1}, Lcom/beizi/ad/lance/a/m;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-object p2
.end method

.method public a(Ljava/lang/String;Z)Ljava/lang/String;
    .locals 7

    .line 98
    invoke-static {}, Lcom/beizi/fusion/c/b;->a()Lcom/beizi/fusion/c/b;

    move-result-object v0

    invoke-virtual {v0}, Lcom/beizi/fusion/c/b;->e()Landroid/content/Context;

    move-result-object v0

    .line 99
    invoke-static {}, Lcom/beizi/ad/internal/h/a;->a()Lcom/beizi/ad/internal/h/a;

    move-result-object v1

    .line 100
    new-instance v2, Lcom/beizi/ad/model/d$a$a;

    invoke-direct {v2}, Lcom/beizi/ad/model/d$a$a;-><init>()V

    .line 101
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->d()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->a(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 102
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->e()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->j(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 103
    invoke-static {v0}, Lcom/beizi/ad/lance/a/n;->a(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->l(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 104
    invoke-static {v0}, Lcom/beizi/ad/lance/a/n;->b(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->m(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 105
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->b()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->o(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    const-string v3, ""

    .line 106
    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->b(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    iget-object v3, v1, Lcom/beizi/ad/internal/h/a;->d:Ljava/lang/String;

    .line 107
    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    sget-object v3, Lcom/beizi/ad/model/e$e;->c:Lcom/beizi/ad/model/e$e;

    .line 108
    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->a(Lcom/beizi/ad/model/e$e;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 109
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->n()Lcom/beizi/ad/model/e$b;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->a(Lcom/beizi/ad/model/e$b;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    iget-object v3, v1, Lcom/beizi/ad/internal/h/a;->e:Ljava/lang/String;

    .line 110
    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->d(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    iget-object v3, v1, Lcom/beizi/ad/internal/h/a;->f:Ljava/lang/String;

    .line 111
    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->e(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    iget-object v3, v1, Lcom/beizi/ad/internal/h/a;->g:Ljava/lang/String;

    .line 112
    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->f(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 113
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->g()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->g(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 114
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->h()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->h(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    iget-object v3, v1, Lcom/beizi/ad/internal/h/a;->h:Ljava/lang/String;

    .line 115
    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->i(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 116
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->f()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->k(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 117
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->k()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->p(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 118
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->m()Z

    move-result v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->a(Z)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 119
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->i()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->q(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 120
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->j()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->r(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 121
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->l()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/beizi/ad/model/d$a$a;->s(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v2

    .line 122
    invoke-virtual {v1}, Lcom/beizi/ad/internal/h/a;->c()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Lcom/beizi/ad/model/d$a$a;->t(Ljava/lang/String;)Lcom/beizi/ad/model/d$a$a;

    move-result-object v1

    .line 123
    invoke-static {}, Lcom/beizi/ad/internal/h;->a()Lcom/beizi/ad/internal/h;

    move-result-object v2

    invoke-virtual {v2}, Lcom/beizi/ad/internal/h;->o()I

    move-result v2

    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/d$a$a;->a(I)Lcom/beizi/ad/model/d$a$a;

    move-result-object v1

    .line 124
    invoke-virtual {v1}, Lcom/beizi/ad/model/d$a$a;->a()Lcom/beizi/ad/model/d$a;

    move-result-object v1

    .line 125
    invoke-static {}, Lcom/beizi/ad/internal/h/s;->a()Lcom/beizi/ad/internal/h/s;

    move-result-object v2

    .line 126
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/s;->d()V

    .line 127
    new-instance v3, Lcom/beizi/ad/model/d$c$a;

    invoke-direct {v3}, Lcom/beizi/ad/model/d$c$a;-><init>()V

    .line 128
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/s;->b()Lcom/beizi/ad/model/e$d;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$c$a;->a(Lcom/beizi/ad/model/e$d;)Lcom/beizi/ad/model/d$c$a;

    .line 129
    invoke-virtual {v2}, Lcom/beizi/ad/internal/h/s;->c()Lcom/beizi/ad/model/e$c;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/beizi/ad/model/d$c$a;->a(Lcom/beizi/ad/model/e$c;)Lcom/beizi/ad/model/d$c$a;

    .line 130
    iget-object v4, v2, Lcom/beizi/ad/internal/h/s;->b:Ljava/lang/String;

    invoke-static {v4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v4

    if-nez v4, :cond_0

    iget-object v4, v2, Lcom/beizi/ad/internal/h/s;->c:Ljava/lang/String;

    invoke-static {v4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v4

    if-nez v4, :cond_0

    .line 131
    new-instance v4, Lcom/beizi/ad/model/d$b$a;

    invoke-direct {v4}, Lcom/beizi/ad/model/d$b$a;-><init>()V

    iget-object v5, v2, Lcom/beizi/ad/internal/h/s;->c:Ljava/lang/String;

    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/d$b$a;->b(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;

    move-result-object v4

    iget-object v5, v2, Lcom/beizi/ad/internal/h/s;->b:Ljava/lang/String;

    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/d$b$a;->a(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;

    move-result-object v4

    iget-object v5, v2, Lcom/beizi/ad/internal/h/s;->d:Ljava/lang/String;

    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/d$b$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;

    move-result-object v4

    iget-wide v5, v2, Lcom/beizi/ad/internal/h/s;->e:J

    invoke-virtual {v4, v5, v6}, Lcom/beizi/ad/model/d$b$a;->a(J)Lcom/beizi/ad/model/d$b$a;

    move-result-object v2

    invoke-virtual {v2}, Lcom/beizi/ad/model/d$b$a;->a()Lcom/beizi/ad/model/d$b;

    move-result-object v2

    invoke-virtual {v3, v2}, Lcom/beizi/ad/model/d$c$a;->a(Lcom/beizi/ad/model/d$b;)Lcom/beizi/ad/model/d$c$a;

    .line 132
    :cond_0
    new-instance v2, Lcom/beizi/ad/model/a$b$a;

    invoke-direct {v2}, Lcom/beizi/ad/model/a$b$a;-><init>()V

    const-string v4, "5.2.1.3"

    .line 133
    invoke-virtual {v2, v4}, Lcom/beizi/ad/model/a$b$a;->a(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v2

    sget-object v4, Lcom/beizi/ad/model/e$i;->b:Lcom/beizi/ad/model/e$i;

    .line 134
    invoke-virtual {v2, v4}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/e$i;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v2

    .line 135
    invoke-static {v0}, Lcom/beizi/ad/internal/h/t;->c(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Lcom/beizi/ad/model/a$b$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v2

    sget-object v4, Lcom/beizi/ad/model/e$g;->b:Lcom/beizi/ad/model/e$g;

    .line 136
    invoke-virtual {v2, v4}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/e$g;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v2

    .line 137
    invoke-static {}, Lcom/beizi/ad/lance/a/q;->c()J

    move-result-wide v4

    invoke-virtual {v2, v4, v5}, Lcom/beizi/ad/model/a$b$a;->a(J)Lcom/beizi/ad/model/a$b$a;

    move-result-object v2

    .line 138
    invoke-virtual {v2, v1}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/d$a;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 139
    invoke-virtual {v3}, Lcom/beizi/ad/model/d$c$a;->a()Lcom/beizi/ad/model/d$c;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/d$c;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 140
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->d(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/a$b$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 141
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->c(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/beizi/ad/model/a$b$a;->d(Ljava/lang/String;)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 142
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->a(Landroid/content/Context;)J

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Lcom/beizi/ad/model/a$b$a;->b(J)Lcom/beizi/ad/model/a$b$a;

    move-result-object v1

    .line 143
    invoke-static {v0}, Lcom/beizi/ad/lance/a/q;->b(Landroid/content/Context;)J

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Lcom/beizi/ad/model/a$b$a;->c(J)Lcom/beizi/ad/model/a$b$a;

    move-result-object v0

    .line 144
    new-instance v1, Lcom/beizi/ad/model/a$a$a;

    invoke-direct {v1}, Lcom/beizi/ad/model/a$a$a;-><init>()V

    .line 145
    invoke-virtual {v1, p1}, Lcom/beizi/ad/model/a$a$a;->c(Ljava/lang/String;)Lcom/beizi/ad/model/a$a$a;

    move-result-object p1

    .line 146
    invoke-virtual {p1, p2}, Lcom/beizi/ad/model/a$a$a;->a(I)Lcom/beizi/ad/model/a$a$a;

    move-result-object p1

    .line 147
    invoke-virtual {p1}, Lcom/beizi/ad/model/a$a$a;->a()Lcom/beizi/ad/model/a$a;

    move-result-object p1

    .line 148
    invoke-virtual {v0, p1}, Lcom/beizi/ad/model/a$b$a;->a(Lcom/beizi/ad/model/a$a;)V

    .line 149
    invoke-virtual {v0}, Lcom/beizi/ad/model/a$b$a;->a()Lcom/beizi/ad/model/a$b;

    move-result-object p1

    .line 150
    invoke-static {}, Lcom/beizi/ad/lance/a/l;->a()Ljava/lang/String;

    move-result-object p2

    .line 151
    invoke-virtual {p1}, Lcom/beizi/ad/model/a$b;->toString()Ljava/lang/String;

    move-result-object v0

    .line 152
    invoke-static {p2, v0}, Lcom/beizi/ad/lance/a/a;->a(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 153
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "sdkRequest:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lcom/beizi/ad/model/a$b;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "lance"

    invoke-static {v0, p1}, Lcom/beizi/ad/lance/a/m;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-object p2
.end method

.method public a(Ljava/lang/String;Ljava/lang/String;ZLcom/beizi/ad/v2/d/a;)V
    .locals 4

    .line 2
    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_4

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_4

    if-nez p4, :cond_0

    goto :goto_1

    .line 3
    :cond_0
    :try_start_0
    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_4

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_4

    .line 4
    invoke-static {}, Lcom/beizi/ad/internal/h;->a()Lcom/beizi/ad/internal/h;

    move-result-object v0

    invoke-virtual {v0}, Lcom/beizi/ad/internal/h;->l()Ljava/lang/String;

    move-result-object v0

    .line 5
    const-string v1, "lance"

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getRequestBaseUrl:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/beizi/ad/lance/a/m;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    new-instance v1, Ljava/net/URL;

    invoke-direct {v1, v0}, Ljava/net/URL;-><init>(Ljava/lang/String;)V

    invoke-direct {p0, v1}, Lcom/beizi/ad/v2/d/b;->a(Ljava/net/URL;)Ljava/net/HttpURLConnection;

    move-result-object v0

    .line 7
    invoke-direct {p0, p1, p2, p3}, Lcom/beizi/ad/v2/d/b;->b(Ljava/lang/String;Ljava/lang/String;Z)[B

    move-result-object p1

    if-nez p1, :cond_1

    const/4 p1, 0x4

    .line 8
    invoke-interface {p4, p1}, Lcom/beizi/ad/v2/d/a;->a(I)V

    return-void

    :catch_0
    move-exception p1

    goto :goto_0

    .line 9
    :cond_1
    invoke-direct {p0, v0, p1}, Lcom/beizi/ad/v2/d/b;->a(Ljava/net/HttpURLConnection;[B)V

    .line 10
    invoke-virtual {v0}, Ljava/net/URLConnection;->connect()V

    .line 11
    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->getResponseCode()I

    move-result p1

    invoke-direct {p0, p1}, Lcom/beizi/ad/v2/d/b;->a(I)Z

    move-result p1

    if-nez p1, :cond_2

    const/4 p1, 0x2

    .line 12
    invoke-interface {p4, p1}, Lcom/beizi/ad/v2/d/a;->a(I)V

    return-void

    .line 13
    :cond_2
    invoke-virtual {v0}, Ljava/net/URLConnection;->getInputStream()Ljava/io/InputStream;

    move-result-object p1

    .line 14
    invoke-static {p1}, Lcom/beizi/ad/model/b$l;->a(Ljava/io/InputStream;)Ljava/lang/String;

    move-result-object p2

    .line 15
    invoke-virtual {p1}, Ljava/io/InputStream;->close()V

    .line 16
    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_3

    const/4 p1, 0x3

    .line 17
    invoke-interface {p4, p1}, Lcom/beizi/ad/v2/d/a;->a(I)V

    return-void

    .line 18
    :cond_3
    invoke-interface {p4, p2}, Lcom/beizi/ad/v2/d/a;->a(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 19
    :goto_0
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_4
    :goto_1
    return-void
.end method
