.class final Ltop/cycdm/cycapp/ui/home/<USER>
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.home.HomeViewModel$initNav$1"
    f = "HomeViewModel.kt"
    i = {
        0x0,
        0x1
    }
    l = {
        0x4f,
        0x51,
        0x5a
    }
    m = "invokeSuspend"
    n = {
        "$this$intent",
        "$this$intent"
    }
    s = {
        "L$0",
        "L$0"
    }
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nHomeViewModel.kt\nKotlin\n*S Kotlin\n*F\n+ 1 HomeViewModel.kt\ntop/cycdm/cycapp/ui/home/<USER>/collections/CollectionsKt___CollectionsKt\n*L\n1#1,276:1\n1193#2,2:277\n1267#2,4:279\n*S KotlinDebug\n*F\n+ 1 HomeViewModel.kt\ntop/cycdm/cycapp/ui/home/<USER>"
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/home/<USER>


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltop/cycdm/cycapp/ui/home/<USER>",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Ljava/util/List;Ljava/util/Map;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;Ljava/util/Map;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h(Ljava/lang/Throwable;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Throwable;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>

    move-result-object p0

    return-object p0
.end method

.method private static final invokeSuspend$lambda$1$lambda$0(Ljava/lang/Throwable;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 20

    .line 1
    invoke-virtual/range {p1 .. p1}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    move-object v1, v0

    .line 6
    check-cast v1, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 7
    .line 8
    new-instance v11, Ltop/cycdm/cycapp/utils/h$a;

    .line 9
    .line 10
    const/4 v0, 0x0

    .line 11
    const/4 v2, 0x2

    .line 12
    move-object/from16 v3, p0

    .line 13
    .line 14
    invoke-direct {v11, v3, v0, v2, v0}, Ltop/cycdm/cycapp/utils/h$a;-><init>(Ljava/lang/Throwable;Ljava/lang/Object;ILkotlin/jvm/internal/n;)V

    .line 15
    .line 16
    .line 17
    const v18, 0xfdff

    .line 18
    .line 19
    .line 20
    const/16 v19, 0x0

    .line 21
    .line 22
    const/4 v2, 0x0

    .line 23
    const/4 v3, 0x0

    .line 24
    const/4 v4, 0x0

    .line 25
    const/4 v5, 0x0

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v9, 0x0

    .line 30
    const/4 v10, 0x0

    .line 31
    const/4 v12, 0x0

    .line 32
    const/4 v13, 0x0

    .line 33
    const/4 v14, 0x0

    .line 34
    const/4 v15, 0x0

    .line 35
    const/16 v16, 0x0

    .line 36
    .line 37
    const/16 v17, 0x0

    .line 38
    .line 39
    invoke-static/range {v1 .. v19}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/Map;Ljava/util/Map;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;Ltop/cycdm/model/z;Ltop/cycdm/model/s;Ltop/cycdm/model/p;Ltop/cycdm/cycapp/ui/weekly/DayOfWeek;Ljava/util/List;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    return-object v0
.end method

.method private static final invokeSuspend$lambda$4$lambda$3(Ljava/util/List;Ljava/util/Map;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 20

    .line 1
    invoke-virtual/range {p2 .. p2}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    move-object v1, v0

    .line 6
    check-cast v1, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 7
    .line 8
    const v18, 0xff3f

    .line 9
    .line 10
    .line 11
    const/16 v19, 0x0

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    const/4 v3, 0x0

    .line 15
    const/4 v4, 0x0

    .line 16
    const/4 v5, 0x0

    .line 17
    const/4 v6, 0x0

    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v10, 0x0

    .line 20
    const/4 v11, 0x0

    .line 21
    const/4 v12, 0x0

    .line 22
    const/4 v13, 0x0

    .line 23
    const/4 v14, 0x0

    .line 24
    const/4 v15, 0x0

    .line 25
    const/16 v16, 0x0

    .line 26
    .line 27
    const/16 v17, 0x0

    .line 28
    .line 29
    move-object/from16 v8, p0

    .line 30
    .line 31
    move-object/from16 v9, p1

    .line 32
    .line 33
    invoke-static/range {v1 .. v19}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/Map;Ljava/util/Map;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;Ltop/cycdm/model/z;Ltop/cycdm/model/s;Ltop/cycdm/model/p;Ltop/cycdm/cycapp/ui/weekly/DayOfWeek;Ljava/util/List;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    return-object v0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    invoke-direct {v0, v1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/home/<USER>

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 8
    .line 9
    const/4 v3, 0x3

    .line 10
    const/4 v4, 0x2

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_4

    .line 13
    .line 14
    if-eq v2, v5, :cond_2

    .line 15
    .line 16
    if-eq v2, v4, :cond_1

    .line 17
    .line 18
    if-ne v2, v3, :cond_0

    .line 19
    .line 20
    invoke-static/range {p1 .. p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    goto/16 :goto_4

    .line 24
    .line 25
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 28
    .line 29
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    throw v1

    .line 33
    :cond_1
    iget-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 34
    .line 35
    iget-object v4, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 36
    .line 37
    check-cast v4, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 38
    .line 39
    invoke-static/range {p1 .. p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    goto :goto_1

    .line 43
    :cond_2
    iget-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 44
    .line 45
    check-cast v2, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 46
    .line 47
    invoke-static/range {p1 .. p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    move-object/from16 v5, p1

    .line 51
    .line 52
    check-cast v5, Lkotlin/Result;

    .line 53
    .line 54
    invoke-virtual {v5}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object v5

    .line 58
    :cond_3
    move-object/from16 v18, v5

    .line 59
    .line 60
    move-object v5, v2

    .line 61
    move-object/from16 v2, v18

    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_4
    invoke-static/range {p1 .. p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    iget-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 68
    .line 69
    check-cast v2, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 70
    .line 71
    iget-object v6, v0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 72
    .line 73
    invoke-static {v6}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/d;

    .line 74
    .line 75
    .line 76
    move-result-object v6

    .line 77
    invoke-interface {v6}, Lg8/d;->a()Lkotlinx/coroutines/flow/d;

    .line 78
    .line 79
    .line 80
    move-result-object v6

    .line 81
    iput-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 82
    .line 83
    iput v5, v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 84
    .line 85
    invoke-static {v6, v0}, Ltop/cycdm/cycapp/ui/common/ExtensionKt;->S(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v5

    .line 89
    if-ne v5, v1, :cond_3

    .line 90
    .line 91
    goto/16 :goto_3

    .line 92
    .line 93
    :goto_0
    invoke-static {v2}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 94
    .line 95
    .line 96
    move-result-object v6

    .line 97
    if-eqz v6, :cond_6

    .line 98
    .line 99
    new-instance v7, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 100
    .line 101
    invoke-direct {v7, v6}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Throwable;)V

    .line 102
    .line 103
    .line 104
    iput-object v5, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 105
    .line 106
    iput-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 107
    .line 108
    iput v4, v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 109
    .line 110
    invoke-static {v5, v7, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v4

    .line 114
    if-ne v4, v1, :cond_5

    .line 115
    .line 116
    goto :goto_3

    .line 117
    :cond_5
    move-object v4, v5

    .line 118
    :goto_1
    move-object v5, v4

    .line 119
    :cond_6
    iget-object v4, v0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 120
    .line 121
    invoke-static {v2}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 122
    .line 123
    .line 124
    move-result v6

    .line 125
    if-eqz v6, :cond_8

    .line 126
    .line 127
    move-object v6, v2

    .line 128
    check-cast v6, Ljava/util/List;

    .line 129
    .line 130
    const/16 v7, 0xa

    .line 131
    .line 132
    invoke-static {v6, v7}, Lkotlin/collections/x;->y(Ljava/lang/Iterable;I)I

    .line 133
    .line 134
    .line 135
    move-result v7

    .line 136
    invoke-static {v7}, Lkotlin/collections/r0;->f(I)I

    .line 137
    .line 138
    .line 139
    move-result v7

    .line 140
    const/16 v8, 0x10

    .line 141
    .line 142
    invoke-static {v7, v8}, Lx5/n;->e(II)I

    .line 143
    .line 144
    .line 145
    move-result v7

    .line 146
    new-instance v8, Ljava/util/LinkedHashMap;

    .line 147
    .line 148
    invoke-direct {v8, v7}, Ljava/util/LinkedHashMap;-><init>(I)V

    .line 149
    .line 150
    .line 151
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 152
    .line 153
    .line 154
    move-result-object v7

    .line 155
    :goto_2
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    .line 156
    .line 157
    .line 158
    move-result v9

    .line 159
    if-eqz v9, :cond_7

    .line 160
    .line 161
    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object v9

    .line 165
    check-cast v9, Ltop/cycdm/model/NavInfoInner;

    .line 166
    .line 167
    invoke-virtual {v9}, Ltop/cycdm/model/NavInfoInner;->b()I

    .line 168
    .line 169
    .line 170
    move-result v9

    .line 171
    invoke-static {v9}, Ln5/a;->e(I)Ljava/lang/Integer;

    .line 172
    .line 173
    .line 174
    move-result-object v9

    .line 175
    new-instance v10, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 176
    .line 177
    const/16 v16, 0x1f

    .line 178
    .line 179
    const/16 v17, 0x0

    .line 180
    .line 181
    const/4 v11, 0x0

    .line 182
    const/4 v12, 0x0

    .line 183
    const/4 v13, 0x0

    .line 184
    const/4 v14, 0x0

    .line 185
    const/4 v15, 0x0

    .line 186
    invoke-direct/range {v10 .. v17}, Ltop/cycdm/cycapp/ui/home/<USER>/jvm/internal/n;)V

    .line 187
    .line 188
    .line 189
    invoke-static {v9, v10}, Lkotlin/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 190
    .line 191
    .line 192
    move-result-object v9

    .line 193
    invoke-virtual {v9}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 194
    .line 195
    .line 196
    move-result-object v10

    .line 197
    invoke-virtual {v9}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 198
    .line 199
    .line 200
    move-result-object v9

    .line 201
    invoke-interface {v8, v10, v9}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 202
    .line 203
    .line 204
    goto :goto_2

    .line 205
    :cond_7
    invoke-static {v4}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 206
    .line 207
    .line 208
    move-result-object v4

    .line 209
    invoke-interface {v4, v8}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 210
    .line 211
    .line 212
    new-instance v4, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 213
    .line 214
    invoke-direct {v4, v6, v8}, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;Ljava/util/Map;)V

    .line 215
    .line 216
    .line 217
    iput-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 218
    .line 219
    const/4 v2, 0x0

    .line 220
    iput-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 221
    .line 222
    iput v3, v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 223
    .line 224
    invoke-static {v5, v4, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 225
    .line 226
    .line 227
    move-result-object v2

    .line 228
    if-ne v2, v1, :cond_8

    .line 229
    .line 230
    :goto_3
    return-object v1

    .line 231
    :cond_8
    :goto_4
    sget-object v1, Lkotlin/t;->a:Lkotlin/t;

    .line 232
    .line 233
    return-object v1
.end method
