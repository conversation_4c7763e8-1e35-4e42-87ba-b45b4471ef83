.class final Lcom/kwad/components/ct/detail/b/c$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/core/i/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avk:Lcom/kwad/components/ct/detail/b/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c$2;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final aT()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$2;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->j(Lcom/kwad/components/ct/detail/b/c;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$2;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->k(Lcom/kwad/components/ct/detail/b/c;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final aU()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$2;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->g(Lcom/kwad/components/ct/detail/b/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
