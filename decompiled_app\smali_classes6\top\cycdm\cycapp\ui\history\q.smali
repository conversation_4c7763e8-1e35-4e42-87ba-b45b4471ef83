.class public final synthetic Ltop/cycdm/cycapp/ui/history/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/paging/compose/LazyPagingItems;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(Landroidx/paging/compose/LazyPagingItems;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/q;->a:Landroidx/paging/compose/LazyPagingItems;

    iput p2, p0, Ltop/cycdm/cycapp/ui/history/q;->b:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/q;->a:Landroidx/paging/compose/LazyPagingItems;

    iget v1, p0, Ltop/cycdm/cycapp/ui/history/q;->b:I

    check-cast p1, Landroidx/compose/runtime/Composer;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    invoke-static {v0, v1, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->j(Landroidx/paging/compose/LazyPagingItems;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p1

    return-object p1
.end method
