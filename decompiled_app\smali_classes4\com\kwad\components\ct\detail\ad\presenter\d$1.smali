.class final Lcom/kwad/components/ct/detail/ad/presenter/d$1;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amz:Lcom/kwad/components/ct/detail/ad/presenter/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$1;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$1;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/d;->b(Lcom/kwad/components/ct/detail/ad/presenter/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/d$1;->amz:Lcom/kwad/components/ct/detail/ad/presenter/d;

    .line 8
    .line 9
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/d;->a(Lcom/kwad/components/ct/detail/ad/presenter/d;)Ljava/lang/Runnable;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v0, v1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 14
    .line 15
    .line 16
    return-void
.end method
