.class public Lcom/beizi/ad/model/b$l;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/beizi/ad/model/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "l"
.end annotation


# instance fields
.field private a:I

.field private b:Ljava/lang/String;

.field private c:Ljava/lang/String;

.field private d:J

.field private e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$m;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a([B)Lcom/beizi/ad/model/b$l;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/json/JSONException;
        }
    .end annotation

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return-object v0

    .line 6
    :cond_0
    :try_start_0
    new-instance v1, Ljava/lang/String;

    const-string v2, "UTF-8"

    invoke-direct {v1, p0, v2}, Ljava/lang/String;-><init>([BLjava/lang/String;)V

    invoke-static {v1}, Lcom/beizi/ad/model/b$l;->c(Ljava/lang/String;)Lcom/beizi/ad/model/b$l;

    move-result-object p0
    :try_end_0
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 7
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    return-object v0
.end method

.method public static a(Ljava/io/InputStream;)Ljava/lang/String;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 8
    new-instance v0, Ljava/io/ByteArrayOutputStream;

    invoke-direct {v0}, Ljava/io/ByteArrayOutputStream;-><init>()V

    const/16 v1, 0x400

    .line 9
    new-array v1, v1, [B

    .line 10
    :goto_0
    invoke-virtual {p0, v1}, Ljava/io/InputStream;->read([B)I

    move-result v2

    const/4 v3, -0x1

    if-eq v2, v3, :cond_0

    const/4 v3, 0x0

    .line 11
    invoke-virtual {v0, v1, v3, v2}, Ljava/io/ByteArrayOutputStream;->write([BII)V

    goto :goto_0

    .line 12
    :cond_0
    const-string p0, "UTF-8"

    invoke-virtual {v0, p0}, Ljava/io/ByteArrayOutputStream;->toString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static a(Lorg/json/JSONArray;)Ljava/util/ArrayList;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/json/JSONArray;",
            ")",
            "Ljava/util/ArrayList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/json/JSONException;
        }
    .end annotation

    .line 13
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 14
    invoke-static {p0}, Lcom/beizi/ad/model/b$l;->b(Lorg/json/JSONArray;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    .line 15
    :goto_0
    invoke-virtual {p0}, Lorg/json/JSONArray;->length()I

    move-result v2

    if-ge v1, v2, :cond_0

    .line 16
    invoke-virtual {p0, v1}, Lorg/json/JSONArray;->getString(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static b(Lorg/json/JSONArray;)Z
    .locals 0

    if-eqz p0, :cond_0

    .line 3
    invoke-virtual {p0}, Lorg/json/JSONArray;->length()I

    move-result p0

    if-lez p0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method

.method public static c(Ljava/lang/String;)Lcom/beizi/ad/model/b$l;
    .locals 39
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/json/JSONException;
        }
    .end annotation

    .line 2
    const-string v1, "customData"

    const-string v2, "winPriceMax"

    const-string v3, "winPriceMin"

    const-string v4, "isLastLook"

    const-string v5, "auctionType"

    const-string v6, "effectRate"

    const-string v7, "raiseSortPrice"

    const-string v8, "auctionStrategy"

    const-string v9, "expireSec"

    const-string v10, "rewardedVideo"

    const-string v11, "angle"

    const-string v12, "maxAcc"

    const-string v13, "forceUnreal"

    const-string v14, "sensor"

    const-string v15, "isCloseConfirm"

    move-object/from16 v16, v1

    const-string v1, "canJumpStore"

    move-object/from16 v17, v2

    const-string v2, "ext"

    move-object/from16 v18, v3

    const-string v3, "secondPrice"

    invoke-static {}, Lcom/beizi/ad/lance/a/l;->a()Ljava/lang/String;

    move-result-object v0

    move-object/from16 v19, v4

    move-object/from16 v4, p0

    invoke-static {v0, v4}, Lcom/beizi/ad/lance/a/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 3
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v20, v5

    const-string v5, "decryptStr = "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const-string v5, "ServerResponse"

    invoke-static {v5, v4}, Lcom/beizi/ad/lance/a/m;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 4
    new-instance v4, Lorg/json/JSONObject;

    invoke-direct {v4, v0}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    move-object/from16 p0, v5

    .line 5
    new-instance v5, Lcom/beizi/ad/model/b$l;

    invoke-direct {v5}, Lcom/beizi/ad/model/b$l;-><init>()V

    .line 6
    :try_start_0
    const-string v0, "errcode"

    invoke-virtual {v4, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$l;->a(Ljava/lang/String;)V

    .line 7
    const-string v0, "errmsg"

    invoke-virtual {v4, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$l;->b(Ljava/lang/String;)V

    .line 8
    const-string v0, "status"

    invoke-virtual {v4, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$l;->a(I)V

    .line 9
    const-string v0, "ts"

    move-object/from16 v21, v6

    move-object/from16 v22, v7

    invoke-virtual {v4, v0}, Lorg/json/JSONObject;->optLong(Ljava/lang/String;)J

    move-result-wide v6

    invoke-virtual {v5, v6, v7}, Lcom/beizi/ad/model/b$l;->a(J)V

    .line 10
    const-string v0, "spaceInfo"

    invoke-virtual {v4, v0}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v4

    .line 11
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 12
    invoke-static {v4}, Lcom/beizi/ad/model/b$l;->b(Lorg/json/JSONArray;)Z

    move-result v0

    if-eqz v0, :cond_26

    const/4 v7, 0x0

    .line 13
    :goto_0
    invoke-virtual {v4}, Lorg/json/JSONArray;->length()I

    move-result v0
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_11

    if-ge v7, v0, :cond_25

    move-object/from16 v23, v5

    .line 14
    :try_start_1
    new-instance v5, Lcom/beizi/ad/model/b$m;

    invoke-direct {v5}, Lcom/beizi/ad/model/b$m;-><init>()V

    move-object/from16 v24, v6

    .line 15
    invoke-virtual {v4, v7}, Lorg/json/JSONArray;->optJSONObject(I)Lorg/json/JSONObject;

    move-result-object v6

    if-eqz v6, :cond_24

    .line 16
    const-string v0, "spaceID"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->a(Ljava/lang/String;)V

    .line 17
    const-string v0, "spaceParam"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->b(Ljava/lang/String;)V

    .line 18
    const-string v0, "adpType"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-static {v0}, Lcom/beizi/ad/model/e$a;->a(I)Lcom/beizi/ad/model/e$a;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->a(Lcom/beizi/ad/model/e$a;)V

    .line 19
    const-string v0, "refreshInterval"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->a(I)V

    .line 20
    const-string v0, "screenDirection"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-static {v0}, Lcom/beizi/ad/model/e$h;->a(I)Lcom/beizi/ad/model/e$h;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->a(Lcom/beizi/ad/model/e$h;)V

    .line 21
    const-string v0, "width"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->c(Ljava/lang/String;)V

    .line 22
    const-string v0, "height"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->d(Ljava/lang/String;)V

    .line 23
    const-string v0, "adpPosition"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    move-object/from16 v25, v4

    .line 24
    new-instance v4, Lcom/beizi/ad/model/b$h;

    invoke-direct {v4}, Lcom/beizi/ad/model/b$h;-><init>()V

    move/from16 v26, v7

    .line 25
    const-string v7, "x"

    invoke-virtual {v0, v7}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v4, v7}, Lcom/beizi/ad/model/b$h;->a(Ljava/lang/String;)V

    .line 26
    const-string v7, "y"

    invoke-virtual {v0, v7}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lcom/beizi/ad/model/b$h;->b(Ljava/lang/String;)V

    .line 27
    invoke-virtual {v5, v4}, Lcom/beizi/ad/model/b$m;->a(Lcom/beizi/ad/model/b$h;)V

    .line 28
    const-string v0, "autoClose"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->a(Z)V

    .line 29
    const-string v0, "maxTime"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->b(I)V

    .line 30
    const-string v0, "manualClosable"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->b(Z)V

    .line 31
    const-string v0, "minTime"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->c(I)V

    .line 32
    const-string v0, "wifiPreload"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->c(Z)V

    .line 33
    const-string v0, "mute"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->d(Z)V

    .line 34
    const-string v0, "fullScreen"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->e(Z)V

    .line 35
    const-string v0, "autoPlay"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->f(Z)V

    .line 36
    const-string v0, "orgID"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->d(I)V

    .line 37
    const-string v0, "contentType"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->e(I)V

    .line 38
    const-string v0, "appID"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$m;->e(Ljava/lang/String;)V

    .line 39
    const-string v0, "adResponse"

    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v4

    .line 40
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    .line 41
    invoke-static {v4}, Lcom/beizi/ad/model/b$l;->b(Lorg/json/JSONArray;)Z

    move-result v0

    if-eqz v0, :cond_1b

    move-object/from16 v27, v6

    const/4 v6, 0x0

    .line 42
    :goto_1
    invoke-virtual {v4}, Lorg/json/JSONArray;->length()I

    move-result v0

    if-ge v6, v0, :cond_1a

    move-object/from16 v28, v8

    .line 43
    invoke-virtual {v4, v6}, Lorg/json/JSONArray;->optJSONObject(I)Lorg/json/JSONObject;

    move-result-object v8

    if-eqz v8, :cond_19

    move-object/from16 v29, v4

    .line 44
    new-instance v4, Lcom/beizi/ad/model/b$d;

    invoke-direct {v4}, Lcom/beizi/ad/model/b$d;-><init>()V

    .line 45
    const-string v0, "extInfo"

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lcom/beizi/ad/model/b$d;->a(Ljava/lang/String;)V

    .line 46
    const-string v0, "adid"

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lcom/beizi/ad/model/b$d;->b(Ljava/lang/String;)V

    .line 47
    const-string v0, "contentInfo"

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v0

    move/from16 v30, v6

    .line 48
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 49
    invoke-static {v0}, Lcom/beizi/ad/model/b$l;->b(Lorg/json/JSONArray;)Z

    move-result v31

    if-eqz v31, :cond_4

    move-object/from16 v31, v5

    move-object/from16 v32, v7

    const/4 v5, 0x0

    .line 50
    :goto_2
    invoke-virtual {v0}, Lorg/json/JSONArray;->length()I

    move-result v7

    if-ge v5, v7, :cond_3

    .line 51
    invoke-virtual {v0, v5}, Lorg/json/JSONArray;->optJSONObject(I)Lorg/json/JSONObject;

    move-result-object v7

    move-object/from16 v33, v0

    .line 52
    new-instance v0, Lcom/beizi/ad/model/b$a;

    invoke-direct {v0}, Lcom/beizi/ad/model/b$a;-><init>()V

    move/from16 v34, v5

    .line 53
    const-string v5, "template"

    invoke-virtual {v7, v5}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v0, v5}, Lcom/beizi/ad/model/b$a;->a(Ljava/lang/String;)V

    .line 54
    const-string v5, "renderType"

    invoke-virtual {v7, v5}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v5

    invoke-static {v5}, Lcom/beizi/ad/model/e$f;->a(I)Lcom/beizi/ad/model/e$f;

    move-result-object v5

    invoke-virtual {v0, v5}, Lcom/beizi/ad/model/b$a;->a(Lcom/beizi/ad/model/e$f;)V

    .line 55
    const-string v5, "adcontentSlot"

    invoke-virtual {v7, v5}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v5

    .line 56
    invoke-static {v5}, Lcom/beizi/ad/model/b$l;->b(Lorg/json/JSONArray;)Z

    move-result v7

    if-eqz v7, :cond_2

    .line 57
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    move-object/from16 v35, v9

    move-object/from16 v36, v10

    const/4 v9, 0x0

    .line 58
    :goto_3
    invoke-virtual {v5}, Lorg/json/JSONArray;->length()I

    move-result v10

    if-ge v9, v10, :cond_1

    .line 59
    invoke-virtual {v5, v9}, Lorg/json/JSONArray;->optJSONObject(I)Lorg/json/JSONObject;

    move-result-object v10

    if-eqz v10, :cond_0

    move-object/from16 v37, v5

    .line 60
    new-instance v5, Lcom/beizi/ad/model/b$f;

    invoke-direct {v5}, Lcom/beizi/ad/model/b$f;-><init>()V

    move/from16 v38, v9

    .line 61
    const-string v9, "md5"

    invoke-virtual {v10, v9}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v5, v9}, Lcom/beizi/ad/model/b$f;->a(Ljava/lang/String;)V

    .line 62
    const-string v9, "content"

    invoke-virtual {v10, v9}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v5, v9}, Lcom/beizi/ad/model/b$f;->b(Ljava/lang/String;)V

    .line 63
    invoke-virtual {v7, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_4

    :catch_0
    move-exception v0

    move-object/from16 v1, v23

    goto/16 :goto_2c

    :cond_0
    move-object/from16 v37, v5

    move/from16 v38, v9

    :goto_4
    add-int/lit8 v9, v38, 0x1

    move-object/from16 v5, v37

    goto :goto_3

    .line 64
    :cond_1
    invoke-virtual {v0, v7}, Lcom/beizi/ad/model/b$a;->a(Ljava/util/List;)V

    goto :goto_5

    :cond_2
    move-object/from16 v35, v9

    move-object/from16 v36, v10

    .line 65
    :goto_5
    invoke-virtual {v6, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    add-int/lit8 v5, v34, 0x1

    move-object/from16 v0, v33

    move-object/from16 v9, v35

    move-object/from16 v10, v36

    goto/16 :goto_2

    :cond_3
    move-object/from16 v35, v9

    move-object/from16 v36, v10

    .line 66
    invoke-virtual {v4, v6}, Lcom/beizi/ad/model/b$d;->a(Ljava/util/List;)V

    goto :goto_6

    :cond_4
    move-object/from16 v31, v5

    move-object/from16 v32, v7

    move-object/from16 v35, v9

    move-object/from16 v36, v10

    .line 67
    :goto_6
    const-string v0, "adLogo"

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_5

    .line 68
    new-instance v5, Lcom/beizi/ad/model/b$c;

    invoke-direct {v5}, Lcom/beizi/ad/model/b$c;-><init>()V

    .line 69
    const-string v6, "adLabel"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$c;->b(Ljava/lang/String;)V

    .line 70
    const-string v6, "adLabelUrl"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$c;->a(Ljava/lang/String;)V

    .line 71
    const-string v6, "sourceLabel"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$c;->d(Ljava/lang/String;)V

    .line 72
    const-string v6, "sourceUrl"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$c;->c(Ljava/lang/String;)V

    .line 73
    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/b$d;->a(Lcom/beizi/ad/model/b$c;)V

    .line 74
    :cond_5
    const-string v0, "price"

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lcom/beizi/ad/model/b$d;->c(Ljava/lang/String;)V

    .line 75
    invoke-virtual {v8, v3}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 76
    invoke-virtual {v8, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lcom/beizi/ad/model/b$d;->d(Ljava/lang/String;)V

    .line 77
    :cond_6
    new-instance v5, Lcom/beizi/ad/model/b$b;

    invoke-direct {v5}, Lcom/beizi/ad/model/b$b;-><init>()V

    .line 78
    const-string v0, "interactInfo"

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_12

    .line 79
    const-string v6, "thirdpartInfo"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v6

    .line 80
    invoke-static {v6}, Lcom/beizi/ad/model/b$l;->b(Lorg/json/JSONArray;)Z

    move-result v7

    if-eqz v7, :cond_9

    .line 81
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    const/4 v9, 0x0

    .line 82
    :goto_7
    invoke-virtual {v6}, Lorg/json/JSONArray;->length()I

    move-result v10

    if-ge v9, v10, :cond_8

    .line 83
    invoke-virtual {v6, v9}, Lorg/json/JSONArray;->optJSONObject(I)Lorg/json/JSONObject;

    move-result-object v10

    if-eqz v10, :cond_7

    move-object/from16 v33, v3

    .line 84
    new-instance v3, Lcom/beizi/ad/model/b$j;

    invoke-direct {v3}, Lcom/beizi/ad/model/b$j;-><init>()V

    move-object/from16 v34, v6

    .line 85
    const-string v6, "clickUrl"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->b(Ljava/lang/String;)V

    .line 86
    const-string v6, "viewUrl"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->a(Ljava/lang/String;)V

    .line 87
    const-string v6, "convertUrl"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->c(Ljava/lang/String;)V

    .line 88
    const-string v6, "onFinish"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->g(Ljava/lang/String;)V

    .line 89
    const-string v6, "onPause"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->e(Ljava/lang/String;)V

    .line 90
    const-string v6, "onRecover"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->f(Ljava/lang/String;)V

    .line 91
    const-string v6, "onStart"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->d(Ljava/lang/String;)V

    .line 92
    const-string v6, "percent25"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->h(Ljava/lang/String;)V

    .line 93
    const-string v6, "percent50"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->i(Ljava/lang/String;)V

    .line 94
    const-string v6, "percent75"

    invoke-virtual {v10, v6}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$j;->j(Ljava/lang/String;)V

    .line 95
    invoke-virtual {v7, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_8

    :cond_7
    move-object/from16 v33, v3

    move-object/from16 v34, v6

    :goto_8
    add-int/lit8 v9, v9, 0x1

    move-object/from16 v3, v33

    move-object/from16 v6, v34

    goto :goto_7

    :cond_8
    move-object/from16 v33, v3

    .line 96
    invoke-virtual {v5, v7}, Lcom/beizi/ad/model/b$b;->a(Ljava/util/List;)V

    goto :goto_9

    :cond_9
    move-object/from16 v33, v3

    .line 97
    :goto_9
    const-string v3, "apkName"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->c(Ljava/lang/String;)V

    .line 98
    const-string v3, "appDesc"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->f(Ljava/lang/String;)V

    .line 99
    const-string v3, "appVersion"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->h(Ljava/lang/String;)V

    .line 100
    const-string v3, "appDeveloper"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->i(Ljava/lang/String;)V

    .line 101
    const-string v3, "appPermissionsDesc"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->j(Ljava/lang/String;)V

    .line 102
    const-string v3, "appPermissionsUrl"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->k(Ljava/lang/String;)V

    .line 103
    const-string v3, "appPrivacyUrl"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->l(Ljava/lang/String;)V

    .line 104
    const-string v3, "appIconURL"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->m(Ljava/lang/String;)V

    .line 105
    const-string v3, "appintro"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->n(Ljava/lang/String;)V

    .line 106
    const-string v3, "appDownloadURL"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->g(Ljava/lang/String;)V

    .line 107
    const-string v3, "appStoreID"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->e(Ljava/lang/String;)V

    .line 108
    const-string v3, "landingPageUrl"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->a(Ljava/lang/String;)V

    .line 109
    const-string v3, "deeplinkUrl"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->b(Ljava/lang/String;)V

    .line 110
    const-string v3, "interactType"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->a(I)V

    .line 111
    const-string v3, "packageName"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->d(Ljava/lang/String;)V

    .line 112
    const-string v3, "useBuiltInBrow"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->a(Z)V

    .line 113
    const-string v3, "openExternal"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v3

    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->b(I)V

    .line 114
    const-string v3, "followTrackExt"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v3

    .line 115
    new-instance v6, Lcom/beizi/ad/model/b$b$b;

    invoke-direct {v6}, Lcom/beizi/ad/model/b$b$b;-><init>()V

    if-eqz v3, :cond_a

    .line 116
    const-string v7, "open"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 117
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 118
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->a(Ljava/util/List;)V

    .line 119
    const-string v7, "beginDownload"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 120
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 121
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->b(Ljava/util/List;)V

    .line 122
    const-string v7, "download"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 123
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 124
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->c(Ljava/util/List;)V

    .line 125
    const-string v7, "beginInstall"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 126
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 127
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->d(Ljava/util/List;)V

    .line 128
    const-string v7, "install"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 129
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 130
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->e(Ljava/util/List;)V

    .line 131
    const-string v7, "active"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 132
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 133
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->f(Ljava/util/List;)V

    .line 134
    const-string v7, "close"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 135
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 136
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->g(Ljava/util/List;)V

    .line 137
    const-string v7, "showSlide"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 138
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 139
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->h(Ljava/util/List;)V

    .line 140
    const-string v7, "pageClose"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 141
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 142
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->j(Ljava/util/List;)V

    .line 143
    const-string v7, "pageLoad"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 144
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 145
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->i(Ljava/util/List;)V

    .line 146
    const-string v7, "pageAction"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 147
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 148
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->k(Ljava/util/List;)V

    .line 149
    const-string v7, "deepLinkSuccess"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 150
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 151
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->l(Ljava/util/List;)V

    .line 152
    const-string v7, "realDeepLinkSuccess"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 153
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 154
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->m(Ljava/util/List;)V

    .line 155
    const-string v7, "deepLinkFail"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 156
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 157
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->n(Ljava/util/List;)V

    .line 158
    const-string v7, "dpAppInstalled"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 159
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 160
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$b;->o(Ljava/util/List;)V

    .line 161
    const-string v7, "dpAppNotInstalled"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v3

    .line 162
    invoke-static {v3}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v3

    .line 163
    invoke-virtual {v6, v3}, Lcom/beizi/ad/model/b$b$b;->p(Ljava/util/List;)V

    .line 164
    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$b;->a(Lcom/beizi/ad/model/b$b$b;)V

    .line 165
    :cond_a
    const-string v3, "videoTrackExt"

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v3

    .line 166
    new-instance v6, Lcom/beizi/ad/model/b$b$c;

    invoke-direct {v6}, Lcom/beizi/ad/model/b$b$c;-><init>()V

    if-eqz v3, :cond_e

    .line 167
    const-string v7, "start"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 168
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 169
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$c;->a(Ljava/util/List;)V

    .line 170
    const-string v7, "pause"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 171
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 172
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$c;->b(Ljava/util/List;)V

    .line 173
    const-string v7, "continue"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 174
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 175
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$c;->c(Ljava/util/List;)V

    .line 176
    const-string v7, "exit"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 177
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 178
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$c;->d(Ljava/util/List;)V

    .line 179
    const-string v7, "complete"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v7

    .line 180
    invoke-static {v7}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v7

    .line 181
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$c;->e(Ljava/util/List;)V

    .line 182
    const-string v7, "showTrack"

    invoke-virtual {v3, v7}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v3

    .line 183
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    .line 184
    invoke-static {v3}, Lcom/beizi/ad/model/b$l;->b(Lorg/json/JSONArray;)Z

    move-result v9

    if-eqz v9, :cond_d

    const/4 v9, 0x0

    .line 185
    :goto_a
    invoke-virtual {v3}, Lorg/json/JSONArray;->length()I

    move-result v10

    if-ge v9, v10, :cond_c

    .line 186
    invoke-virtual {v3, v9}, Lorg/json/JSONArray;->optJSONObject(I)Lorg/json/JSONObject;

    move-result-object v10

    if-eqz v10, :cond_b

    move-object/from16 v34, v3

    .line 187
    new-instance v3, Lcom/beizi/ad/model/b$b$c$a;

    invoke-direct {v3}, Lcom/beizi/ad/model/b$b$c$a;-><init>()V

    move/from16 v37, v9

    .line 188
    const-string v9, "t"

    invoke-virtual {v10, v9}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v9

    invoke-virtual {v3, v9}, Lcom/beizi/ad/model/b$b$c$a;->a(I)V

    .line 189
    const-string v9, "url"

    invoke-virtual {v10, v9}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object v9

    .line 190
    invoke-static {v9}, Lcom/beizi/ad/model/b$l;->a(Lorg/json/JSONArray;)Ljava/util/ArrayList;

    move-result-object v9

    .line 191
    invoke-virtual {v3, v9}, Lcom/beizi/ad/model/b$b$c$a;->a(Ljava/util/List;)V

    .line 192
    invoke-virtual {v7, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_b

    :cond_b
    move-object/from16 v34, v3

    move/from16 v37, v9

    :goto_b
    add-int/lit8 v9, v37, 0x1

    move-object/from16 v3, v34

    goto :goto_a

    .line 193
    :cond_c
    invoke-virtual {v6, v7}, Lcom/beizi/ad/model/b$b$c;->f(Ljava/util/List;)V

    .line 194
    :cond_d
    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$b;->a(Lcom/beizi/ad/model/b$b$c;)V
    :try_end_1
    .catch Lorg/json/JSONException; {:try_start_1 .. :try_end_1} :catch_0

    .line 195
    :cond_e
    :try_start_2
    invoke-virtual {v0, v2}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_11

    .line 196
    invoke-virtual {v0, v2}, Lorg/json/JSONObject;->getJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    .line 197
    new-instance v3, Lcom/beizi/ad/model/b$b$a;

    invoke-direct {v3}, Lcom/beizi/ad/model/b$b$a;-><init>()V

    if-eqz v0, :cond_11

    .line 198
    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v6

    if-eqz v6, :cond_f

    .line 199
    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v6

    invoke-virtual {v3, v6}, Lcom/beizi/ad/model/b$b$a;->a(I)V

    goto :goto_c

    :catch_1
    move-exception v0

    goto :goto_d

    .line 200
    :cond_f
    :goto_c
    invoke-virtual {v0, v15}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v6

    if-eqz v6, :cond_10

    .line 201
    invoke-virtual {v0, v15}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v3, v0}, Lcom/beizi/ad/model/b$b$a;->b(I)V

    .line 202
    :cond_10
    invoke-virtual {v5, v3}, Lcom/beizi/ad/model/b$b;->a(Lcom/beizi/ad/model/b$b$a;)V
    :try_end_2
    .catch Lorg/json/JSONException; {:try_start_2 .. :try_end_2} :catch_1

    goto :goto_e

    .line 203
    :goto_d
    :try_start_3
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 204
    :cond_11
    :goto_e
    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/b$d;->a(Lcom/beizi/ad/model/b$b;)V
    :try_end_3
    .catch Lorg/json/JSONException; {:try_start_3 .. :try_end_3} :catch_0

    goto :goto_f

    :cond_12
    move-object/from16 v33, v3

    .line 205
    :goto_f
    :try_start_4
    invoke-virtual {v8, v14}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_16

    .line 206
    invoke-virtual {v8, v14}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_16

    .line 207
    new-instance v3, Lcom/beizi/ad/model/b$e;

    invoke-direct {v3}, Lcom/beizi/ad/model/b$e;-><init>()V

    .line 208
    invoke-virtual {v0, v13}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v5

    if-eqz v5, :cond_13

    .line 209
    invoke-virtual {v0, v13}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v5

    invoke-virtual {v3, v5}, Lcom/beizi/ad/model/b$e;->a(Z)V

    goto :goto_10

    :catch_2
    move-exception v0

    move-object/from16 v3, v36

    goto :goto_12

    .line 210
    :cond_13
    :goto_10
    invoke-virtual {v0, v12}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v5

    if-eqz v5, :cond_14

    .line 211
    invoke-virtual {v0, v12}, Lorg/json/JSONObject;->optDouble(Ljava/lang/String;)D

    move-result-wide v5

    invoke-virtual {v3, v5, v6}, Lcom/beizi/ad/model/b$e;->a(D)V

    .line 212
    :cond_14
    invoke-virtual {v0, v11}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v5

    if-eqz v5, :cond_15

    .line 213
    invoke-virtual {v0, v11}, Lorg/json/JSONObject;->optDouble(Ljava/lang/String;)D

    move-result-wide v5

    invoke-virtual {v3, v5, v6}, Lcom/beizi/ad/model/b$e;->b(D)V

    .line 214
    :cond_15
    invoke-virtual {v4, v3}, Lcom/beizi/ad/model/b$d;->a(Lcom/beizi/ad/model/b$e;)V
    :try_end_4
    .catch Ljava/lang/Exception; {:try_start_4 .. :try_end_4} :catch_2

    :cond_16
    move-object/from16 v3, v36

    .line 215
    :try_start_5
    invoke-virtual {v8, v3}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_17

    .line 216
    invoke-virtual {v8, v3}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_17

    .line 217
    new-instance v5, Lcom/beizi/ad/model/b$k;

    invoke-direct {v5}, Lcom/beizi/ad/model/b$k;-><init>()V

    .line 218
    const-string v6, "optimizeType"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v6

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$k;->a(I)V

    .line 219
    const-string v6, "optimizeTime"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v6

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$k;->b(I)V

    .line 220
    const-string v6, "showTime"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v6

    invoke-virtual {v5, v6}, Lcom/beizi/ad/model/b$k;->c(I)V

    .line 221
    const-string v6, "awardTime"

    invoke-virtual {v0, v6}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v5, v0}, Lcom/beizi/ad/model/b$k;->d(I)V

    .line 222
    invoke-virtual {v4, v5}, Lcom/beizi/ad/model/b$d;->a(Lcom/beizi/ad/model/b$k;)V
    :try_end_5
    .catch Ljava/lang/Exception; {:try_start_5 .. :try_end_5} :catch_3

    goto :goto_11

    :catch_3
    move-exception v0

    goto :goto_12

    :cond_17
    :goto_11
    move-object/from16 v5, v35

    goto :goto_13

    .line 223
    :goto_12
    :try_start_6
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    goto :goto_11

    .line 224
    :goto_13
    invoke-virtual {v8, v5}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_18

    .line 225
    invoke-virtual {v8, v5}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v4, v0}, Lcom/beizi/ad/model/b$d;->a(I)V

    .line 226
    :cond_18
    const-string v0, "requestUUID"

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lcom/beizi/ad/model/b$d;->e(Ljava/lang/String;)V

    move-object/from16 v6, v32

    .line 227
    invoke-virtual {v6, v4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_14

    :cond_19
    move-object/from16 v33, v3

    move-object/from16 v29, v4

    move-object/from16 v31, v5

    move/from16 v30, v6

    move-object v6, v7

    move-object v5, v9

    move-object v3, v10

    :goto_14
    add-int/lit8 v0, v30, 0x1

    move-object v10, v3

    move-object v9, v5

    move-object v7, v6

    move-object/from16 v8, v28

    move-object/from16 v4, v29

    move-object/from16 v5, v31

    move-object/from16 v3, v33

    move v6, v0

    goto/16 :goto_1

    :cond_1a
    move-object/from16 v33, v3

    move-object v4, v5

    move-object v6, v7

    move-object/from16 v28, v8

    move-object v5, v9

    move-object v3, v10

    .line 228
    invoke-virtual {v4, v6}, Lcom/beizi/ad/model/b$m;->a(Ljava/util/List;)V
    :try_end_6
    .catch Lorg/json/JSONException; {:try_start_6 .. :try_end_6} :catch_0

    move-object/from16 v7, v27

    move-object/from16 v6, v28

    goto :goto_15

    :cond_1b
    move-object/from16 v33, v3

    move-object v4, v5

    move-object v5, v9

    move-object v3, v10

    move-object v7, v6

    move-object v6, v8

    .line 229
    :goto_15
    :try_start_7
    invoke-virtual {v7, v6}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_23

    .line 230
    invoke-virtual {v7, v6}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_23

    .line 231
    new-instance v7, Lcom/beizi/ad/model/b$i;

    invoke-direct {v7}, Lcom/beizi/ad/model/b$i;-><init>()V
    :try_end_7
    .catch Ljava/lang/Exception; {:try_start_7 .. :try_end_7} :catch_f

    move-object/from16 v8, v22

    .line 232
    :try_start_8
    invoke-virtual {v0, v8}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v9

    if-eqz v9, :cond_1c

    .line 233
    invoke-virtual {v0, v8}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v9

    invoke-virtual {v7, v9}, Lcom/beizi/ad/model/b$i;->a(I)V
    :try_end_8
    .catch Ljava/lang/Exception; {:try_start_8 .. :try_end_8} :catch_4

    :cond_1c
    move-object/from16 v9, v21

    goto :goto_1a

    :catch_4
    move-exception v0

    move-object/from16 v10, v20

    move-object/from16 v9, v21

    :goto_16
    move-object/from16 v20, v1

    :goto_17
    move-object/from16 v1, v17

    move-object/from16 v17, v18

    move-object/from16 v18, v19

    :goto_18
    move-object/from16 v19, v2

    :goto_19
    move-object/from16 v2, v16

    goto/16 :goto_29

    .line 234
    :goto_1a
    :try_start_9
    invoke-virtual {v0, v9}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v10

    if-eqz v10, :cond_1d

    .line 235
    invoke-virtual {v0, v9}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v10

    invoke-virtual {v7, v10}, Lcom/beizi/ad/model/b$i;->b(I)V
    :try_end_9
    .catch Ljava/lang/Exception; {:try_start_9 .. :try_end_9} :catch_5

    :cond_1d
    move-object/from16 v10, v20

    goto :goto_1b

    :catch_5
    move-exception v0

    move-object/from16 v10, v20

    goto :goto_16

    .line 236
    :goto_1b
    :try_start_a
    invoke-virtual {v0, v10}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v20
    :try_end_a
    .catch Ljava/lang/Exception; {:try_start_a .. :try_end_a} :catch_e

    if-eqz v20, :cond_1e

    move-object/from16 v20, v1

    .line 237
    :try_start_b
    invoke-virtual {v0, v10}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v7, v1}, Lcom/beizi/ad/model/b$i;->c(I)V
    :try_end_b
    .catch Ljava/lang/Exception; {:try_start_b .. :try_end_b} :catch_6

    :goto_1c
    move-object/from16 v1, v19

    goto :goto_1d

    :catch_6
    move-exception v0

    goto :goto_17

    :cond_1e
    move-object/from16 v20, v1

    goto :goto_1c

    .line 238
    :goto_1d
    :try_start_c
    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v19
    :try_end_c
    .catch Ljava/lang/Exception; {:try_start_c .. :try_end_c} :catch_d

    if-eqz v19, :cond_1f

    move-object/from16 v19, v2

    .line 239
    :try_start_d
    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v2

    invoke-virtual {v7, v2}, Lcom/beizi/ad/model/b$i;->d(I)V
    :try_end_d
    .catch Ljava/lang/Exception; {:try_start_d .. :try_end_d} :catch_7

    :goto_1e
    move-object/from16 v2, v18

    goto :goto_22

    :catch_7
    move-exception v0

    move-object/from16 v2, v18

    :goto_1f
    move-object/from16 v18, v1

    :goto_20
    move-object/from16 v1, v17

    :goto_21
    move-object/from16 v17, v2

    goto :goto_19

    :cond_1f
    move-object/from16 v19, v2

    goto :goto_1e

    .line 240
    :goto_22
    :try_start_e
    invoke-virtual {v0, v2}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v18
    :try_end_e
    .catch Ljava/lang/Exception; {:try_start_e .. :try_end_e} :catch_c

    if-eqz v18, :cond_20

    move-object/from16 v18, v1

    .line 241
    :try_start_f
    invoke-virtual {v0, v2}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v7, v1}, Lcom/beizi/ad/model/b$i;->a(Ljava/lang/String;)V
    :try_end_f
    .catch Ljava/lang/Exception; {:try_start_f .. :try_end_f} :catch_8

    :goto_23
    move-object/from16 v1, v17

    goto :goto_24

    :catch_8
    move-exception v0

    goto :goto_20

    :cond_20
    move-object/from16 v18, v1

    goto :goto_23

    .line 242
    :goto_24
    :try_start_10
    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v17
    :try_end_10
    .catch Ljava/lang/Exception; {:try_start_10 .. :try_end_10} :catch_b

    if-eqz v17, :cond_21

    move-object/from16 v17, v2

    .line 243
    :try_start_11
    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v7, v2}, Lcom/beizi/ad/model/b$i;->b(Ljava/lang/String;)V
    :try_end_11
    .catch Ljava/lang/Exception; {:try_start_11 .. :try_end_11} :catch_9

    :goto_25
    move-object/from16 v2, v16

    goto :goto_26

    :catch_9
    move-exception v0

    goto :goto_19

    :cond_21
    move-object/from16 v17, v2

    goto :goto_25

    .line 244
    :goto_26
    :try_start_12
    invoke-virtual {v0, v2}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v16

    if-eqz v16, :cond_22

    .line 245
    invoke-virtual {v0, v2}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v7, v0}, Lcom/beizi/ad/model/b$i;->c(Ljava/lang/String;)V

    goto :goto_27

    :catch_a
    move-exception v0

    goto :goto_29

    .line 246
    :cond_22
    :goto_27
    invoke-virtual {v4, v7}, Lcom/beizi/ad/model/b$m;->a(Lcom/beizi/ad/model/b$i;)V
    :try_end_12
    .catch Ljava/lang/Exception; {:try_start_12 .. :try_end_12} :catch_a

    goto :goto_28

    :catch_b
    move-exception v0

    goto :goto_21

    :catch_c
    move-exception v0

    goto :goto_1f

    :catch_d
    move-exception v0

    move-object/from16 v19, v18

    move-object/from16 v18, v1

    move-object/from16 v1, v17

    move-object/from16 v17, v19

    goto/16 :goto_18

    :catch_e
    move-exception v0

    goto/16 :goto_16

    :catch_f
    move-exception v0

    move-object/from16 v10, v20

    move-object/from16 v9, v21

    move-object/from16 v8, v22

    goto/16 :goto_16

    :cond_23
    move-object/from16 v10, v20

    move-object/from16 v9, v21

    move-object/from16 v8, v22

    move-object/from16 v20, v1

    move-object/from16 v1, v17

    move-object/from16 v17, v18

    move-object/from16 v18, v19

    move-object/from16 v19, v2

    move-object/from16 v2, v16

    :goto_28
    move-object/from16 v7, v24

    goto :goto_2a

    .line 247
    :goto_29
    :try_start_13
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    goto :goto_28

    .line 248
    :goto_2a
    invoke-virtual {v7, v4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z
    :try_end_13
    .catch Lorg/json/JSONException; {:try_start_13 .. :try_end_13} :catch_0

    goto :goto_2b

    :cond_24
    move-object/from16 v33, v3

    move-object/from16 v25, v4

    move/from16 v26, v7

    move-object v6, v8

    move-object v5, v9

    move-object v3, v10

    move-object/from16 v10, v20

    move-object/from16 v9, v21

    move-object/from16 v8, v22

    move-object/from16 v7, v24

    move-object/from16 v20, v1

    move-object/from16 v1, v17

    move-object/from16 v17, v18

    move-object/from16 v18, v19

    move-object/from16 v19, v2

    move-object/from16 v2, v16

    :goto_2b
    add-int/lit8 v0, v26, 0x1

    move-object/from16 v16, v2

    move-object/from16 v22, v8

    move-object/from16 v21, v9

    move-object/from16 v2, v19

    move-object/from16 v4, v25

    move-object v9, v5

    move-object v8, v6

    move-object v6, v7

    move-object/from16 v19, v18

    move-object/from16 v5, v23

    move v7, v0

    move-object/from16 v18, v17

    move-object/from16 v17, v1

    move-object/from16 v1, v20

    move-object/from16 v20, v10

    move-object v10, v3

    move-object/from16 v3, v33

    goto/16 :goto_0

    :cond_25
    move-object v1, v5

    move-object v7, v6

    .line 249
    :try_start_14
    invoke-virtual {v1, v7}, Lcom/beizi/ad/model/b$l;->a(Ljava/util/List;)V
    :try_end_14
    .catch Lorg/json/JSONException; {:try_start_14 .. :try_end_14} :catch_10

    goto :goto_2d

    :catch_10
    move-exception v0

    goto :goto_2c

    :catch_11
    move-exception v0

    move-object v1, v5

    goto :goto_2c

    :cond_26
    move-object v1, v5

    goto :goto_2d

    .line 250
    :goto_2c
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "JSONException e = "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    move-object/from16 v2, p0

    invoke-static {v2, v0}, Lcom/beizi/ad/lance/a/m;->c(Ljava/lang/String;Ljava/lang/String;)V

    :goto_2d
    return-object v1
.end method


# virtual methods
.method public a()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$l;->e:Ljava/util/List;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method public a(I)V
    .locals 0

    .line 2
    iput p1, p0, Lcom/beizi/ad/model/b$l;->a:I

    return-void
.end method

.method public a(J)V
    .locals 0

    .line 4
    iput-wide p1, p0, Lcom/beizi/ad/model/b$l;->d:J

    return-void
.end method

.method public a(Ljava/lang/String;)V
    .locals 0

    .line 3
    iput-object p1, p0, Lcom/beizi/ad/model/b$l;->b:Ljava/lang/String;

    return-void
.end method

.method public a(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$m;",
            ">;)V"
        }
    .end annotation

    .line 5
    iput-object p1, p0, Lcom/beizi/ad/model/b$l;->e:Ljava/util/List;

    return-void
.end method

.method public b()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/beizi/ad/model/b$l;->a:I

    return v0
.end method

.method public b(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$l;->c:Ljava/lang/String;

    return-void
.end method

.method public c()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$m;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$l;->e:Ljava/util/List;

    return-object v0
.end method
