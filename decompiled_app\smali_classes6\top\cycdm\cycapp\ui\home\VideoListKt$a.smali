.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;Landroidx/compose/runtime/Composer;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/home/<USER>

.field public final synthetic b:I

.field public final synthetic c:Ltop/cycdm/model/m;

.field public final synthetic d:I


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;I)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    iput p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;

    iput p4, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 4

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 4
    .line 5
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;

    .line 6
    .line 7
    invoke-virtual {v2}, Ltop/cycdm/model/m;->b()Ltop/cycdm/model/NavType;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    iget v3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2, v3}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;I)Lkotlinx/coroutines/w1;

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    .line 4
    sget-object v0, Lkotlin/t;->a:Lkotlin/t;

    .line 5
    .line 6
    return-object v0
.end method
