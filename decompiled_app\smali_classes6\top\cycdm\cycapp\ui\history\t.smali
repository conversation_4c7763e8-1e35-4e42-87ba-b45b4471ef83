.class public final synthetic Ltop/cycdm/cycapp/ui/history/t;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/MutableState;

.field public final synthetic b:Ltop/cycdm/cycapp/ui/history/HistoryVM;


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/t;->a:Landroidx/compose/runtime/MutableState;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/t;->b:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/t;->a:Landroidx/compose/runtime/MutableState;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/t;->b:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    invoke-static {v0, v1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->c(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lkotlin/t;

    move-result-object v0

    return-object v0
.end method
