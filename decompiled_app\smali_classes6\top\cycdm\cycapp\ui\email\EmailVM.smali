.class public final Ltop/cycdm/cycapp/ui/email/EmailVM;
.super Ltop/cycdm/cycapp/BaseVM;
.source "SourceFile"


# annotations
.annotation build Landroidx/compose/runtime/internal/StabilityInferred;
    parameters = 0x0
.end annotation

.annotation build Ldagger/hilt/android/lifecycle/HiltViewModel;
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ltop/cycdm/cycapp/BaseVM<",
        "Ltop/cycdm/cycapp/ui/email/x;",
        "Ltop/cycdm/cycapp/ui/email/b;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0007\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B)\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ(\u0010\u0012\u001a\u00020\u0011*\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u000e2\u0006\u0010\u0010\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0015\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0016\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u001d\u0010\u001b\u001a\u00020\u00172\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u001a\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u001b\u0010\u001cR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0007\u0010\u001dR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u001eR\u0016\u0010\u000b\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u001f\u00a8\u0006 "
    }
    d2 = {
        "Ltop/cycdm/cycapp/ui/email/EmailVM;",
        "Ltop/cycdm/cycapp/BaseVM;",
        "Ltop/cycdm/cycapp/ui/email/x;",
        "Ltop/cycdm/cycapp/ui/email/b;",
        "Landroidx/lifecycle/SavedStateHandle;",
        "savedStateHandle",
        "Lg8/h;",
        "userRep",
        "Lg8/g;",
        "userDataRep",
        "Ltop/cycdm/cycapp/UserData;",
        "userData",
        "<init>",
        "(Landroidx/lifecycle/SavedStateHandle;Lg8/h;Lg8/g;Ltop/cycdm/cycapp/UserData;)V",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "",
        "s",
        "Lkotlin/t;",
        "sendSnackBar",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "createInitialState",
        "()Ltop/cycdm/cycapp/ui/email/x;",
        "email",
        "Lkotlinx/coroutines/w1;",
        "sendEmail",
        "(Ljava/lang/String;)Lkotlinx/coroutines/w1;",
        "code",
        "bindEmail",
        "(Ljava/lang/String;Ljava/lang/String;)Lkotlinx/coroutines/w1;",
        "Lg8/h;",
        "Lg8/g;",
        "Ltop/cycdm/cycapp/UserData;",
        "app_adRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final $stable:I = 0x8


# instance fields
.field private userData:Ltop/cycdm/cycapp/UserData;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final userDataRep:Lg8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final userRep:Lg8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroidx/lifecycle/SavedStateHandle;Lg8/h;Lg8/g;Ltop/cycdm/cycapp/UserData;)V
    .locals 0
    .param p1    # Landroidx/lifecycle/SavedStateHandle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lg8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lg8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ltop/cycdm/cycapp/UserData;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Ljavax/inject/Inject;
    .end annotation

    .line 1
    invoke-direct {p0, p1}, Ltop/cycdm/cycapp/BaseVM;-><init>(Landroidx/lifecycle/SavedStateHandle;)V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM;->userRep:Lg8/h;

    .line 5
    .line 6
    iput-object p3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM;->userDataRep:Lg8/g;

    .line 7
    .line 8
    iput-object p4, p0, Ltop/cycdm/cycapp/ui/email/EmailVM;->userData:Ltop/cycdm/cycapp/UserData;

    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic access$getUserData$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Ltop/cycdm/cycapp/UserData;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/email/EmailVM;->userData:Ltop/cycdm/cycapp/UserData;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/g;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/email/EmailVM;->userDataRep:Lg8/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getUserRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/h;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/email/EmailVM;->userRep:Lg8/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/email/EmailVM;->sendSnackBar(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final sendSnackBar(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/email/b$a;

    .line 2
    .line 3
    invoke-direct {v0, p2}, Ltop/cycdm/cycapp/ui/email/b$a;-><init>(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p1, v0, p3}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->d(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object p2

    .line 14
    if-ne p1, p2, :cond_0

    .line 15
    .line 16
    return-object p1

    .line 17
    :cond_0
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 18
    .line 19
    return-object p1
.end method


# virtual methods
.method public final bindEmail(Ljava/lang/String;Ljava/lang/String;)Lkotlinx/coroutines/w1;
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p2, p0, p1, v1}, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;-><init>(Ljava/lang/String;Ltop/cycdm/cycapp/ui/email/EmailVM;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 p1, 0x1

    .line 8
    const/4 p2, 0x0

    .line 9
    invoke-static {p0, p2, v0, p1, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method

.method public bridge synthetic createInitialState()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->createInitialState()Ltop/cycdm/cycapp/ui/email/x;

    move-result-object v0

    return-object v0
.end method

.method public createInitialState()Ltop/cycdm/cycapp/ui/email/x;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 2
    new-instance v0, Ltop/cycdm/cycapp/ui/email/x;

    const/4 v1, 0x0

    const/4 v2, 0x3

    invoke-direct {v0, v1, v1, v2, v1}, Ltop/cycdm/cycapp/ui/email/x;-><init>(Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;ILkotlin/jvm/internal/n;)V

    return-object v0
.end method

.method public final sendEmail(Ljava/lang/String;)Lkotlinx/coroutines/w1;
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p1, p0, v1}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;-><init>(Ljava/lang/String;Ltop/cycdm/cycapp/ui/email/EmailVM;Lkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 p1, 0x1

    .line 8
    const/4 v2, 0x0

    .line 9
    invoke-static {p0, v2, v0, p1, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method
