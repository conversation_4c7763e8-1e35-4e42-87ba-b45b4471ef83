.class public final synthetic Ltop/cycdm/cycapp/ui/email/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/u;->a:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/u;->a:Lkotlin/jvm/functions/Function1;

    invoke-static {v0}, Ltop/cycdm/cycapp/ui/email/EmailScreenKt$b;->a(<PERSON><PERSON><PERSON>/jvm/functions/Function1;)L<PERSON><PERSON>/t;

    move-result-object v0

    return-object v0
.end method
