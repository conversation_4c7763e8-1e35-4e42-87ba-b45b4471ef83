.class final Lcom/kwad/components/ct/detail/b/d$10;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$10;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayPaused()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayPaused()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$10;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->b(Lcom/kwad/components/ct/detail/b/d;Z)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final onMediaPlayStart()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayStart()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$10;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 5
    .line 6
    const/4 v1, 0x1

    .line 7
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->b(Lcom/kwad/components/ct/detail/b/d;Z)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final onMediaPlaying()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlaying()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$10;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 5
    .line 6
    const/4 v1, 0x1

    .line 7
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->b(Lcom/kwad/components/ct/detail/b/d;Z)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
