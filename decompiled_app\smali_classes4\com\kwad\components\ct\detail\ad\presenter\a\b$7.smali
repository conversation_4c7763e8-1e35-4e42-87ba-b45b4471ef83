.class final Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/a/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayCompleted()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->f(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {v0}, Lcom/kwad/components/ct/detail/d/b;->a(Lcom/kwad/components/ct/detail/c;)Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)Z

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 23
    .line 24
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->b(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)V

    .line 25
    .line 26
    .line 27
    :cond_0
    return-void
.end method

.method public final onMediaPlayProgress(JJ)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->b(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 10
    .line 11
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->c(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/d;->ez(Lcom/kwad/sdk/core/response/model/AdTemplate;)J

    .line 16
    .line 17
    .line 18
    move-result-wide p1

    .line 19
    cmp-long p1, p3, p1

    .line 20
    .line 21
    if-ltz p1, :cond_0

    .line 22
    .line 23
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 24
    .line 25
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->d(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 29
    .line 30
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->e(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    invoke-static {p1, p2}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Landroid/view/View;)V

    .line 35
    .line 36
    .line 37
    :cond_0
    return-void
.end method
