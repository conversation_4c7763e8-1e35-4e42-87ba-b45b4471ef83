.class final Lcom/kwad/components/ct/detail/b/d$6$1;
.super Lcom/kwad/sdk/utils/bh;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/b/d$6;->a(Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avT:Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;

.field final synthetic avU:Lcom/kwad/components/ct/detail/b/d$6;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d$6;Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$6$1;->avU:Lcom/kwad/components/ct/detail/b/d$6;

    .line 2
    .line 3
    iput-object p2, p0, Lcom/kwad/components/ct/detail/b/d$6$1;->avT:Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;

    .line 4
    .line 5
    invoke-direct {p0}, Lcom/kwad/sdk/utils/bh;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final doTask()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$6$1;->avU:Lcom/kwad/components/ct/detail/b/d$6;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/b/d$6;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 4
    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$6$1;->avT:Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;

    .line 6
    .line 7
    iget-object v1, v1, Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;->userProfile:Lcom/kwad/components/ct/profile/home/<USER>/UserProfile;

    .line 8
    .line 9
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->a(Lcom/kwad/components/ct/detail/b/d;Lcom/kwad/components/ct/profile/home/<USER>/UserProfile;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$6$1;->avU:Lcom/kwad/components/ct/detail/b/d$6;

    .line 13
    .line 14
    iget-object v0, v0, Lcom/kwad/components/ct/detail/b/d$6;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->e(Lcom/kwad/components/ct/detail/b/d;Z)Z

    .line 18
    .line 19
    .line 20
    return-void
.end method
