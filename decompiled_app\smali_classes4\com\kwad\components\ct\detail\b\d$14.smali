.class final Lcom/kwad/components/ct/detail/b/d$14;
.super Lcom/kwad/sdk/utils/bh;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/b/d;->k(F)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;

.field final synthetic avV:F


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;F)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$14;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    iput p2, p0, Lcom/kwad/components/ct/detail/b/d$14;->avV:F

    .line 4
    .line 5
    invoke-direct {p0}, Lcom/kwad/sdk/utils/bh;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final doTask()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$14;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    iget v1, p0, Lcom/kwad/components/ct/detail/b/d$14;->avV:F

    .line 4
    .line 5
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->c(Lcom/kwad/components/ct/detail/b/d;F)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
