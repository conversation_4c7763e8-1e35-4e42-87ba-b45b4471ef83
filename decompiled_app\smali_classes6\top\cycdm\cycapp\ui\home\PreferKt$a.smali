.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function3;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/compose/ui/Modifier;Ljava/util/List;Landroidx/compose/runtime/Composer;II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ljava/util/List;

.field public final synthetic b:Landroidx/compose/runtime/MutableState;

.field public final synthetic c:Z

.field public final synthetic d:Landroidx/navigation/NavHostController;

.field public final synthetic e:Landroid/view/View;


# direct methods
.method public constructor <init>(Ljava/util/List;Landroidx/compose/runtime/MutableState;ZLandroidx/navigation/NavHostController;Landroid/view/View;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableState;

    iput-boolean p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iput-object p4, p0, Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;

    iput-object p5, p0, Ltop/cycdm/cycapp/ui/home/<USER>/view/View;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(I)I
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/home/<USER>

    move-result p0

    return p0
.end method

.method public static final c(I)I
    .locals 0

    .line 1
    return p0
.end method


# virtual methods
.method public final b(Landroidx/compose/foundation/layout/BoxWithConstraintsScope;Landroidx/compose/runtime/Composer;I)V
    .locals 20

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v8, p2

    .line 4
    .line 5
    and-int/lit8 v1, p3, 0x6

    .line 6
    .line 7
    move-object/from16 v11, p1

    .line 8
    .line 9
    if-nez v1, :cond_1

    .line 10
    .line 11
    invoke-interface {v8, v11}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    const/4 v1, 0x4

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/4 v1, 0x2

    .line 20
    :goto_0
    or-int v1, p3, v1

    .line 21
    .line 22
    goto :goto_1

    .line 23
    :cond_1
    move/from16 v1, p3

    .line 24
    .line 25
    :goto_1
    and-int/lit8 v2, v1, 0x13

    .line 26
    .line 27
    const/16 v3, 0x12

    .line 28
    .line 29
    if-ne v2, v3, :cond_3

    .line 30
    .line 31
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 32
    .line 33
    .line 34
    move-result v2

    .line 35
    if-nez v2, :cond_2

    .line 36
    .line 37
    goto :goto_2

    .line 38
    :cond_2
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 39
    .line 40
    .line 41
    return-void

    .line 42
    :cond_3
    :goto_2
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    if-eqz v2, :cond_4

    .line 47
    .line 48
    const/4 v2, -0x1

    .line 49
    const-string v3, "top.cycdm.cycapp.ui.home.BannerView.<anonymous> (Prefer.kt:301)"

    .line 50
    .line 51
    const v4, 0x3ec853e

    .line 52
    .line 53
    .line 54
    invoke-static {v4, v1, v2, v3}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 55
    .line 56
    .line 57
    :cond_4
    iget-object v1, v0, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;

    .line 58
    .line 59
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 60
    .line 61
    .line 62
    move-result v1

    .line 63
    if-nez v1, :cond_c

    .line 64
    .line 65
    const v1, -0x5ed91999

    .line 66
    .line 67
    .line 68
    invoke-interface {v8, v1}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 69
    .line 70
    .line 71
    iget-object v1, v0, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;

    .line 72
    .line 73
    invoke-interface {v8, v1}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    move-result v1

    .line 77
    iget-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;

    .line 78
    .line 79
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v3

    .line 83
    if-nez v1, :cond_5

    .line 84
    .line 85
    sget-object v1, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 86
    .line 87
    invoke-virtual {v1}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    if-ne v3, v1, :cond_6

    .line 92
    .line 93
    :cond_5
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 94
    .line 95
    .line 96
    move-result v1

    .line 97
    mul-int/lit16 v1, v1, 0x400

    .line 98
    .line 99
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 100
    .line 101
    .line 102
    move-result-object v3

    .line 103
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 104
    .line 105
    .line 106
    :cond_6
    check-cast v3, Ljava/lang/Number;

    .line 107
    .line 108
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    .line 109
    .line 110
    .line 111
    move-result v1

    .line 112
    invoke-static {}, Ltop/cycdm/cycapp/RouterKt;->k()Z

    .line 113
    .line 114
    .line 115
    move-result v2

    .line 116
    const/4 v12, 0x0

    .line 117
    const/4 v13, 0x1

    .line 118
    if-eqz v2, :cond_7

    .line 119
    .line 120
    iget-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableState;

    .line 121
    .line 122
    invoke-interface {v2}, Landroidx/compose/runtime/MutableState;->getValue()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v2

    .line 126
    check-cast v2, Ljava/lang/Boolean;

    .line 127
    .line 128
    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 129
    .line 130
    .line 131
    move-result v2

    .line 132
    if-eqz v2, :cond_7

    .line 133
    .line 134
    move v2, v13

    .line 135
    goto :goto_3

    .line 136
    :cond_7
    move v2, v12

    .line 137
    :goto_3
    div-int/lit8 v5, v1, 0x2

    .line 138
    .line 139
    invoke-interface {v8, v1}, Landroidx/compose/runtime/Composer;->changed(I)Z

    .line 140
    .line 141
    .line 142
    move-result v3

    .line 143
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 144
    .line 145
    .line 146
    move-result-object v4

    .line 147
    if-nez v3, :cond_8

    .line 148
    .line 149
    sget-object v3, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 150
    .line 151
    invoke-virtual {v3}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 152
    .line 153
    .line 154
    move-result-object v3

    .line 155
    if-ne v4, v3, :cond_9

    .line 156
    .line 157
    :cond_8
    new-instance v4, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 158
    .line 159
    invoke-direct {v4, v1}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 160
    .line 161
    .line 162
    invoke-interface {v8, v4}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 163
    .line 164
    .line 165
    :cond_9
    move-object v7, v4

    .line 166
    check-cast v7, Lkotlin/jvm/functions/Function0;

    .line 167
    .line 168
    const/16 v9, 0x30

    .line 169
    .line 170
    const/16 v10, 0x14

    .line 171
    .line 172
    move v1, v2

    .line 173
    const-wide/16 v2, 0x1388

    .line 174
    .line 175
    const/4 v4, 0x0

    .line 176
    const/4 v6, 0x0

    .line 177
    invoke-static/range {v1 .. v10}, Ltop/cycdm/cycapp/ui/common/AutoScrollPagerKt;->a(ZJLandroidx/compose/animation/core/AnimationSpec;IFLkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)Landroidx/compose/foundation/pager/PagerState;

    .line 178
    .line 179
    .line 180
    move-result-object v1

    .line 181
    sget-object v2, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 182
    .line 183
    const/4 v3, 0x0

    .line 184
    invoke-static {v2, v3, v13, v4}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxSize$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 185
    .line 186
    .line 187
    move-result-object v2

    .line 188
    iget-boolean v3, v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 189
    .line 190
    if-eqz v3, :cond_a

    .line 191
    .line 192
    const/16 v3, 0x14

    .line 193
    .line 194
    int-to-float v3, v3

    .line 195
    :goto_4
    invoke-static {v3}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 196
    .line 197
    .line 198
    move-result v3

    .line 199
    move v6, v3

    .line 200
    goto :goto_5

    .line 201
    :cond_a
    int-to-float v3, v12

    .line 202
    goto :goto_4

    .line 203
    :goto_5
    iget-boolean v3, v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 204
    .line 205
    if-eqz v3, :cond_b

    .line 206
    .line 207
    new-instance v3, Landroidx/compose/foundation/pager/PageSize$Fixed;

    .line 208
    .line 209
    invoke-interface {v11}, Landroidx/compose/foundation/layout/BoxWithConstraintsScope;->getMaxWidth-D9Ej5fM()F

    .line 210
    .line 211
    .line 212
    move-result v5

    .line 213
    const/high16 v7, 0x40200000    # 2.5f

    .line 214
    .line 215
    div-float/2addr v5, v7

    .line 216
    invoke-static {v5}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 217
    .line 218
    .line 219
    move-result v5

    .line 220
    invoke-direct {v3, v5, v4}, Landroidx/compose/foundation/pager/PageSize$Fixed;-><init>(FLkotlin/jvm/internal/n;)V

    .line 221
    .line 222
    .line 223
    :goto_6
    move-object v4, v3

    .line 224
    goto :goto_7

    .line 225
    :cond_b
    sget-object v3, Landroidx/compose/foundation/pager/PageSize$Fill;->INSTANCE:Landroidx/compose/foundation/pager/PageSize$Fill;

    .line 226
    .line 227
    goto :goto_6

    .line 228
    :goto_7
    new-instance v3, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 229
    .line 230
    iget-object v5, v0, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;

    .line 231
    .line 232
    iget-object v7, v0, Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;

    .line 233
    .line 234
    iget-object v9, v0, Ltop/cycdm/cycapp/ui/home/<USER>/view/View;

    .line 235
    .line 236
    invoke-direct {v3, v5, v7, v9}, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;Landroidx/navigation/NavHostController;Landroid/view/View;)V

    .line 237
    .line 238
    .line 239
    const/16 v5, 0x36

    .line 240
    .line 241
    const v7, -0x4e0546a6

    .line 242
    .line 243
    .line 244
    invoke-static {v7, v13, v3, v8, v5}, Landroidx/compose/runtime/internal/ComposableLambdaKt;->rememberComposableLambda(IZLjava/lang/Object;Landroidx/compose/runtime/Composer;I)Landroidx/compose/runtime/internal/ComposableLambda;

    .line 245
    .line 246
    .line 247
    move-result-object v15

    .line 248
    const/16 v18, 0x6000

    .line 249
    .line 250
    const/16 v19, 0x3fc4

    .line 251
    .line 252
    const/4 v3, 0x0

    .line 253
    const/4 v5, 0x1

    .line 254
    const/4 v7, 0x0

    .line 255
    const/4 v8, 0x0

    .line 256
    const/4 v9, 0x0

    .line 257
    const/4 v10, 0x0

    .line 258
    const/4 v11, 0x0

    .line 259
    const/4 v12, 0x0

    .line 260
    const/4 v13, 0x0

    .line 261
    const/4 v14, 0x0

    .line 262
    const/16 v17, 0x6030

    .line 263
    .line 264
    move-object/from16 v16, p2

    .line 265
    .line 266
    invoke-static/range {v1 .. v19}, Landroidx/compose/foundation/pager/PagerKt;->HorizontalPager--8jOkeI(Landroidx/compose/foundation/pager/PagerState;Landroidx/compose/ui/Modifier;Landroidx/compose/foundation/layout/PaddingValues;Landroidx/compose/foundation/pager/PageSize;IFLandroidx/compose/ui/Alignment$Vertical;Landroidx/compose/foundation/gestures/TargetedFlingBehavior;ZZLkotlin/jvm/functions/Function1;Landroidx/compose/ui/input/nestedscroll/NestedScrollConnection;Landroidx/compose/foundation/gestures/snapping/SnapPosition;Landroidx/compose/foundation/OverscrollEffect;Lkotlin/jvm/functions/Function4;Landroidx/compose/runtime/Composer;III)V

    .line 267
    .line 268
    .line 269
    move-object/from16 v8, v16

    .line 270
    .line 271
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 272
    .line 273
    .line 274
    goto :goto_8

    .line 275
    :cond_c
    const v1, -0x5ec1367c

    .line 276
    .line 277
    .line 278
    invoke-interface {v8, v1}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 279
    .line 280
    .line 281
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 282
    .line 283
    .line 284
    :goto_8
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 285
    .line 286
    .line 287
    move-result v1

    .line 288
    if-eqz v1, :cond_d

    .line 289
    .line 290
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 291
    .line 292
    .line 293
    :cond_d
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/layout/BoxWithConstraintsScope;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/Composer;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/layout/BoxWithConstraintsScope;Landroidx/compose/runtime/Composer;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 15
    .line 16
    return-object p1
.end method
