.class public final Lcom/kwad/components/ct/detail/b/c;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private Zh:I

.field private alX:Lcom/kwad/components/ct/detail/e/a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final amo:Lcom/kwad/components/core/j/a;

.field private apZ:Lcom/kwad/components/core/widget/a/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final avg:Z

.field private avh:Z

.field private avi:Z

.field private avj:Z

.field private final eQ:Lcom/kwad/sdk/core/i/c;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private final mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avg:Z

    .line 6
    .line 7
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avh:Z

    .line 8
    .line 9
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avi:Z

    .line 10
    .line 11
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avj:Z

    .line 12
    .line 13
    new-instance v0, Lcom/kwad/components/ct/detail/b/c$1;

    .line 14
    .line 15
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/c$1;-><init>(Lcom/kwad/components/ct/detail/b/c;)V

    .line 16
    .line 17
    .line 18
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c;->amo:Lcom/kwad/components/core/j/a;

    .line 19
    .line 20
    new-instance v0, Lcom/kwad/components/ct/detail/b/c$2;

    .line 21
    .line 22
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/c$2;-><init>(Lcom/kwad/components/ct/detail/b/c;)V

    .line 23
    .line 24
    .line 25
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 26
    .line 27
    new-instance v0, Lcom/kwad/components/ct/detail/b/c$3;

    .line 28
    .line 29
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/c$3;-><init>(Lcom/kwad/components/ct/detail/b/c;)V

    .line 30
    .line 31
    .line 32
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 33
    .line 34
    return-void
.end method

.method private Bo()V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avh:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x1

    .line 6
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avh:Z

    .line 7
    .line 8
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 13
    .line 14
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 15
    .line 16
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->i(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 17
    .line 18
    .line 19
    :cond_0
    return-void
.end method

.method private Bp()V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avh:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 10
    .line 11
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->l(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    return-void
.end method

.method private Bq()V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avi:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 10
    .line 11
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->j(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    const/4 v0, 0x1

    .line 17
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avi:Z

    .line 18
    .line 19
    return-void
.end method

.method private Br()V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avi:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 10
    .line 11
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->k(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    const/4 v0, 0x0

    .line 17
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avi:Z

    .line 18
    .line 19
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/sdk/core/i/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c;->eQ:Lcom/kwad/sdk/core/i/c;

    return-object p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c;Z)Z
    .locals 0

    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/b/c;->avh:Z

    return p1
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/widget/a/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c;->apZ:Lcom/kwad/components/core/widget/a/b;

    return-object p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/c;Z)Z
    .locals 0

    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/b/c;->avi:Z

    return p1
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/e/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/c;Z)Z
    .locals 0

    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/b/c;->avj:Z

    return p1
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/video/n;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/b/c;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/b/c;->avi:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/b/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c;->Br()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic h(Lcom/kwad/components/ct/detail/b/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c;->Bp()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic i(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic j(Lcom/kwad/components/ct/detail/b/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c;->Bo()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic k(Lcom/kwad/components/ct/detail/b/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c;->Bq()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final Bs()V
    .locals 3

    .line 1
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 6
    .line 7
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 8
    .line 9
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->e(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 10
    .line 11
    .line 12
    const/4 v0, 0x0

    .line 13
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avj:Z

    .line 14
    .line 15
    return-void
.end method

.method public final Bt()V
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avj:Z

    .line 3
    .line 4
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 9
    .line 10
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 11
    .line 12
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->f(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final Bu()V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avj:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 10
    .line 11
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->g(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    const/4 v0, 0x0

    .line 17
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c;->avj:Z

    .line 18
    .line 19
    return-void
.end method

.method public final Bv()V
    .locals 3

    .line 1
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 6
    .line 7
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 8
    .line 9
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/detail/listener/c;->h(ILcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final T()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 7
    .line 8
    if-nez v1, :cond_0

    .line 9
    .line 10
    const-string v0, "DetailOpenListener"

    .line 11
    .line 12
    const-string v1, "homePageHelper is null"

    .line 13
    .line 14
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->w(Ljava/lang/String;Ljava/lang/String;)V

    .line 15
    .line 16
    .line 17
    return-void

    .line 18
    :cond_0
    iget-object v1, v1, Lcom/kwad/components/ct/home/<USER>/kwad/components/core/widget/a/b;

    .line 19
    .line 20
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c;->apZ:Lcom/kwad/components/core/widget/a/b;

    .line 21
    .line 22
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 23
    .line 24
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 25
    .line 26
    iget v1, v0, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 27
    .line 28
    iput v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 29
    .line 30
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 31
    .line 32
    const/4 v1, 0x0

    .line 33
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->amo:Lcom/kwad/components/core/j/a;

    .line 34
    .line 35
    invoke-interface {v0, v1, v2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 39
    .line 40
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 41
    .line 42
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 43
    .line 44
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 7
    .line 8
    if-nez v1, :cond_0

    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 12
    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c;->amo:Lcom/kwad/components/core/j/a;

    .line 14
    .line 15
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final s(II)V
    .locals 3

    .line 1
    invoke-static {}, Lcom/kwad/components/ct/detail/listener/c;->yz()Lcom/kwad/components/ct/detail/listener/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lcom/kwad/components/ct/detail/b/c;->Zh:I

    .line 6
    .line 7
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 8
    .line 9
    invoke-virtual {v0, v1, v2, p1, p2}, Lcom/kwad/components/ct/detail/listener/c;->b(ILcom/kwad/components/ct/response/model/CtAdTemplate;II)V

    .line 10
    .line 11
    .line 12
    return-void
.end method
