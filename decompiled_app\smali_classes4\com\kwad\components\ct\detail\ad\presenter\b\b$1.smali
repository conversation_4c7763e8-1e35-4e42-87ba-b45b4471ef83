.class final Lcom/kwad/components/ct/detail/ad/presenter/b/b$1;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/b/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic anA:Lcom/kwad/components/ct/detail/ad/presenter/b/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/b/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b/b$1;->anA:Lcom/kwad/components/ct/detail/ad/presenter/b/b;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b/b$1;->anA:Lcom/kwad/components/ct/detail/ad/presenter/b/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b/b;->c(Lcom/kwad/components/ct/detail/ad/presenter/b/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final onMediaPlayProgress(JJ)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b/b$1;->anA:Lcom/kwad/components/ct/detail/ad/presenter/b/b;

    .line 2
    .line 3
    invoke-static {p1, p3, p4}, Lcom/kwad/components/ct/detail/ad/presenter/b/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/b/b;J)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final onMediaPlayStart()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b/b$1;->anA:Lcom/kwad/components/ct/detail/ad/presenter/b/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/b/b;)V

    .line 4
    .line 5
    .line 6
    invoke-static {}, Lcom/kwad/components/core/t/b;->sI()Lcom/kwad/components/core/t/b;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b/b$1;->anA:Lcom/kwad/components/ct/detail/ad/presenter/b/b;

    .line 11
    .line 12
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/b/b;->b(Lcom/kwad/components/ct/detail/ad/presenter/b/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    const/4 v2, 0x0

    .line 17
    invoke-virtual {v0, v1, v2, v2}, Lcom/kwad/components/core/t/b;->a(Lcom/kwad/sdk/core/response/model/AdTemplate;Lorg/json/JSONObject;Lcom/kwad/sdk/core/adlog/c/b;)Z

    .line 18
    .line 19
    .line 20
    return-void
.end method
