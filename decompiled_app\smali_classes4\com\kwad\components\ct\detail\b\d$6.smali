.class final Lcom/kwad/components/ct/detail/b/d$6;
.super Lcom/kwad/sdk/core/network/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/b/d;->BC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/kwad/sdk/core/network/o<",
        "Lcom/kwad/components/ct/request/r;",
        "Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$6;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/sdk/core/network/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method private BG()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$6;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->e(Lcom/kwad/components/ct/detail/b/d;Z)Z

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method private a(Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;)V
    .locals 2
    .param p1    # Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$6;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->A(Lcom/kwad/components/ct/detail/b/d;)Landroid/os/Handler;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lcom/kwad/components/ct/detail/b/d$6$1;

    .line 8
    .line 9
    invoke-direct {v1, p0, p1}, Lcom/kwad/components/ct/detail/b/d$6$1;-><init>(Lcom/kwad/components/ct/detail/b/d$6;Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 13
    .line 14
    .line 15
    return-void
.end method


# virtual methods
.method public final synthetic onError(Lcom/kwad/sdk/core/network/f;ILjava/lang/String;)V
    .locals 0
    .param p1    # Lcom/kwad/sdk/core/network/f;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/d$6;->BG()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final synthetic onSuccess(Lcom/kwad/sdk/core/network/f;Lcom/kwad/sdk/core/response/model/BaseResultData;)V
    .locals 0
    .param p1    # Lcom/kwad/sdk/core/network/f;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lcom/kwad/sdk/core/response/model/BaseResultData;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    check-cast p2, Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;

    .line 2
    .line 3
    invoke-direct {p0, p2}, Lcom/kwad/components/ct/detail/b/d$6;->a(Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
