.class public interface abstract Lcom/sigmob/sdk/downloader/core/breakpoint/f;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:Ljava/lang/String; = "id"

.field public static final b:Ljava/lang/String; = "url"

.field public static final c:Ljava/lang/String; = "etag"

.field public static final d:Ljava/lang/String; = "parent_path"

.field public static final e:Ljava/lang/String; = "filename"

.field public static final f:Ljava/lang/String; = "task_only_parent_path"

.field public static final g:Ljava/lang/String; = "chunked"

.field public static final h:Ljava/lang/String; = "breakpoint_id"

.field public static final i:Ljava/lang/String; = "block_index"

.field public static final j:Ljava/lang/String; = "start_offset"

.field public static final k:Ljava/lang/String; = "content_length"

.field public static final l:Ljava/lang/String; = "current_offset"
