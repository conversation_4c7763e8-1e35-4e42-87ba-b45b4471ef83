.class final Lcom/kwad/components/ct/detail/b/d$12;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/widget/swipe/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final j(F)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->F(Lcom/kwad/components/ct/detail/b/d;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    goto :goto_2

    .line 10
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 11
    .line 12
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-nez v0, :cond_1

    .line 21
    .line 22
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 23
    .line 24
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->G(Lcom/kwad/components/ct/detail/b/d;)V

    .line 25
    .line 26
    .line 27
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 28
    .line 29
    invoke-static {v0, p1}, Lcom/kwad/components/ct/detail/b/d;->a(Lcom/kwad/components/ct/detail/b/d;F)F

    .line 30
    .line 31
    .line 32
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 33
    .line 34
    invoke-static {v0, p1}, Lcom/kwad/components/ct/detail/b/d;->b(Lcom/kwad/components/ct/detail/b/d;F)V

    .line 35
    .line 36
    .line 37
    const/high16 v0, 0x3f800000    # 1.0f

    .line 38
    .line 39
    cmpl-float v0, p1, v0

    .line 40
    .line 41
    if-nez v0, :cond_2

    .line 42
    .line 43
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 44
    .line 45
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->i(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    const/16 v1, 0x8

    .line 50
    .line 51
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 56
    .line 57
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->i(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    const/4 v1, 0x0

    .line 62
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 63
    .line 64
    .line 65
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 66
    .line 67
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->H(Lcom/kwad/components/ct/detail/b/d;)Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    if-eqz v1, :cond_3

    .line 80
    .line 81
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    check-cast v1, Lcom/kwad/sdk/widget/swipe/a;

    .line 86
    .line 87
    invoke-interface {v1, p1}, Lcom/kwad/sdk/widget/swipe/a;->j(F)V

    .line 88
    .line 89
    .line 90
    goto :goto_1

    .line 91
    :cond_3
    :goto_2
    return-void
.end method

.method public final m(F)F
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->C(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Float;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->D(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, Landroid/view/View;->getTranslationX()F

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->a(Lcom/kwad/components/ct/detail/b/d;Ljava/lang/Float;)Ljava/lang/Float;

    .line 24
    .line 25
    .line 26
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 27
    .line 28
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->C(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Float;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    const/4 v1, 0x0

    .line 37
    cmpl-float v0, v0, v1

    .line 38
    .line 39
    const/high16 v2, 0x3f800000    # 1.0f

    .line 40
    .line 41
    if-nez v0, :cond_2

    .line 42
    .line 43
    cmpg-float v0, p1, v1

    .line 44
    .line 45
    if-gez v0, :cond_1

    .line 46
    .line 47
    return v1

    .line 48
    :cond_1
    invoke-static {p1}, Ljava/lang/Math;->abs(F)F

    .line 49
    .line 50
    .line 51
    move-result p1

    .line 52
    mul-float/2addr p1, v2

    .line 53
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 54
    .line 55
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->E(Lcom/kwad/components/ct/detail/b/d;)I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    int-to-float v0, v0

    .line 60
    div-float/2addr p1, v0

    .line 61
    invoke-static {v2, p1}, Ljava/lang/Math;->min(FF)F

    .line 62
    .line 63
    .line 64
    move-result p1

    .line 65
    return p1

    .line 66
    :cond_2
    cmpl-float v0, p1, v1

    .line 67
    .line 68
    if-lez v0, :cond_3

    .line 69
    .line 70
    return v2

    .line 71
    :cond_3
    invoke-static {p1}, Ljava/lang/Math;->abs(F)F

    .line 72
    .line 73
    .line 74
    move-result p1

    .line 75
    mul-float/2addr p1, v2

    .line 76
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 77
    .line 78
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->E(Lcom/kwad/components/ct/detail/b/d;)I

    .line 79
    .line 80
    .line 81
    move-result v0

    .line 82
    int-to-float v0, v0

    .line 83
    div-float/2addr p1, v0

    .line 84
    sub-float/2addr v2, p1

    .line 85
    invoke-static {v1, v2}, Ljava/lang/Math;->max(FF)F

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    return p1
.end method

.method public final n(F)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->F(Lcom/kwad/components/ct/detail/b/d;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    goto :goto_1

    .line 10
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 11
    .line 12
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->H(Lcom/kwad/components/ct/detail/b/d;)Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    if-eqz v1, :cond_1

    .line 25
    .line 26
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    check-cast v1, Lcom/kwad/sdk/widget/swipe/a;

    .line 31
    .line 32
    invoke-interface {v1, p1}, Lcom/kwad/sdk/widget/swipe/a;->n(F)V

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_1
    :goto_1
    return-void
.end method

.method public final o(F)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->F(Lcom/kwad/components/ct/detail/b/d;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    goto :goto_1

    .line 10
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 11
    .line 12
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->I(Lcom/kwad/components/ct/detail/b/d;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 16
    .line 17
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->J(Lcom/kwad/components/ct/detail/b/d;)Landroid/content/Context;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-static {v0}, Lcom/kwad/sdk/utils/af;->cY(Landroid/content/Context;)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 25
    .line 26
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    const/4 v1, 0x0

    .line 31
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->setEnabled(Z)V

    .line 32
    .line 33
    .line 34
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 35
    .line 36
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->H(Lcom/kwad/components/ct/detail/b/d;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    if-eqz v1, :cond_1

    .line 49
    .line 50
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    check-cast v1, Lcom/kwad/sdk/widget/swipe/a;

    .line 55
    .line 56
    invoke-interface {v1, p1}, Lcom/kwad/sdk/widget/swipe/a;->o(F)V

    .line 57
    .line 58
    .line 59
    goto :goto_0

    .line 60
    :cond_1
    :goto_1
    return-void
.end method

.method public final p(F)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->F(Lcom/kwad/components/ct/detail/b/d;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 11
    .line 12
    const/4 v1, 0x0

    .line 13
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->a(Lcom/kwad/components/ct/detail/b/d;Ljava/lang/Float;)Ljava/lang/Float;

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 17
    .line 18
    invoke-static {v0, p1}, Lcom/kwad/components/ct/detail/b/d;->a(Lcom/kwad/components/ct/detail/b/d;F)F

    .line 19
    .line 20
    .line 21
    new-instance v0, Ljava/lang/StringBuilder;

    .line 22
    .line 23
    const-string v1, "updateFeed onSwipeFinish mPosition"

    .line 24
    .line 25
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 29
    .line 30
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->K(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/c;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    iget v1, v1, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 35
    .line 36
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 37
    .line 38
    .line 39
    const-string v1, "--mSourceType="

    .line 40
    .line 41
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 42
    .line 43
    .line 44
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 45
    .line 46
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    invoke-virtual {v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->getSourceType()I

    .line 51
    .line 52
    .line 53
    move-result v1

    .line 54
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 55
    .line 56
    .line 57
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    const-string v1, "DetailProfileSlidePresenter"

    .line 62
    .line 63
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 64
    .line 65
    .line 66
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 67
    .line 68
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->L(Lcom/kwad/components/ct/detail/b/d;)V

    .line 69
    .line 70
    .line 71
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 72
    .line 73
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->H(Lcom/kwad/components/ct/detail/b/d;)Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 82
    .line 83
    .line 84
    move-result v1

    .line 85
    if-eqz v1, :cond_1

    .line 86
    .line 87
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    check-cast v1, Lcom/kwad/sdk/widget/swipe/a;

    .line 92
    .line 93
    invoke-interface {v1, p1}, Lcom/kwad/sdk/widget/swipe/a;->p(F)V

    .line 94
    .line 95
    .line 96
    goto :goto_0

    .line 97
    :cond_1
    const/4 v0, 0x0

    .line 98
    cmpl-float v0, p1, v0

    .line 99
    .line 100
    if-nez v0, :cond_2

    .line 101
    .line 102
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 107
    .line 108
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->n(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/e/b;->X(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 113
    .line 114
    .line 115
    :cond_2
    const/high16 v0, 0x3f800000    # 1.0f

    .line 116
    .line 117
    cmpl-float p1, p1, v0

    .line 118
    .line 119
    if-nez p1, :cond_3

    .line 120
    .line 121
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 122
    .line 123
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->i(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    const/16 v0, 0x8

    .line 128
    .line 129
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 130
    .line 131
    .line 132
    return-void

    .line 133
    :cond_3
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$12;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 134
    .line 135
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->i(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    const/4 v0, 0x0

    .line 140
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 141
    .line 142
    .line 143
    return-void
.end method
