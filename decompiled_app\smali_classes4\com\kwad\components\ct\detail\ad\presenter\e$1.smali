.class final Lcom/kwad/components/ct/detail/ad/presenter/e$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/e;->T()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amC:Lcom/kwad/components/ct/detail/ad/presenter/e;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/e;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$1;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$1;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;)Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget v1, Lcom/kwad/sdk/R$dimen;->ksad_content_feed_force_look_padding:I

    .line 8
    .line 9
    invoke-static {v0, v1}, Lcom/kwad/sdk/c/a/a;->h(Landroid/content/Context;I)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$1;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 14
    .line 15
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->b(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/core/widget/ComplianceTextView;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    add-int/2addr v2, v0

    .line 24
    invoke-static {v1, v2}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;I)V

    .line 25
    .line 26
    .line 27
    return-void
.end method
