.class public final Lcom/kwad/components/ct/detail/ad/presenter/b;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private amm:Landroid/view/View;

.field private amn:Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;

.field private amo:Lcom/kwad/components/core/j/a;

.field private amp:Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/b$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/b;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amo:Lcom/kwad/components/core/j/a;

    .line 10
    .line 11
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/b$2;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/b$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/b;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amp:Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;

    .line 17
    .line 18
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amp:Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amn:Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    return-object p0
.end method

.method private c(Lcom/kwad/sdk/api/core/fragment/KsFragment;)V
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amm:Landroid/view/View;

    if-eqz v0, :cond_0

    return-void

    .line 3
    :cond_0
    invoke-virtual {p1}, Lcom/kwad/sdk/api/core/fragment/KsFragment;->getParentFragment()Lcom/kwad/sdk/api/core/fragment/KsFragment;

    move-result-object p1

    invoke-virtual {p1}, Lcom/kwad/sdk/api/core/fragment/KsFragment;->getView()Landroid/view/View;

    move-result-object p1

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amm:Landroid/view/View;

    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_swipe:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amn:Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;

    return-void
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/ad/presenter/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->xF()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private xF()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 6
    .line 7
    invoke-static {v0, v1}, Lcom/kwad/components/core/page/AdWebViewActivityProxy;->launch(Landroid/content/Context;Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 7
    .line 8
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->c(Lcom/kwad/sdk/api/core/fragment/KsFragment;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 12
    .line 13
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 14
    .line 15
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 16
    .line 17
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 18
    .line 19
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amo:Lcom/kwad/components/core/j/a;

    .line 20
    .line 21
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 4
    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b;->amo:Lcom/kwad/components/core/j/a;

    .line 6
    .line 7
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    return-void
.end method
