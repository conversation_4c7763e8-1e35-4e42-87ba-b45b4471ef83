.class public interface abstract Landroidx/media3/container/ReorderingSeiMessageQueue$SeiConsumer;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/container/ReorderingSeiMessageQueue;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "SeiConsumer"
.end annotation


# virtual methods
.method public abstract consume(JLandroidx/media3/common/util/ParsableByteArray;)V
.end method
