.class public interface abstract Landroidx/media3/datasource/DataSource$Factory;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/datasource/DataSource;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Factory"
.end annotation


# virtual methods
.method public abstract createDataSource()Landroidx/media3/datasource/DataSource;
    .annotation build Landroidx/media3/common/util/UnstableApi;
    .end annotation
.end method
