.class public final synthetic Ltop/cycdm/cycapp/ui/history/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroidx/navigation/NavHostController;

.field public final synthetic b:Ltop/cycdm/model/j;


# direct methods
.method public synthetic constructor <init>(Landroidx/navigation/NavHostController;Ltop/cycdm/model/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/v;->a:Landroidx/navigation/NavHostController;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/v;->b:Ltop/cycdm/model/j;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/v;->a:Landroidx/navigation/NavHostController;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/v;->b:Ltop/cycdm/model/j;

    invoke-static {v0, v1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->a(Landroidx/navigation/NavHostController;Ltop/cycdm/model/j;)Lkotlin/t;

    move-result-object v0

    return-object v0
.end method
