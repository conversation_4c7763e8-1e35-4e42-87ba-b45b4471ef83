.class public final Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function3;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->K(Landroidx/compose/runtime/Composer;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

.field public final synthetic b:Landroidx/compose/runtime/State;

.field public final synthetic c:Landroidx/navigation/NavHostController;


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;Landroidx/compose/runtime/State;Landroidx/navigation/NavHostController;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->b:Landroidx/compose/runtime/State;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->c:Landroidx/navigation/NavHostController;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;Landroidx/compose/material3/SwipeToDismissBoxValue;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->c(Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;Landroidx/compose/material3/SwipeToDismissBoxValue;)Z

    move-result p0

    return p0
.end method

.method public static final c(Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;Landroidx/compose/material3/SwipeToDismissBoxValue;)Z
    .locals 1

    .line 1
    sget-object v0, Landroidx/compose/material3/SwipeToDismissBoxValue;->EndToStart:Landroidx/compose/material3/SwipeToDismissBoxValue;

    .line 2
    .line 3
    if-ne p2, v0, :cond_0

    .line 4
    .line 5
    new-instance p2, Ltop/cycdm/cycapp/ui/history/a$b;

    .line 6
    .line 7
    invoke-direct {p2, p1}, Ltop/cycdm/cycapp/ui/history/a$b;-><init>(Ltop/cycdm/model/j;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0, p2}, Ltop/cycdm/cycapp/BaseVM;->postSideEffectNotSuspend(Ljava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 11
    .line 12
    .line 13
    :cond_0
    const/4 p0, 0x0

    .line 14
    return p0
.end method


# virtual methods
.method public final b(Ltop/cycdm/model/j;Landroidx/compose/runtime/Composer;I)V
    .locals 10

    .line 1
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 2
    .line 3
    .line 4
    move-result v1

    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    const/4 v1, -0x1

    .line 8
    const-string v2, "top.cycdm.cycapp.ui.history.HistoryList.<anonymous> (HistoryScreen.kt:103)"

    .line 9
    .line 10
    const v3, -0x5d6cd0c0

    .line 11
    .line 12
    .line 13
    invoke-static {v3, p3, v1, v2}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->b:Landroidx/compose/runtime/State;

    .line 17
    .line 18
    invoke-static {v1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->Y(Landroidx/compose/runtime/State;)Ltop/cycdm/cycapp/ui/history/w;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {v1}, Ltop/cycdm/cycapp/ui/history/w;->b()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-virtual {p1}, Ltop/cycdm/model/j;->e()I

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    invoke-interface {v1, v2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    if-eqz v1, :cond_1

    .line 39
    .line 40
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    if-eqz v0, :cond_4

    .line 45
    .line 46
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 47
    .line 48
    .line 49
    return-void

    .line 50
    :cond_1
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 51
    .line 52
    invoke-interface {p2, v1}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    move-result v1

    .line 56
    invoke-interface {p2, p1}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    move-result v2

    .line 60
    or-int/2addr v1, v2

    .line 61
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 62
    .line 63
    invoke-interface {p2}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v3

    .line 67
    if-nez v1, :cond_2

    .line 68
    .line 69
    sget-object v1, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 70
    .line 71
    invoke-virtual {v1}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    if-ne v3, v1, :cond_3

    .line 76
    .line 77
    :cond_2
    new-instance v3, Ltop/cycdm/cycapp/ui/history/u;

    .line 78
    .line 79
    invoke-direct {v3, v2, p1}, Ltop/cycdm/cycapp/ui/history/u;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)V

    .line 80
    .line 81
    .line 82
    invoke-interface {p2, v3}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 83
    .line 84
    .line 85
    :cond_3
    move-object v2, v3

    .line 86
    check-cast v2, Lkotlin/jvm/functions/Function1;

    .line 87
    .line 88
    const/4 v5, 0x0

    .line 89
    const/4 v6, 0x5

    .line 90
    const/4 v1, 0x0

    .line 91
    const/4 v3, 0x0

    .line 92
    move-object v4, p2

    .line 93
    invoke-static/range {v1 .. v6}, Landroidx/compose/material3/SwipeToDismissBoxKt;->rememberSwipeToDismissBoxState(Landroidx/compose/material3/SwipeToDismissBoxValue;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/Composer;II)Landroidx/compose/material3/SwipeToDismissBoxState;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    new-instance v2, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$a;

    .line 98
    .line 99
    invoke-direct {v2, v1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$a;-><init>(Landroidx/compose/material3/SwipeToDismissBoxState;)V

    .line 100
    .line 101
    .line 102
    const v3, 0x648d99de

    .line 103
    .line 104
    .line 105
    const/4 v5, 0x1

    .line 106
    const/16 v6, 0x36

    .line 107
    .line 108
    invoke-static {v3, v5, v2, p2, v6}, Landroidx/compose/runtime/internal/ComposableLambdaKt;->rememberComposableLambda(IZLjava/lang/Object;Landroidx/compose/runtime/Composer;I)Landroidx/compose/runtime/internal/ComposableLambda;

    .line 109
    .line 110
    .line 111
    move-result-object v2

    .line 112
    new-instance v3, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;

    .line 113
    .line 114
    iget-object v7, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->c:Landroidx/navigation/NavHostController;

    .line 115
    .line 116
    invoke-direct {v3, p1, v7}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;-><init>(Ltop/cycdm/model/j;Landroidx/navigation/NavHostController;)V

    .line 117
    .line 118
    .line 119
    const v0, 0x453373a3

    .line 120
    .line 121
    .line 122
    invoke-static {v0, v5, v3, p2, v6}, Landroidx/compose/runtime/internal/ComposableLambdaKt;->rememberComposableLambda(IZLjava/lang/Object;Landroidx/compose/runtime/Composer;I)Landroidx/compose/runtime/internal/ComposableLambda;

    .line 123
    .line 124
    .line 125
    move-result-object v6

    .line 126
    const v0, 0x180c30

    .line 127
    .line 128
    .line 129
    sget v3, Landroidx/compose/material3/SwipeToDismissBoxState;->$stable:I

    .line 130
    .line 131
    or-int v8, v3, v0

    .line 132
    .line 133
    const/16 v9, 0x34

    .line 134
    .line 135
    move-object v0, v1

    .line 136
    move-object v1, v2

    .line 137
    const/4 v2, 0x0

    .line 138
    const/4 v3, 0x0

    .line 139
    const/4 v4, 0x0

    .line 140
    const/4 v5, 0x0

    .line 141
    move-object v7, p2

    .line 142
    invoke-static/range {v0 .. v9}, Landroidx/compose/material3/SwipeToDismissBoxKt;->SwipeToDismissBox(Landroidx/compose/material3/SwipeToDismissBoxState;Lkotlin/jvm/functions/Function3;Landroidx/compose/ui/Modifier;ZZZLkotlin/jvm/functions/Function3;Landroidx/compose/runtime/Composer;II)V

    .line 143
    .line 144
    .line 145
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 146
    .line 147
    .line 148
    move-result v0

    .line 149
    if-eqz v0, :cond_4

    .line 150
    .line 151
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 152
    .line 153
    .line 154
    :cond_4
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ltop/cycdm/model/j;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/Composer;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->b(Ltop/cycdm/model/j;Landroidx/compose/runtime/Composer;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 15
    .line 16
    return-object p1
.end method
