.class public final synthetic Ltop/cycdm/cycapp/ui/email/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroidx/navigation/NavHostController;


# direct methods
.method public synthetic constructor <init>(Landroidx/navigation/NavHostController;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/c;->a:Landroidx/navigation/NavHostController;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/c;->a:Landroidx/navigation/NavHostController;

    invoke-static {v0}, Ltop/cycdm/cycapp/ui/email/EmailScreenKt;->g(Landroidx/navigation/NavHostController;)<PERSON><PERSON><PERSON>/t;

    move-result-object v0

    return-object v0
.end method
