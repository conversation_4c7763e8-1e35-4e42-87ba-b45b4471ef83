.class final Lcom/kwad/components/ct/detail/b/c/b$1;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awL:Lcom/kwad/components/ct/detail/b/c/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayPaused()V
    .locals 8

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayPaused()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 5
    .line 6
    const/4 v1, 0x1

    .line 7
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c/b;->a(Lcom/kwad/components/ct/detail/b/c/b;Z)Z

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 11
    .line 12
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->j(Lcom/kwad/components/ct/detail/b/c/b;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 19
    .line 20
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->l(Lcom/kwad/components/ct/detail/b/c/b;)J

    .line 21
    .line 22
    .line 23
    move-result-wide v0

    .line 24
    const-wide/16 v2, 0x0

    .line 25
    .line 26
    cmp-long v0, v0, v2

    .line 27
    .line 28
    if-lez v0, :cond_0

    .line 29
    .line 30
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    .line 31
    .line 32
    .line 33
    move-result-wide v0

    .line 34
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 35
    .line 36
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/c/b;->l(Lcom/kwad/components/ct/detail/b/c/b;)J

    .line 37
    .line 38
    .line 39
    move-result-wide v2

    .line 40
    sub-long/2addr v0, v2

    .line 41
    :goto_0
    move-wide v5, v0

    .line 42
    goto :goto_1

    .line 43
    :cond_0
    const-wide/16 v0, -0x1

    .line 44
    .line 45
    goto :goto_0

    .line 46
    :goto_1
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 51
    .line 52
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->m(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/sdk/internal/api/SceneImpl;

    .line 53
    .line 54
    .line 55
    move-result-object v3

    .line 56
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 57
    .line 58
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->f(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    invoke-static {}, Lcom/kwad/components/core/video/c;->tt()Lcom/kwad/components/core/video/c;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    invoke-virtual {v0}, Lcom/kwad/components/core/video/c;->tw()I

    .line 67
    .line 68
    .line 69
    move-result v7

    .line 70
    invoke-virtual/range {v2 .. v7}, Lcom/kwad/components/ct/e/b;->a(Lcom/kwad/sdk/internal/api/SceneImpl;Lcom/kwad/components/ct/response/model/CtAdTemplate;JI)V

    .line 71
    .line 72
    .line 73
    :cond_1
    return-void
.end method

.method public final onMediaPlayStart()V
    .locals 4

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayStart()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->a(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/detail/c;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 11
    .line 12
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 13
    .line 14
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/b;->b(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/detail/c;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 19
    .line 20
    iput-object v1, v0, Lcom/kwad/components/ct/home/<USER>/kwad/components/ct/response/model/CtAdTemplate;

    .line 21
    .line 22
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 23
    .line 24
    const/4 v1, 0x0

    .line 25
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c/b;->a(Lcom/kwad/components/ct/detail/b/c/b;Z)Z

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 29
    .line 30
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->c(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 31
    .line 32
    .line 33
    new-instance v0, Ljava/lang/StringBuilder;

    .line 34
    .line 35
    const-string v1, "onVideoPlayStart() mPlayTimes="

    .line 36
    .line 37
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 41
    .line 42
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/b;->d(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    const-string v1, "DetailLogVideoPresenter"

    .line 54
    .line 55
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 59
    .line 60
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->d(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 61
    .line 62
    .line 63
    move-result v0

    .line 64
    const/4 v2, 0x1

    .line 65
    if-le v0, v2, :cond_0

    .line 66
    .line 67
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 68
    .line 69
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->e(Lcom/kwad/components/ct/detail/b/c/b;)V

    .line 70
    .line 71
    .line 72
    const-class v0, Lcom/kwad/components/ec/api/a;

    .line 73
    .line 74
    invoke-static {v0}, Lcom/kwad/sdk/components/d;->g(Ljava/lang/Class;)Lcom/kwad/sdk/components/b;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    check-cast v0, Lcom/kwad/components/ec/api/a;

    .line 79
    .line 80
    if-eqz v0, :cond_0

    .line 81
    .line 82
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 83
    .line 84
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->f(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 85
    .line 86
    .line 87
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 88
    .line 89
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->g(Lcom/kwad/components/ct/detail/b/c/b;)Z

    .line 90
    .line 91
    .line 92
    move-result v0

    .line 93
    if-nez v0, :cond_1

    .line 94
    .line 95
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 96
    .line 97
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    .line 98
    .line 99
    .line 100
    move-result-wide v2

    .line 101
    invoke-static {v0, v2, v3}, Lcom/kwad/components/ct/detail/b/c/b;->a(Lcom/kwad/components/ct/detail/b/c/b;J)J

    .line 102
    .line 103
    .line 104
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 105
    .line 106
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    .line 107
    .line 108
    .line 109
    move-result-wide v2

    .line 110
    invoke-static {v0, v2, v3}, Lcom/kwad/components/ct/detail/b/c/b;->b(Lcom/kwad/components/ct/detail/b/c/b;J)J

    .line 111
    .line 112
    .line 113
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/b;->BY()Z

    .line 114
    .line 115
    .line 116
    move-result v0

    .line 117
    if-eqz v0, :cond_2

    .line 118
    .line 119
    new-instance v0, Ljava/lang/StringBuilder;

    .line 120
    .line 121
    const-string v2, "position: "

    .line 122
    .line 123
    invoke-direct {v0, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 124
    .line 125
    .line 126
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 127
    .line 128
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/c/b;->h(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 129
    .line 130
    .line 131
    move-result v2

    .line 132
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 133
    .line 134
    .line 135
    const-string v2, " onVideoPlayStart"

    .line 136
    .line 137
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 138
    .line 139
    .line 140
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 141
    .line 142
    .line 143
    move-result-object v0

    .line 144
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 145
    .line 146
    .line 147
    :cond_2
    return-void
.end method

.method public final onMediaPlaying()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlaying()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/b;->BY()Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    new-instance v0, Ljava/lang/StringBuilder;

    .line 11
    .line 12
    const-string v1, "position: "

    .line 13
    .line 14
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 15
    .line 16
    .line 17
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 18
    .line 19
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/b;->h(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 24
    .line 25
    .line 26
    const-string v1, " onVideoPlaying"

    .line 27
    .line 28
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    const-string v1, "DetailLogVideoPresenter"

    .line 36
    .line 37
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 41
    .line 42
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->i(Lcom/kwad/components/ct/detail/b/c/b;)V

    .line 43
    .line 44
    .line 45
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 46
    .line 47
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    .line 48
    .line 49
    .line 50
    move-result-wide v1

    .line 51
    invoke-static {v0, v1, v2}, Lcom/kwad/components/ct/detail/b/c/b;->b(Lcom/kwad/components/ct/detail/b/c/b;J)J

    .line 52
    .line 53
    .line 54
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 55
    .line 56
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->j(Lcom/kwad/components/ct/detail/b/c/b;)Z

    .line 57
    .line 58
    .line 59
    move-result v0

    .line 60
    if-eqz v0, :cond_1

    .line 61
    .line 62
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 63
    .line 64
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/b;->k(Lcom/kwad/components/ct/detail/b/c/b;)Z

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    if-eqz v0, :cond_1

    .line 69
    .line 70
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 75
    .line 76
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/b;->f(Lcom/kwad/components/ct/detail/b/c/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    invoke-static {}, Lcom/kwad/components/core/video/c;->tt()Lcom/kwad/components/core/video/c;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lcom/kwad/components/core/video/c;->tw()I

    .line 85
    .line 86
    .line 87
    move-result v2

    .line 88
    invoke-virtual {v0, v1, v2}, Lcom/kwad/components/ct/e/b;->f(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 89
    .line 90
    .line 91
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$1;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 92
    .line 93
    const/4 v1, 0x0

    .line 94
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c/b;->a(Lcom/kwad/components/ct/detail/b/c/b;Z)Z

    .line 95
    .line 96
    .line 97
    return-void
.end method
