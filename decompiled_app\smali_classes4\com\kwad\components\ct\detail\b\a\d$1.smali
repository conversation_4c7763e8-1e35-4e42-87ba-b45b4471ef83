.class final Lcom/kwad/components/ct/detail/b/a/d$1;
.super Lcom/kwad/sdk/utils/bh;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/b/a/d;->j(Landroid/app/Activity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awn:Landroid/graphics/Point;

.field final synthetic awo:I

.field final synthetic awp:Lcom/kwad/components/ct/detail/b/a/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/a/d;Landroid/graphics/Point;I)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awp:Lcom/kwad/components/ct/detail/b/a/d;

    .line 2
    .line 3
    iput-object p2, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awn:Landroid/graphics/Point;

    .line 4
    .line 5
    iput p3, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awo:I

    .line 6
    .line 7
    invoke-direct {p0}, Lcom/kwad/sdk/utils/bh;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final doTask()V
    .locals 4

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v0, v0, [I

    .line 3
    .line 4
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awp:Lcom/kwad/components/ct/detail/b/a/d;

    .line 5
    .line 6
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/a/d;->a(Lcom/kwad/components/ct/detail/b/a/d;)Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1, v0}, Landroid/view/View;->getLocationOnScreen([I)V

    .line 11
    .line 12
    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awn:Landroid/graphics/Point;

    .line 14
    .line 15
    iget v1, v1, Landroid/graphics/Point;->y:I

    .line 16
    .line 17
    const/4 v2, 0x1

    .line 18
    aget v0, v0, v2

    .line 19
    .line 20
    sub-int/2addr v1, v0

    .line 21
    if-ltz v1, :cond_0

    .line 22
    .line 23
    iget v0, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awo:I

    .line 24
    .line 25
    if-ge v1, v0, :cond_0

    .line 26
    .line 27
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awp:Lcom/kwad/components/ct/detail/b/a/d;

    .line 28
    .line 29
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a/d;->b(Lcom/kwad/components/ct/detail/b/a/d;)Landroid/view/View;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    iget v1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 38
    .line 39
    iget v3, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awo:I

    .line 40
    .line 41
    add-int/2addr v1, v3

    .line 42
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 43
    .line 44
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awp:Lcom/kwad/components/ct/detail/b/a/d;

    .line 45
    .line 46
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/a/d;->b(Lcom/kwad/components/ct/detail/b/a/d;)Landroid/view/View;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    invoke-virtual {v1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 51
    .line 52
    .line 53
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awp:Lcom/kwad/components/ct/detail/b/a/d;

    .line 54
    .line 55
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a/d;->c(Lcom/kwad/components/ct/detail/b/a/d;)Landroid/widget/RelativeLayout;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    iget v1, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awo:I

    .line 60
    .line 61
    const/4 v3, 0x0

    .line 62
    invoke-virtual {v0, v3, v3, v3, v1}, Landroid/view/View;->setPadding(IIII)V

    .line 63
    .line 64
    .line 65
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/d$1;->awp:Lcom/kwad/components/ct/detail/b/a/d;

    .line 66
    .line 67
    invoke-static {v0, v2}, Lcom/kwad/components/ct/detail/b/a/d;->a(Lcom/kwad/components/ct/detail/b/a/d;Z)Z

    .line 68
    .line 69
    .line 70
    :cond_0
    return-void
.end method
