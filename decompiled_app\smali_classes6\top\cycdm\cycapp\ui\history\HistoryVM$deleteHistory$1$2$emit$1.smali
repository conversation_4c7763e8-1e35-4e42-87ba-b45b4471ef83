.class final Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;
.super Lkotlin/coroutines/jvm/internal/ContinuationImpl;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.history.HistoryVM$deleteHistory$1$2"
    f = "HistoryVM.kt"
    i = {}
    l = {
        0x4b,
        0x4e,
        0x50
    }
    m = "emit"
    n = {}
    s = {}
.end annotation


# instance fields
.field label:I

.field synthetic result:Ljava/lang/Object;

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;

    invoke-direct {p0, p2}, Lkotlin/coroutines/jvm/internal/ContinuationImpl;-><init>(Lkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->result:Ljava/lang/Object;

    iget p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    const/high16 v0, -0x80000000

    or-int/2addr p1, v0

    iput p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;

    const/4 v0, 0x0

    invoke-virtual {p1, v0, p0}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
