.class final Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryVM;->deleteHistory(I)Lkotlinx/coroutines/w1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Ltop/cycdm/cycapp/ui/history/w;",
        "Ltop/cycdm/cycapp/ui/history/a;",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.history.HistoryVM$deleteHistory$1"
    f = "HistoryVM.kt"
    i = {
        0x0
    }
    l = {
        0x3f,
        0x41,
        0x48
    }
    m = "invokeSuspend"
    n = {
        "$this$intent"
    }
    s = {
        "L$0"
    }
.end annotation


# instance fields
.field final synthetic $id:I

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltop/cycdm/cycapp/ui/history/HistoryVM;",
            "I",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iput p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->$id:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iget v2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->$id:I

    invoke-direct {v0, v1, v2, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;ILkotlin/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    if-eq v1, v4, :cond_2

    .line 13
    .line 14
    if-eq v1, v3, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    goto/16 :goto_1

    .line 22
    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto/16 :goto_4

    .line 35
    .line 36
    :cond_2
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->L$0:Ljava/lang/Object;

    .line 37
    .line 38
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 39
    .line 40
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_3
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->L$0:Ljava/lang/Object;

    .line 48
    .line 49
    move-object v1, p1

    .line 50
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 51
    .line 52
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 53
    .line 54
    invoke-static {p1}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lg8/g;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-interface {p1}, Lg8/g;->c()Lkotlinx/coroutines/flow/d;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->L$0:Ljava/lang/Object;

    .line 63
    .line 64
    iput v4, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->label:I

    .line 65
    .line 66
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/f;->B(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    if-ne p1, v0, :cond_4

    .line 71
    .line 72
    goto :goto_3

    .line 73
    :cond_4
    :goto_0
    check-cast p1, Ltop/cycdm/model/w;

    .line 74
    .line 75
    invoke-virtual {p1}, Ltop/cycdm/model/w;->a()I

    .line 76
    .line 77
    .line 78
    move-result v4

    .line 79
    invoke-virtual {p1}, Ltop/cycdm/model/w;->b()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    const-string v5, ""

    .line 84
    .line 85
    invoke-static {p1, v5}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    move-result v5

    .line 89
    const/4 v6, 0x0

    .line 90
    if-nez v5, :cond_7

    .line 91
    .line 92
    const/4 v5, -0x1

    .line 93
    if-ne v4, v5, :cond_5

    .line 94
    .line 95
    goto :goto_2

    .line 96
    :cond_5
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 97
    .line 98
    invoke-static {v3}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->access$getUserRep$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lg8/h;

    .line 99
    .line 100
    .line 101
    move-result-object v3

    .line 102
    iget v5, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->$id:I

    .line 103
    .line 104
    invoke-interface {v3, p1, v4, v5}, Lg8/h;->i(Ljava/lang/String;II)Lkotlinx/coroutines/flow/d;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    new-instance v3, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$1;

    .line 109
    .line 110
    invoke-direct {v3, v1, v6}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$1;-><init>(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    .line 111
    .line 112
    .line 113
    invoke-static {p1, v3}, Lkotlinx/coroutines/flow/f;->i(Lkotlinx/coroutines/flow/d;Lkotlin/jvm/functions/Function3;)Lkotlinx/coroutines/flow/d;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    new-instance v3, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;

    .line 118
    .line 119
    iget-object v4, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 120
    .line 121
    iget v5, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->$id:I

    .line 122
    .line 123
    invoke-direct {v3, v4, v5, v1}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;ILorg/orbitmvi/orbit/syntax/simple/b;)V

    .line 124
    .line 125
    .line 126
    iput-object v6, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->L$0:Ljava/lang/Object;

    .line 127
    .line 128
    iput v2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->label:I

    .line 129
    .line 130
    invoke-interface {p1, v3, p0}, Lkotlinx/coroutines/flow/d;->collect(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    if-ne p1, v0, :cond_6

    .line 135
    .line 136
    goto :goto_3

    .line 137
    :cond_6
    :goto_1
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 138
    .line 139
    return-object p1

    .line 140
    :cond_7
    :goto_2
    new-instance p1, Ltop/cycdm/cycapp/ui/history/a$d;

    .line 141
    .line 142
    const-string v2, "\u8bf7\u5148\u767b\u5f55"

    .line 143
    .line 144
    invoke-direct {p1, v2}, Ltop/cycdm/cycapp/ui/history/a$d;-><init>(Ljava/lang/String;)V

    .line 145
    .line 146
    .line 147
    iput-object v6, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->L$0:Ljava/lang/Object;

    .line 148
    .line 149
    iput v3, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->label:I

    .line 150
    .line 151
    invoke-static {v1, p1, p0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->d(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    if-ne p1, v0, :cond_8

    .line 156
    .line 157
    :goto_3
    return-object v0

    .line 158
    :cond_8
    :goto_4
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 159
    .line 160
    return-object p1
.end method
