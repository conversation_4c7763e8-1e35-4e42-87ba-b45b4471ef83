.class public final Lcom/kwad/components/ct/detail/b/b/a;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private amo:Lcom/kwad/components/core/j/a;

.field private avF:Z

.field private awq:Lcom/kwad/lottie/LottieAnimationView;

.field private awr:Landroid/view/ViewGroup;

.field private aws:Landroid/widget/TextView;

.field private awt:Ljava/lang/Runnable;

.field private awu:Ljava/lang/Runnable;

.field private mHandler:Landroid/os/Handler;

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->avF:Z

    .line 6
    .line 7
    new-instance v0, Lcom/kwad/components/ct/detail/b/b/a$1;

    .line 8
    .line 9
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/b/a$1;-><init>(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 10
    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->amo:Lcom/kwad/components/core/j/a;

    .line 13
    .line 14
    new-instance v0, Lcom/kwad/components/ct/detail/b/b/a$3;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/b/a$3;-><init>(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 17
    .line 18
    .line 19
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awt:Ljava/lang/Runnable;

    .line 20
    .line 21
    new-instance v1, Lcom/kwad/components/core/t/s;

    .line 22
    .line 23
    invoke-direct {v1, v0}, Lcom/kwad/components/core/t/s;-><init>(Ljava/lang/Runnable;)V

    .line 24
    .line 25
    .line 26
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->awu:Ljava/lang/Runnable;

    .line 27
    .line 28
    new-instance v0, Lcom/kwad/components/ct/detail/b/b/a$4;

    .line 29
    .line 30
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/b/a$4;-><init>(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 31
    .line 32
    .line 33
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 34
    .line 35
    return-void
.end method

.method private BM()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Lcom/kwad/sdk/utils/an;->isNetworkConnected(Landroid/content/Context;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/b/a;->BN()V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awr:Landroid/view/ViewGroup;

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-nez v0, :cond_1

    .line 28
    .line 29
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 30
    .line 31
    invoke-virtual {v0}, Lcom/kwad/lottie/LottieAnimationView;->isAnimating()Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    return-void

    .line 38
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 39
    .line 40
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 41
    .line 42
    .line 43
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 44
    .line 45
    invoke-virtual {v0}, Lcom/kwad/lottie/LottieAnimationView;->isAnimating()Z

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    if-nez v0, :cond_2

    .line 50
    .line 51
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 52
    .line 53
    invoke-virtual {v0}, Lcom/kwad/lottie/LottieAnimationView;->Pr()V

    .line 54
    .line 55
    .line 56
    :cond_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awr:Landroid/view/ViewGroup;

    .line 57
    .line 58
    const/16 v1, 0x8

    .line 59
    .line 60
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 61
    .line 62
    .line 63
    return-void
.end method

.method private BN()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    invoke-virtual {v0}, Lcom/kwad/lottie/LottieAnimationView;->isAnimating()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 13
    .line 14
    invoke-virtual {v0}, Lcom/kwad/lottie/LottieAnimationView;->Ps()V

    .line 15
    .line 16
    .line 17
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 18
    .line 19
    const/16 v1, 0x8

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method private BO()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/kwad/lottie/LottieAnimationView;->Ps()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 7
    .line 8
    const/16 v1, 0x8

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const-string v1, "\u7f51\u7edc\u9519\u8bef"

    .line 18
    .line 19
    invoke-static {v0, v1}, Lcom/kwad/sdk/utils/ab;->ab(Landroid/content/Context;Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method private BP()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Lcom/kwad/sdk/utils/an;->isNetworkConnected(Landroid/content/Context;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/b/a;->BM()V

    .line 12
    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/b/a;->BN()V

    .line 16
    .line 17
    .line 18
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->mHandler:Landroid/os/Handler;

    .line 19
    .line 20
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->awu:Ljava/lang/Runnable;

    .line 21
    .line 22
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->mHandler:Landroid/os/Handler;

    .line 26
    .line 27
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->awu:Ljava/lang/Runnable;

    .line 28
    .line 29
    const-wide/16 v2, 0x2710

    .line 30
    .line 31
    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/b/a;Z)Z
    .locals 0

    .line 2
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/b/b/a;->avF:Z

    return p1
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/b/a;->BO()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/view/ViewGroup;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awr:Landroid/view/ViewGroup;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/b/b/a;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/b/b/a;->avF:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/b/a;->BP()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic h(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/b/a;->BN()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic i(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic j(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic k(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic l(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic m(Lcom/kwad/components/ct/detail/b/b/a;)Ljava/lang/Runnable;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awu:Ljava/lang/Runnable;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic n(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/os/Handler;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/b/a;->mHandler:Landroid/os/Handler;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic o(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/b/a;->BM()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic p(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    const-string v0, "DetailLoadingPresenter"

    .line 5
    .line 6
    const-string v1, "onBind"

    .line 7
    .line 8
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->aws:Landroid/widget/TextView;

    .line 12
    .line 13
    new-instance v1, Lcom/kwad/components/ct/detail/b/b/a$2;

    .line 14
    .line 15
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/b/b/a$2;-><init>(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 22
    .line 23
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 24
    .line 25
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->amo:Lcom/kwad/components/core/j/a;

    .line 26
    .line 27
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 31
    .line 32
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 33
    .line 34
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 35
    .line 36
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final onCreate()V
    .locals 4

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Landroid/os/Handler;

    .line 5
    .line 6
    invoke-direct {v0}, Landroid/os/Handler;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->mHandler:Landroid/os/Handler;

    .line 10
    .line 11
    sget v0, Lcom/kwad/sdk/R$id;->ksad_error_container:I

    .line 12
    .line 13
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    check-cast v0, Landroid/view/ViewGroup;

    .line 18
    .line 19
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awr:Landroid/view/ViewGroup;

    .line 20
    .line 21
    sget v0, Lcom/kwad/sdk/R$id;->ksad_retry_btn:I

    .line 22
    .line 23
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    check-cast v0, Landroid/widget/TextView;

    .line 28
    .line 29
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->aws:Landroid/widget/TextView;

    .line 30
    .line 31
    sget v0, Lcom/kwad/sdk/R$id;->ksad_center_loading_animation_view:I

    .line 32
    .line 33
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    check-cast v0, Lcom/kwad/lottie/LottieAnimationView;

    .line 38
    .line 39
    sget v1, Lcom/kwad/sdk/R$id;->ksad_bottom_loading_animation_view:I

    .line 40
    .line 41
    invoke-virtual {p0, v1}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    check-cast v1, Lcom/kwad/lottie/LottieAnimationView;

    .line 46
    .line 47
    sget v2, Lcom/kwad/sdk/R$raw;->ksad_detail_loading_amin_bottom:I

    .line 48
    .line 49
    const/16 v3, 0x8

    .line 50
    .line 51
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    const/4 v0, 0x0

    .line 55
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 56
    .line 57
    .line 58
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 59
    .line 60
    const/4 v0, 0x1

    .line 61
    invoke-virtual {v1, v0}, Lcom/kwad/lottie/LottieAnimationView;->setRepeatMode(I)V

    .line 62
    .line 63
    .line 64
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 65
    .line 66
    const/4 v3, -0x1

    .line 67
    invoke-virtual {v1, v3}, Lcom/kwad/lottie/LottieAnimationView;->setRepeatCount(I)V

    .line 68
    .line 69
    .line 70
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 71
    .line 72
    invoke-virtual {v1, v2}, Lcom/kwad/lottie/LottieAnimationView;->setAnimation(I)V

    .line 73
    .line 74
    .line 75
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 76
    .line 77
    invoke-virtual {v1, v0}, Lcom/kwad/lottie/LottieAnimationView;->setRepeatMode(I)V

    .line 78
    .line 79
    .line 80
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->awq:Lcom/kwad/lottie/LottieAnimationView;

    .line 81
    .line 82
    invoke-virtual {v0, v3}, Lcom/kwad/lottie/LottieAnimationView;->setRepeatCount(I)V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a;->mHandler:Landroid/os/Handler;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    const/4 v1, 0x0

    .line 9
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 13
    .line 14
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 15
    .line 16
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->amo:Lcom/kwad/components/core/j/a;

    .line 17
    .line 18
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 22
    .line 23
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 28
    .line 29
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 30
    .line 31
    .line 32
    :cond_1
    return-void
.end method
