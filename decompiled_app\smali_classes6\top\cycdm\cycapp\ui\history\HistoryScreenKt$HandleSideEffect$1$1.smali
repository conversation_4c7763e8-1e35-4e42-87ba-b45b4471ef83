.class final Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->C(Landroidx/paging/compose/LazyPagingItems;Landroidx/compose/runtime/Composer;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ltop/cycdm/cycapp/ui/history/a;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Ltop/cycdm/cycapp/ui/history/a;",
        "it",
        "Lkotlin/t;",
        "<anonymous>",
        "(Ltop/cycdm/cycapp/ui/history/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.history.HistoryScreenKt$HandleSideEffect$1$1"
    f = "HistoryScreen.kt"
    i = {}
    l = {
        0x108
    }
    m = "invokeSuspend"
    n = {}
    s = {}
.end annotation


# instance fields
.field final synthetic $lazyPagingItems:Landroidx/paging/compose/LazyPagingItems;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/paging/compose/LazyPagingItems<",
            "Ltop/cycdm/model/j;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $showClearDialog:Landroidx/compose/runtime/MutableState;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/MutableState<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $showDeleteDialog:Landroidx/compose/runtime/MutableState;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/MutableState<",
            "Ltop/cycdm/model/j;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $snackbar:Landroidx/compose/material3/SnackbarHostState;

.field synthetic L$0:Ljava/lang/Object;

.field label:I


# direct methods
.method public constructor <init>(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;Landroidx/compose/material3/SnackbarHostState;Landroidx/paging/compose/LazyPagingItems;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/MutableState<",
            "Ljava/lang/Boolean;",
            ">;",
            "Landroidx/compose/runtime/MutableState<",
            "Ltop/cycdm/model/j;",
            ">;",
            "Landroidx/compose/material3/SnackbarHostState;",
            "Landroidx/paging/compose/LazyPagingItems<",
            "Ltop/cycdm/model/j;",
            ">;",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$showClearDialog:Landroidx/compose/runtime/MutableState;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$showDeleteDialog:Landroidx/compose/runtime/MutableState;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$snackbar:Landroidx/compose/material3/SnackbarHostState;

    iput-object p4, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$lazyPagingItems:Landroidx/paging/compose/LazyPagingItems;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$showClearDialog:Landroidx/compose/runtime/MutableState;

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$showDeleteDialog:Landroidx/compose/runtime/MutableState;

    iget-object v3, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$snackbar:Landroidx/compose/material3/SnackbarHostState;

    iget-object v4, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$lazyPagingItems:Landroidx/paging/compose/LazyPagingItems;

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;-><init>(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;Landroidx/compose/material3/SnackbarHostState;Landroidx/paging/compose/LazyPagingItems;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ltop/cycdm/cycapp/ui/history/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->invoke(Ltop/cycdm/cycapp/ui/history/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ltop/cycdm/cycapp/ui/history/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltop/cycdm/cycapp/ui/history/a;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Ltop/cycdm/cycapp/ui/history/a;

    .line 30
    .line 31
    instance-of v1, p1, Ltop/cycdm/cycapp/ui/history/a$a;

    .line 32
    .line 33
    if-eqz v1, :cond_2

    .line 34
    .line 35
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$showClearDialog:Landroidx/compose/runtime/MutableState;

    .line 36
    .line 37
    invoke-static {v2}, Ln5/a;->a(Z)Ljava/lang/Boolean;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-interface {p1, v0}, Landroidx/compose/runtime/MutableState;->setValue(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 45
    .line 46
    :goto_0
    move-object v8, p0

    .line 47
    goto :goto_1

    .line 48
    :cond_2
    instance-of v1, p1, Ltop/cycdm/cycapp/ui/history/a$b;

    .line 49
    .line 50
    if-eqz v1, :cond_3

    .line 51
    .line 52
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$showDeleteDialog:Landroidx/compose/runtime/MutableState;

    .line 53
    .line 54
    check-cast p1, Ltop/cycdm/cycapp/ui/history/a$b;

    .line 55
    .line 56
    invoke-virtual {p1}, Ltop/cycdm/cycapp/ui/history/a$b;->a()Ltop/cycdm/model/j;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-interface {v0, p1}, Landroidx/compose/runtime/MutableState;->setValue(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 64
    .line 65
    goto :goto_0

    .line 66
    :cond_3
    instance-of v1, p1, Ltop/cycdm/cycapp/ui/history/a$d;

    .line 67
    .line 68
    if-eqz v1, :cond_4

    .line 69
    .line 70
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$snackbar:Landroidx/compose/material3/SnackbarHostState;

    .line 71
    .line 72
    check-cast p1, Ltop/cycdm/cycapp/ui/history/a$d;

    .line 73
    .line 74
    invoke-virtual {p1}, Ltop/cycdm/cycapp/ui/history/a$d;->a()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v4

    .line 78
    iput v2, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->label:I

    .line 79
    .line 80
    const/4 v5, 0x0

    .line 81
    const/4 v6, 0x0

    .line 82
    const/4 v7, 0x0

    .line 83
    const/16 v9, 0xe

    .line 84
    .line 85
    const/4 v10, 0x0

    .line 86
    move-object v8, p0

    .line 87
    invoke-static/range {v3 .. v10}, Landroidx/compose/material3/SnackbarHostState;->showSnackbar$default(Landroidx/compose/material3/SnackbarHostState;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/material3/SnackbarDuration;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    if-ne p1, v0, :cond_5

    .line 92
    .line 93
    return-object v0

    .line 94
    :cond_4
    move-object v8, p0

    .line 95
    instance-of p1, p1, Ltop/cycdm/cycapp/ui/history/a$c;

    .line 96
    .line 97
    if-eqz p1, :cond_6

    .line 98
    .line 99
    iget-object p1, v8, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;->$lazyPagingItems:Landroidx/paging/compose/LazyPagingItems;

    .line 100
    .line 101
    invoke-virtual {p1}, Landroidx/paging/compose/LazyPagingItems;->refresh()V

    .line 102
    .line 103
    .line 104
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 105
    .line 106
    :cond_5
    :goto_1
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 107
    .line 108
    return-object p1

    .line 109
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 110
    .line 111
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 112
    .line 113
    .line 114
    throw p1
.end method
