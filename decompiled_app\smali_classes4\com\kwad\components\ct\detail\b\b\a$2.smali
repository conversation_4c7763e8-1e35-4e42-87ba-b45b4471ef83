.class final Lcom/kwad/components/ct/detail/b/b/a$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/b/b/a;->T()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awv:Lcom/kwad/components/ct/detail/b/b/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$2;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$2;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/b/a;->i(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-static {p1}, Lcom/kwad/sdk/utils/an;->isNetworkConnected(Landroid/content/Context;)Z

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-nez p1, :cond_0

    .line 12
    .line 13
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$2;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 14
    .line 15
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/b/a;->j(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    const-string v0, "\u7f51\u7edc\u9519\u8bef"

    .line 20
    .line 21
    invoke-static {p1, v0}, Lcom/kwad/sdk/utils/ab;->ab(Landroid/content/Context;Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void

    .line 25
    :cond_0
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$2;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 26
    .line 27
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/b/a;->k(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iget-object p1, p1, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 32
    .line 33
    invoke-virtual {p1}, Lcom/kwad/components/ct/detail/e/a;->isPlaying()Z

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    if-nez p1, :cond_1

    .line 38
    .line 39
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$2;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 40
    .line 41
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/b/a;->l(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iget-object p1, p1, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 46
    .line 47
    invoke-virtual {p1}, Lcom/kwad/components/ct/detail/e/a;->Cb()V

    .line 48
    .line 49
    .line 50
    :cond_1
    return-void
.end method
