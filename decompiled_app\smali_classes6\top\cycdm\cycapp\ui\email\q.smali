.class public final synthetic Ltop/cycdm/cycapp/ui/email/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/State;


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/runtime/State;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/q;->a:Landroidx/compose/runtime/State;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/q;->a:Landroidx/compose/runtime/State;

    invoke-static {v0}, Ltop/cycdm/cycapp/ui/email/EmailScreenKt;->e(Landroidx/compose/runtime/State;)Z

    move-result v0

    invoke-static {v0}, <PERSON>ja<PERSON>/lang/<PERSON>;->valueOf(Z)Ljava/lang/Bo<PERSON>an;

    move-result-object v0

    return-object v0
.end method
