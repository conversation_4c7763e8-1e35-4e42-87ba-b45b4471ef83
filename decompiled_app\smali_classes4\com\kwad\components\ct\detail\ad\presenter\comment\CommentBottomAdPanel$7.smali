.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$7;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->initView()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$7;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$7;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->e(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/view/View$OnClickListener;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$7;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->e(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/view/View$OnClickListener;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0, p1}, Landroid/view/View$OnClickListener;->onClick(Landroid/view/View;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    return-void
.end method
