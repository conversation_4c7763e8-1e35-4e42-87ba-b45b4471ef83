.class final Lcom/kwad/components/ct/detail/b/a$2;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avc:Lcom/kwad/components/ct/detail/b/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/a$2;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pR()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pR()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$2;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method
