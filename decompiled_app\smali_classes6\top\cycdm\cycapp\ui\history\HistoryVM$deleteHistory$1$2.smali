.class public final Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

.field public final synthetic b:I

.field public final synthetic c:Lorg/orbitmvi/orbit/syntax/simple/b;


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;ILorg/orbitmvi/orbit/syntax/simple/b;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iput p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->b:I

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->c:Lorg/orbitmvi/orbit/syntax/simple/b;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Ltop/cycdm/cycapp/ui/history/HistoryVM;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/history/w;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->f(Ltop/cycdm/cycapp/ui/history/HistoryVM;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/history/w;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Ltop/cycdm/cycapp/ui/history/HistoryVM;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/history/w;
    .locals 0

    .line 1
    invoke-virtual {p1}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ltop/cycdm/cycapp/ui/history/w;

    .line 6
    .line 7
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->access$get_deleteList$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0}, Lkotlin/collections/f0;->b1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    invoke-virtual {p1, p0}, Ltop/cycdm/cycapp/ui/history/w;->a(Ljava/util/List;)Ltop/cycdm/cycapp/ui/history/w;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    return-object p0
.end method


# virtual methods
.method public final c(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6

    .line 1
    instance-of v0, p2, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;

    .line 7
    .line 8
    iget v1, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x3

    .line 34
    const/4 v4, 0x2

    .line 35
    const/4 v5, 0x1

    .line 36
    if-eqz v2, :cond_4

    .line 37
    .line 38
    if-eq v2, v5, :cond_3

    .line 39
    .line 40
    if-eq v2, v4, :cond_2

    .line 41
    .line 42
    if-ne v2, v3, :cond_1

    .line 43
    .line 44
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    goto :goto_4

    .line 48
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw p1

    .line 56
    :cond_2
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    goto :goto_2

    .line 60
    :cond_3
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    goto :goto_1

    .line 64
    :cond_4
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    if-eqz p1, :cond_7

    .line 68
    .line 69
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 70
    .line 71
    invoke-static {p1}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->access$get_deleteList$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Ljava/util/List;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    iget p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->b:I

    .line 76
    .line 77
    invoke-static {p2}, Ln5/a;->e(I)Ljava/lang/Integer;

    .line 78
    .line 79
    .line 80
    move-result-object p2

    .line 81
    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 82
    .line 83
    .line 84
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->c:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 85
    .line 86
    iget-object p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 87
    .line 88
    new-instance v2, Ltop/cycdm/cycapp/ui/history/y;

    .line 89
    .line 90
    invoke-direct {v2, p2}, Ltop/cycdm/cycapp/ui/history/y;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;)V

    .line 91
    .line 92
    .line 93
    iput v5, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    .line 94
    .line 95
    invoke-static {p1, v2, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    if-ne p1, v1, :cond_5

    .line 100
    .line 101
    goto :goto_3

    .line 102
    :cond_5
    :goto_1
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->c:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 103
    .line 104
    new-instance p2, Ltop/cycdm/cycapp/ui/history/a$d;

    .line 105
    .line 106
    const-string v2, "\u5220\u9664\u6210\u529f"

    .line 107
    .line 108
    invoke-direct {p2, v2}, Ltop/cycdm/cycapp/ui/history/a$d;-><init>(Ljava/lang/String;)V

    .line 109
    .line 110
    .line 111
    iput v4, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    .line 112
    .line 113
    invoke-static {p1, p2, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->d(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    if-ne p1, v1, :cond_6

    .line 118
    .line 119
    goto :goto_3

    .line 120
    :cond_6
    :goto_2
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 121
    .line 122
    return-object p1

    .line 123
    :cond_7
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->c:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 124
    .line 125
    new-instance p2, Ltop/cycdm/cycapp/ui/history/a$d;

    .line 126
    .line 127
    const-string v2, "\u5220\u9664\u5931\u8d25"

    .line 128
    .line 129
    invoke-direct {p2, v2}, Ltop/cycdm/cycapp/ui/history/a$d;-><init>(Ljava/lang/String;)V

    .line 130
    .line 131
    .line 132
    iput v3, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2$emit$1;->label:I

    .line 133
    .line 134
    invoke-static {p1, p2, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->d(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 135
    .line 136
    .line 137
    move-result-object p1

    .line 138
    if-ne p1, v1, :cond_8

    .line 139
    .line 140
    :goto_3
    return-object v1

    .line 141
    :cond_8
    :goto_4
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 142
    .line 143
    return-object p1
.end method

.method public bridge synthetic emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method
