.class public final synthetic Ltop/cycdm/cycapp/ui/email/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/email/EmailVM;

.field public final synthetic b:Landroidx/compose/runtime/MutableState;

.field public final synthetic c:Landroidx/compose/runtime/MutableState;


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/e;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/e;->b:Landroidx/compose/runtime/MutableState;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/email/e;->c:Landroidx/compose/runtime/MutableState;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/e;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/e;->b:Landroidx/compose/runtime/MutableState;

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/e;->c:Landroidx/compose/runtime/MutableState;

    check-cast p1, Landroidx/compose/foundation/text/KeyboardActionScope;

    invoke-static {v0, v1, v2, p1}, Ltop/cycdm/cycapp/ui/email/EmailScreenKt;->o(Ltop/cycdm/cycapp/ui/email/EmailVM;Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;Landroidx/compose/foundation/text/KeyboardActionScope;)Lkotlin/t;

    move-result-object p1

    return-object p1
.end method
