.class public interface abstract Lcom/beizi/fusion/NativeAdListener;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/beizi/fusion/a;


# virtual methods
.method public abstract onAdClick()V
.end method

.method public abstract onAdClosed()V
.end method

.method public abstract onAdClosed(Landroid/view/View;)V
.end method

.method public abstract onAdFailed(I)V
.end method

.method public abstract onAdLoaded(Landroid/view/View;)V
.end method

.method public abstract onAdShown()V
.end method
