.class public final synthetic Lcom/sigmob/sdk/base/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lcom/sigmob/sdk/base/m$1;

.field public final synthetic b:Lcom/sigmob/sdk/videoAd/p;

.field public final synthetic c:Lcom/sigmob/windad/WindAdError;

.field public final synthetic d:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lcom/sigmob/sdk/base/m$1;Lcom/sigmob/sdk/videoAd/p;Lcom/sigmob/windad/WindAdError;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/sigmob/sdk/base/w;->a:Lcom/sigmob/sdk/base/m$1;

    iput-object p2, p0, Lcom/sigmob/sdk/base/w;->b:Lcom/sigmob/sdk/videoAd/p;

    iput-object p3, p0, Lcom/sigmob/sdk/base/w;->c:Lcom/sigmob/windad/WindAdError;

    iput-object p4, p0, Lcom/sigmob/sdk/base/w;->d:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/sigmob/sdk/base/w;->a:Lcom/sigmob/sdk/base/m$1;

    iget-object v1, p0, Lcom/sigmob/sdk/base/w;->b:Lcom/sigmob/sdk/videoAd/p;

    iget-object v2, p0, Lcom/sigmob/sdk/base/w;->c:Lcom/sigmob/windad/WindAdError;

    iget-object v3, p0, Lcom/sigmob/sdk/base/w;->d:Ljava/lang/String;

    invoke-static {v0, v1, v2, v3}, Lcom/sigmob/sdk/base/m$1;->j(Lcom/sigmob/sdk/base/m$1;Lcom/sigmob/sdk/videoAd/p;Lcom/sigmob/windad/WindAdError;Ljava/lang/String;)V

    return-void
.end method
