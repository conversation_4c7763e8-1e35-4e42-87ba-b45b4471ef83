.class public final Lcom/kwad/components/ct/detail/ad/presenter/a/a;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field private alX:Lcom/kwad/components/ct/detail/e/a;

.field private amD:Landroid/widget/TextView;

.field private amE:Landroid/view/ViewGroup;

.field private amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

.field private amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

.field private amH:Landroid/animation/ValueAnimator;

.field private amI:Landroid/animation/ValueAnimator;

.field private amJ:Landroid/animation/Animator;

.field private amo:Lcom/kwad/components/core/j/a;

.field private dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

.field private fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

.field private mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/a$2;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amo:Lcom/kwad/components/core/j/a;

    .line 10
    .line 11
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/a$3;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a$3;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 17
    .line 18
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/a$4;

    .line 19
    .line 20
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a$4;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    .line 24
    .line 25
    return-void
.end method

.method private G(Landroid/view/View;)Landroid/animation/AnimatorSet;
    .locals 8
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const v1, 0x3f147ae1    # 0.58f

    .line 3
    .line 4
    .line 5
    const/high16 v2, 0x3f800000    # 1.0f

    .line 6
    .line 7
    invoke-static {v0, v0, v1, v2}, Landroidx/core/view/animation/PathInterpolatorCompat;->create(FFFF)Landroid/view/animation/Interpolator;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const v3, 0x3ed70a3d    # 0.42f

    .line 12
    .line 13
    .line 14
    invoke-static {v3, v0, v2, v2}, Landroidx/core/view/animation/PathInterpolatorCompat;->create(FFFF)Landroid/view/animation/Interpolator;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    const/4 v2, 0x2

    .line 19
    new-array v3, v2, [F

    .line 20
    .line 21
    fill-array-data v3, :array_0

    .line 22
    .line 23
    .line 24
    const/16 v4, 0x12c

    .line 25
    .line 26
    invoke-static {p1, v4, v1, v3}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->a(Landroid/view/View;ILandroid/view/animation/Interpolator;[F)Landroid/animation/AnimatorSet;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    new-array v5, v2, [F

    .line 31
    .line 32
    fill-array-data v5, :array_1

    .line 33
    .line 34
    .line 35
    invoke-static {p1, v4, v0, v5}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->a(Landroid/view/View;ILandroid/view/animation/Interpolator;[F)Landroid/animation/AnimatorSet;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    new-array v6, v2, [F

    .line 40
    .line 41
    fill-array-data v6, :array_2

    .line 42
    .line 43
    .line 44
    invoke-static {p1, v4, v1, v6}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->a(Landroid/view/View;ILandroid/view/animation/Interpolator;[F)Landroid/animation/AnimatorSet;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    new-array v6, v2, [F

    .line 49
    .line 50
    fill-array-data v6, :array_3

    .line 51
    .line 52
    .line 53
    invoke-static {p1, v4, v0, v6}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->a(Landroid/view/View;ILandroid/view/animation/Interpolator;[F)Landroid/animation/AnimatorSet;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    new-instance v4, Landroid/animation/AnimatorSet;

    .line 58
    .line 59
    invoke-direct {v4}, Landroid/animation/AnimatorSet;-><init>()V

    .line 60
    .line 61
    .line 62
    const-wide/16 v6, 0x12c

    .line 63
    .line 64
    invoke-virtual {v4, v6, v7}, Landroid/animation/AnimatorSet;->setDuration(J)Landroid/animation/AnimatorSet;

    .line 65
    .line 66
    .line 67
    new-instance v6, Lcom/kwad/components/ct/detail/ad/presenter/a/a$1;

    .line 68
    .line 69
    invoke-direct {v6, p0, p1, v4}, Lcom/kwad/components/ct/detail/ad/presenter/a/a$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/a;Landroid/view/View;Landroid/animation/AnimatorSet;)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v4, v6}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 73
    .line 74
    .line 75
    const/4 p1, 0x4

    .line 76
    new-array p1, p1, [Landroid/animation/Animator;

    .line 77
    .line 78
    const/4 v6, 0x0

    .line 79
    aput-object v3, p1, v6

    .line 80
    .line 81
    const/4 v3, 0x1

    .line 82
    aput-object v5, p1, v3

    .line 83
    .line 84
    aput-object v1, p1, v2

    .line 85
    .line 86
    const/4 v1, 0x3

    .line 87
    aput-object v0, p1, v1

    .line 88
    .line 89
    invoke-virtual {v4, p1}, Landroid/animation/AnimatorSet;->playSequentially([Landroid/animation/Animator;)V

    .line 90
    .line 91
    .line 92
    return-object v4

    .line 93
    :array_0
    .array-data 4
        0x3f800000    # 1.0f
        0x3f733333    # 0.95f
    .end array-data

    .line 94
    .line 95
    .line 96
    .line 97
    .line 98
    .line 99
    .line 100
    .line 101
    :array_1
    .array-data 4
        0x3f733333    # 0.95f
        0x3f800000    # 1.0f
    .end array-data

    .line 102
    .line 103
    .line 104
    .line 105
    .line 106
    .line 107
    .line 108
    .line 109
    :array_2
    .array-data 4
        0x3f800000    # 1.0f
        0x3f733333    # 0.95f
    .end array-data

    .line 110
    .line 111
    .line 112
    .line 113
    .line 114
    .line 115
    .line 116
    .line 117
    :array_3
    .array-data 4
        0x3f733333    # 0.95f
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/a/a;Landroid/animation/Animator;)Landroid/animation/Animator;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amJ:Landroid/animation/Animator;

    return-object p1
.end method

.method private static varargs a(Landroid/view/View;ILandroid/view/animation/Interpolator;[F)Landroid/animation/AnimatorSet;
    .locals 1

    .line 4
    const-string p1, "scaleX"

    invoke-static {p0, p1, p3}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Ljava/lang/String;[F)Landroid/animation/ObjectAnimator;

    move-result-object p1

    .line 5
    invoke-virtual {p1, p2}, Landroid/animation/Animator;->setInterpolator(Landroid/animation/TimeInterpolator;)V

    .line 6
    const-string v0, "scaleY"

    invoke-static {p0, v0, p3}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Ljava/lang/String;[F)Landroid/animation/ObjectAnimator;

    move-result-object p0

    .line 7
    invoke-virtual {p0, p2}, Landroid/animation/Animator;->setInterpolator(Landroid/animation/TimeInterpolator;)V

    .line 8
    new-instance p2, Landroid/animation/AnimatorSet;

    invoke-direct {p2}, Landroid/animation/AnimatorSet;-><init>()V

    const/4 p3, 0x2

    .line 9
    new-array p3, p3, [Landroid/animation/Animator;

    const/4 v0, 0x0

    aput-object p1, p3, v0

    const/4 p1, 0x1

    aput-object p0, p3, p1

    invoke-virtual {p2, p3}, Landroid/animation/AnimatorSet;->playTogether([Landroid/animation/Animator;)V

    const-wide/16 p0, 0x12c

    .line 10
    invoke-virtual {p2, p0, p1}, Landroid/animation/AnimatorSet;->setDuration(J)Landroid/animation/AnimatorSet;

    return-object p2
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/a/a;Landroid/view/View;)Landroid/animation/AnimatorSet;
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->G(Landroid/view/View;)Landroid/animation/AnimatorSet;

    move-result-object p0

    return-object p0
.end method

.method private a(IIZ)V
    .locals 3

    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    instance-of v0, v0, Lcom/kwad/components/ct/detail/ad/a;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    .line 12
    invoke-static {}, Lcom/kwad/components/core/t/d;->sM()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 13
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object v0

    iget-object v2, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    iget-object v2, v2, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    invoke-virtual {v0, v2, v1}, Lcom/kwad/components/ct/e/b;->b(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 14
    :cond_0
    new-instance v0, Lcom/kwad/components/core/e/d/a$a;

    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    move-result-object v2

    invoke-direct {v0, v2}, Lcom/kwad/components/core/e/d/a$a;-><init>(Landroid/content/Context;)V

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 15
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->aC(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/components/core/e/d/a$a;

    move-result-object v0

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 16
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->b(Lcom/kwad/components/core/e/d/c;)Lcom/kwad/components/core/e/d/a$a;

    move-result-object v0

    .line 17
    invoke-virtual {v0, p2}, Lcom/kwad/components/core/e/d/a$a;->ap(I)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p2

    .line 18
    invoke-virtual {p2, p3}, Lcom/kwad/components/core/e/d/a$a;->aq(Z)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p2

    .line 19
    invoke-virtual {p2, p1}, Lcom/kwad/components/core/e/d/a$a;->ao(I)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p1

    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 20
    invoke-virtual {p2}, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;->getTouchCoords()Lcom/kwad/sdk/utils/ai$a;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/kwad/components/core/e/d/a$a;->d(Lcom/kwad/sdk/utils/ai$a;)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p1

    .line 21
    invoke-virtual {p1, v1}, Lcom/kwad/components/core/e/d/a$a;->as(Z)Lcom/kwad/components/core/e/d/a$a;

    move-result-object p1

    .line 22
    invoke-static {p1}, Lcom/kwad/components/core/e/d/a;->a(Lcom/kwad/components/core/e/d/a$a;)I

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V
    .locals 0

    .line 3
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xW()V

    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xX()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xT()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xR()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)Lcom/kwad/sdk/core/response/model/AdInfo;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)Lcom/kwad/sdk/core/view/AdDownloadProgressBar;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic h(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)Lcom/kwad/sdk/core/view/AdDownloadProgressBar;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic i(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)Landroid/animation/Animator;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amJ:Landroid/animation/Animator;

    .line 2
    .line 3
    return-object p0
.end method

.method private xO()V
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 2
    .line 3
    const-string v1, "#4D36384B"

    .line 4
    .line 5
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setProgressDrawable(I)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 13
    .line 14
    const-string v1, "#66FFFFFF"

    .line 15
    .line 16
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setTextColor(I)V

    .line 21
    .line 22
    .line 23
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 24
    .line 25
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    sget v1, Lcom/kwad/sdk/R$drawable;->ksad_btn_arrow_gray:I

    .line 34
    .line 35
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    const/high16 v1, 0x40000000    # 2.0f

    .line 44
    .line 45
    invoke-static {v0, v1}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 46
    .line 47
    .line 48
    move-result v7

    .line 49
    const/4 v3, 0x0

    .line 50
    const/4 v4, 0x0

    .line 51
    const/4 v6, 0x0

    .line 52
    invoke-virtual/range {v2 .. v7}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->a(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;I)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method private xP()V
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 2
    .line 3
    const-string v1, "#CCFFFFFF"

    .line 4
    .line 5
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setTextColor(I)V

    .line 10
    .line 11
    .line 12
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 13
    .line 14
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    sget v1, Lcom/kwad/sdk/R$drawable;->ksad_btn_arrow_light:I

    .line 23
    .line 24
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 25
    .line 26
    .line 27
    move-result-object v5

    .line 28
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    const/high16 v1, 0x40000000    # 2.0f

    .line 33
    .line 34
    invoke-static {v0, v1}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 35
    .line 36
    .line 37
    move-result v7

    .line 38
    const/4 v3, 0x0

    .line 39
    const/4 v4, 0x0

    .line 40
    const/4 v6, 0x0

    .line 41
    invoke-virtual/range {v2 .. v7}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->a(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;I)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method private xQ()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/a;->cy(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    const-string v0, "\u514d\u8d39\u67e5\u770b"

    .line 14
    .line 15
    :cond_0
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 16
    .line 17
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/a;->cw(Lcom/kwad/sdk/core/response/model/AdInfo;)Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-eqz v1, :cond_1

    .line 22
    .line 23
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amD:Landroid/widget/TextView;

    .line 24
    .line 25
    invoke-virtual {v2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 26
    .line 27
    .line 28
    :cond_1
    if-eqz v1, :cond_2

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    goto :goto_0

    .line 32
    :cond_2
    const/16 v0, 0x8

    .line 33
    .line 34
    :goto_0
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amD:Landroid/widget/TextView;

    .line 35
    .line 36
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 37
    .line 38
    .line 39
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amD:Landroid/widget/TextView;

    .line 40
    .line 41
    invoke-virtual {v0, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method private xR()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_1

    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xY()V

    .line 19
    .line 20
    .line 21
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xS()V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 25
    .line 26
    const/high16 v1, 0x3f800000    # 1.0f

    .line 27
    .line 28
    invoke-virtual {v0, v1}, Landroid/view/View;->setAlpha(F)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 32
    .line 33
    const/4 v1, 0x0

    .line 34
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 35
    .line 36
    .line 37
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 38
    .line 39
    invoke-virtual {v0, p0}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amE:Landroid/view/ViewGroup;

    .line 43
    .line 44
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 45
    .line 46
    .line 47
    move-result-object v2

    .line 48
    const/high16 v3, 0x421c0000    # 39.0f

    .line 49
    .line 50
    invoke-static {v2, v3}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    invoke-static {v0, v1, v2}, Lcom/kwad/components/core/t/r;->e(Landroid/view/View;II)Landroid/animation/ValueAnimator;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amH:Landroid/animation/ValueAnimator;

    .line 59
    .line 60
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 61
    .line 62
    .line 63
    :cond_1
    :goto_0
    return-void
.end method

.method private xS()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amH:Landroid/animation/ValueAnimator;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->removeAllListeners()V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amH:Landroid/animation/ValueAnimator;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 11
    .line 12
    .line 13
    :cond_0
    return-void
.end method

.method private xT()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xU()V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amE:Landroid/view/ViewGroup;

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 20
    .line 21
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/a;->cv(Lcom/kwad/sdk/core/response/model/AdInfo;)Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amE:Landroid/view/ViewGroup;

    .line 28
    .line 29
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/a/a$5;

    .line 30
    .line 31
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a$5;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/a;)V

    .line 32
    .line 33
    .line 34
    const-wide/16 v2, 0x3e8

    .line 35
    .line 36
    invoke-virtual {v0, v1, v2, v3}, Landroid/view/View;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 37
    .line 38
    .line 39
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 40
    .line 41
    invoke-virtual {v0, p0}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 42
    .line 43
    .line 44
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 45
    .line 46
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 47
    .line 48
    invoke-static {v0, v1}, Lcom/kwad/components/core/t/r;->c(Landroid/view/View;Landroid/view/View;)Landroid/animation/ValueAnimator;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amI:Landroid/animation/ValueAnimator;

    .line 53
    .line 54
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method private xU()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amI:Landroid/animation/ValueAnimator;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->removeAllListeners()V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amI:Landroid/animation/ValueAnimator;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 11
    .line 12
    .line 13
    :cond_0
    return-void
.end method

.method private xV()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xS()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xU()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method private xW()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xU()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 5
    .line 6
    const/high16 v1, 0x3f800000    # 1.0f

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setAlpha(F)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 12
    .line 13
    const/16 v1, 0x8

    .line 14
    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amE:Landroid/view/ViewGroup;

    .line 19
    .line 20
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method private xX()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xS()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 5
    .line 6
    const/high16 v1, 0x3f800000    # 1.0f

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setAlpha(F)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 12
    .line 13
    const/16 v1, 0x8

    .line 14
    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method private xY()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    const/16 v1, 0x13

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-static {v0, v1, v2}, Lcom/kwad/sdk/core/adlog/c;->b(Lcom/kwad/sdk/core/response/model/AdTemplate;ILorg/json/JSONObject;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 15
    .line 16
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 17
    .line 18
    iget-object v2, v1, Lcom/kwad/components/ct/detail/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 19
    .line 20
    iput-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 21
    .line 22
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 23
    .line 24
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 25
    .line 26
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 27
    .line 28
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/a;->aI(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {v1, v0}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setText(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 36
    .line 37
    const/16 v1, 0x8

    .line 38
    .line 39
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 43
    .line 44
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 45
    .line 46
    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/a;->aI(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v0, v2}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setText(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 54
    .line 55
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mAdInfo:Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 56
    .line 57
    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/a;->bD(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v2

    .line 61
    invoke-static {v2}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    .line 62
    .line 63
    .line 64
    move-result v2

    .line 65
    invoke-virtual {v0, v2}, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;->setProgressDrawable(I)V

    .line 66
    .line 67
    .line 68
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 69
    .line 70
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 71
    .line 72
    .line 73
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 74
    .line 75
    if-eqz v0, :cond_0

    .line 76
    .line 77
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    .line 78
    .line 79
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/c;->b(Lcom/kwad/sdk/api/KsAppDownloadListener;)V

    .line 80
    .line 81
    .line 82
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 83
    .line 84
    if-eqz v0, :cond_1

    .line 85
    .line 86
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 87
    .line 88
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 89
    .line 90
    .line 91
    :cond_1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xQ()V

    .line 92
    .line 93
    .line 94
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 95
    .line 96
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 97
    .line 98
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amo:Lcom/kwad/components/core/j/a;

    .line 99
    .line 100
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 101
    .line 102
    .line 103
    return-void
.end method

.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amD:Landroid/widget/TextView;

    .line 2
    .line 3
    if-ne p1, v0, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x2

    .line 6
    const/4 v0, 0x0

    .line 7
    const/16 v1, 0x6f

    .line 8
    .line 9
    invoke-direct {p0, v1, p1, v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->a(IIZ)V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    const/4 p1, 0x1

    .line 14
    invoke-direct {p0, p1, p1, p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->a(IIZ)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_root_container:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 13
    .line 14
    sget v0, Lcom/kwad/sdk/R$id;->ksad_progress_container:I

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    check-cast v0, Landroid/view/ViewGroup;

    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amE:Landroid/view/ViewGroup;

    .line 23
    .line 24
    sget v0, Lcom/kwad/sdk/R$id;->ksad_translate_progress:I

    .line 25
    .line 26
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 31
    .line 32
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amF:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 33
    .line 34
    sget v0, Lcom/kwad/sdk/R$id;->ksad_light_progress:I

    .line 35
    .line 36
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    check-cast v0, Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 41
    .line 42
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amG:Lcom/kwad/sdk/core/view/AdDownloadProgressBar;

    .line 43
    .line 44
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_adtag:I

    .line 45
    .line 46
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    check-cast v0, Landroid/widget/TextView;

    .line 51
    .line 52
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amD:Landroid/widget/TextView;

    .line 53
    .line 54
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xO()V

    .line 55
    .line 56
    .line 57
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xP()V

    .line 58
    .line 59
    .line 60
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->xV()V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->dA:Lcom/kwad/sdk/api/KsAppDownloadListener;

    .line 12
    .line 13
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/c;->c(Lcom/kwad/sdk/api/KsAppDownloadListener;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 21
    .line 22
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 23
    .line 24
    .line 25
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 26
    .line 27
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 28
    .line 29
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amo:Lcom/kwad/components/core/j/a;

    .line 30
    .line 31
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 32
    .line 33
    .line 34
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amJ:Landroid/animation/Animator;

    .line 35
    .line 36
    if-eqz v0, :cond_2

    .line 37
    .line 38
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 39
    .line 40
    .line 41
    const/4 v0, 0x0

    .line 42
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;->amJ:Landroid/animation/Animator;

    .line 43
    .line 44
    :cond_2
    return-void
.end method
