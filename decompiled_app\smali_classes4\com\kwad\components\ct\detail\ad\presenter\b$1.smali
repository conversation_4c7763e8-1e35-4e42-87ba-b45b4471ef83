.class final Lcom/kwad/components/ct/detail/ad/presenter/b$1;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amq:Lcom/kwad/components/ct/detail/ad/presenter/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->b(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 8
    .line 9
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;->a(Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final pR()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->b(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 8
    .line 9
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;->b(Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;)Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 20
    .line 21
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/b;->b(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/b$1;->amq:Lcom/kwad/components/ct/detail/ad/presenter/b;

    .line 26
    .line 27
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/b;)Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout;->c(Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;)V

    .line 32
    .line 33
    .line 34
    :cond_0
    return-void
.end method
