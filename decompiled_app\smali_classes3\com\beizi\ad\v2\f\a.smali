.class public Lcom/beizi/ad/v2/f/a;
.super Lcom/beizi/ad/v2/a/a;
.source "SourceFile"


# direct methods
.method public constructor <init>(Landroid/content/Context;Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/String;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/a/a;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    invoke-virtual {p2, v0, v0, v0, v0}, Landroid/view/View;->setPadding(IIII)V

    .line 6
    .line 7
    .line 8
    new-instance v0, Lcom/beizi/ad/v2/f/b;

    .line 9
    .line 10
    invoke-direct {v0, p1, p2, p3, p4}, Lcom/beizi/ad/v2/f/b;-><init>(Landroid/content/Context;Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    iput-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    .line 14
    .line 15
    return-void
.end method


# virtual methods
.method public a(II)V
    .locals 2

    .line 8
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 9
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/f/b;

    if-nez v1, :cond_1

    :goto_0
    return-void

    .line 10
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/f/b;

    invoke-virtual {v0, p1, p2}, Lcom/beizi/ad/v2/f/b;->a(II)V

    return-void
.end method

.method public a(IIII)V
    .locals 0

    .line 1
    return-void
.end method

.method public a(Landroid/view/View$OnTouchListener;)V
    .locals 2

    .line 11
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 12
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/f/b;

    if-nez v1, :cond_1

    :goto_0
    return-void

    .line 13
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/f/b;

    invoke-virtual {v0, p1}, Lcom/beizi/ad/v2/f/b;->a(Landroid/view/View$OnTouchListener;)V

    return-void
.end method

.method public a(Landroid/view/View;)V
    .locals 2

    .line 5
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 6
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/f/b;

    if-nez v1, :cond_1

    :goto_0
    return-void

    .line 7
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/f/b;

    invoke-virtual {v0, p1}, Lcom/beizi/ad/v2/f/b;->a(Landroid/view/View;)V

    return-void
.end method

.method public a(Lcom/beizi/ad/a;)V
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/f/b;

    if-nez v1, :cond_1

    :goto_0
    return-void

    .line 4
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/f/b;

    invoke-virtual {v0, p1}, Lcom/beizi/ad/v2/f/b;->a(Lcom/beizi/ad/a;)V

    return-void
.end method

.method public a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 2

    .line 14
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 15
    :cond_0
    instance-of v0, v0, Lcom/beizi/ad/v2/f/b;

    if-nez v0, :cond_1

    :goto_0
    return-void

    .line 16
    :cond_1
    new-instance v0, Lcom/beizi/ad/model/c;

    invoke-direct {v0}, Lcom/beizi/ad/model/c;-><init>()V

    .line 17
    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 18
    invoke-virtual {v0, p1}, Lcom/beizi/ad/model/c;->a(Ljava/lang/String;)V

    .line 19
    :cond_2
    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_3

    .line 20
    invoke-virtual {v0, p2}, Lcom/beizi/ad/model/c;->b(Ljava/lang/String;)V

    .line 21
    :cond_3
    invoke-static {p3}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_4

    .line 22
    invoke-virtual {v0, p3}, Lcom/beizi/ad/model/c;->c(Ljava/lang/String;)V

    .line 23
    :cond_4
    invoke-static {p4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_5

    .line 24
    invoke-virtual {v0, p4}, Lcom/beizi/ad/model/c;->d(Ljava/lang/String;)V

    .line 25
    :cond_5
    invoke-static {p5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_6

    .line 26
    invoke-virtual {v0, p5}, Lcom/beizi/ad/model/c;->e(Ljava/lang/String;)V

    .line 27
    :cond_6
    invoke-static {p6}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_7

    .line 28
    invoke-virtual {v0, p6}, Lcom/beizi/ad/model/c;->f(Ljava/lang/String;)V

    .line 29
    :cond_7
    invoke-static {p7}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_8

    .line 30
    invoke-virtual {v0, p7}, Lcom/beizi/ad/model/c;->g(Ljava/lang/String;)V

    .line 31
    :cond_8
    invoke-static {p8}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_9

    .line 32
    invoke-virtual {v0, p8}, Lcom/beizi/ad/model/c;->h(Ljava/lang/String;)V

    .line 33
    :cond_9
    iget-object p1, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    check-cast p1, Lcom/beizi/ad/v2/f/b;

    invoke-virtual {p1, v0, p9}, Lcom/beizi/ad/v2/f/b;->a(Lcom/beizi/ad/model/c;I)V

    return-void
.end method

.method public b(Landroid/view/View$OnTouchListener;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/f/b;

    .line 7
    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    :goto_0
    return-void

    .line 11
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/f/b;

    .line 12
    .line 13
    invoke-virtual {v0, p1}, Lcom/beizi/ad/v2/f/b;->b(Landroid/view/View$OnTouchListener;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public m()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/a;->a:Lcom/beizi/ad/v2/a/b;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    instance-of v1, v0, Lcom/beizi/ad/v2/f/b;

    .line 7
    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    :goto_0
    return-void

    .line 11
    :cond_1
    check-cast v0, Lcom/beizi/ad/v2/f/b;

    .line 12
    .line 13
    invoke-virtual {v0}, Lcom/beizi/ad/v2/f/b;->u()V

    .line 14
    .line 15
    .line 16
    return-void
.end method
