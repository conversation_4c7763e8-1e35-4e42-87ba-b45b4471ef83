.class public interface abstract Landroidx/media3/datasource/cache/CacheDataSource$EventListener;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/datasource/cache/CacheDataSource;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "EventListener"
.end annotation


# virtual methods
.method public abstract onCacheIgnored(I)V
.end method

.method public abstract onCachedBytesRead(JJ)V
.end method
