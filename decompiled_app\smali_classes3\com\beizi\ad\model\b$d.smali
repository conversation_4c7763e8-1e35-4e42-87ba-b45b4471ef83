.class public Lcom/beizi/ad/model/b$d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/beizi/ad/model/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "d"
.end annotation


# instance fields
.field private a:Ljava/lang/String;

.field private b:Lcom/beizi/ad/model/b$b;

.field private c:Lcom/beizi/ad/model/b$c;

.field private d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$a;",
            ">;"
        }
    .end annotation
.end field

.field private e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$g;",
            ">;"
        }
    .end annotation
.end field

.field private f:Ljava/lang/String;

.field private g:Ljava/lang/String;

.field private h:Lcom/beizi/ad/model/b$e;

.field private i:Ljava/lang/String;

.field private j:I

.field private k:Lcom/beizi/ad/model/b$k;

.field private l:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->a:Ljava/lang/String;

    return-object v0
.end method

.method public a(I)V
    .locals 0

    .line 7
    iput p1, p0, Lcom/beizi/ad/model/b$d;->j:I

    return-void
.end method

.method public a(Lcom/beizi/ad/model/b$b;)V
    .locals 0

    .line 3
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->b:Lcom/beizi/ad/model/b$b;

    return-void
.end method

.method public a(Lcom/beizi/ad/model/b$c;)V
    .locals 0

    .line 4
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->c:Lcom/beizi/ad/model/b$c;

    return-void
.end method

.method public a(Lcom/beizi/ad/model/b$e;)V
    .locals 0

    .line 6
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->h:Lcom/beizi/ad/model/b$e;

    return-void
.end method

.method public a(Lcom/beizi/ad/model/b$k;)V
    .locals 0

    .line 8
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->k:Lcom/beizi/ad/model/b$k;

    return-void
.end method

.method public a(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->a:Ljava/lang/String;

    return-void
.end method

.method public a(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$a;",
            ">;)V"
        }
    .end annotation

    .line 5
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->d:Ljava/util/List;

    return-void
.end method

.method public b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->g:Ljava/lang/String;

    return-object v0
.end method

.method public b(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->g:Ljava/lang/String;

    return-void
.end method

.method public c()Lcom/beizi/ad/model/b$b;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->b:Lcom/beizi/ad/model/b$b;

    return-object v0
.end method

.method public c(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->f:Ljava/lang/String;

    return-void
.end method

.method public d()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->d:Ljava/util/List;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method public d(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->i:Ljava/lang/String;

    return-void
.end method

.method public e()Lcom/beizi/ad/model/b$c;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->c:Lcom/beizi/ad/model/b$c;

    return-object v0
.end method

.method public e(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$d;->l:Ljava/lang/String;

    return-void
.end method

.method public f()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$a;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->d:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public g()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$g;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->e:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public h()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->e:Ljava/util/List;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    return v0

    .line 7
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method

.method public i()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->f:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public j()Lcom/beizi/ad/model/b$e;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->h:Lcom/beizi/ad/model/b$e;

    .line 2
    .line 3
    return-object v0
.end method

.method public k()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->i:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public l()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/beizi/ad/model/b$d;->j:I

    .line 2
    .line 3
    return v0
.end method

.method public m()Lcom/beizi/ad/model/b$k;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->k:Lcom/beizi/ad/model/b$k;

    .line 2
    .line 3
    return-object v0
.end method

.method public n()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$d;->l:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method
