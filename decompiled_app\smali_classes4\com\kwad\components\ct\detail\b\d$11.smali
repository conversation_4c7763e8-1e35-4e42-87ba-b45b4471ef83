.class final Lcom/kwad/components/ct/detail/b/d$11;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->c(Lcom/kwad/components/ct/detail/b/d;Z)Z

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->l(Lcom/kwad/components/ct/detail/b/d;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 13
    .line 14
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->k(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/sdk/widget/swipe/c;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 21
    .line 22
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->k(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/sdk/widget/swipe/c;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 27
    .line 28
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/d;->m(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/sdk/widget/swipe/a;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    invoke-virtual {v0, v2}, Lcom/kwad/sdk/widget/swipe/c;->a(Lcom/kwad/sdk/widget/swipe/a;)V

    .line 33
    .line 34
    .line 35
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 36
    .line 37
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->getSourceType()I

    .line 42
    .line 43
    .line 44
    move-result v0

    .line 45
    const-string v2, "becomesAttachedOnPageSelected mPosition"

    .line 46
    .line 47
    const-string v3, "DetailProfileSlidePresenter"

    .line 48
    .line 49
    if-ne v0, v1, :cond_2

    .line 50
    .line 51
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 52
    .line 53
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    check-cast v0, Lcom/kwad/sdk/lib/widget/a/d;

    .line 62
    .line 63
    if-eqz v0, :cond_1

    .line 64
    .line 65
    invoke-virtual {v0}, Lcom/kwad/sdk/lib/widget/a/d;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    check-cast v1, Lcom/kwad/components/ct/home/<USER>/b;

    .line 70
    .line 71
    invoke-virtual {v1}, Lcom/kwad/components/ct/home/<USER>/b;->Gs()Lcom/kwad/components/ct/home/<USER>/c;

    .line 72
    .line 73
    .line 74
    move-result-object v4

    .line 75
    invoke-virtual {v1}, Lcom/kwad/components/ct/home/<USER>/b;->Gt()I

    .line 76
    .line 77
    .line 78
    move-result v5

    .line 79
    iget-object v6, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 80
    .line 81
    invoke-static {v6}, Lcom/kwad/components/ct/detail/b/d;->o(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/api/a/a/c;

    .line 82
    .line 83
    .line 84
    move-result-object v6

    .line 85
    iget-object v7, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 86
    .line 87
    invoke-static {v7}, Lcom/kwad/components/ct/detail/b/d;->n(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 88
    .line 89
    .line 90
    move-result-object v7

    .line 91
    invoke-interface {v6, v5, v7}, Lcom/kwad/components/ct/api/a/a/c;->b(ILcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 92
    .line 93
    .line 94
    iget-object v5, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 95
    .line 96
    invoke-static {v5}, Lcom/kwad/components/ct/detail/b/d;->p(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 97
    .line 98
    .line 99
    move-result-object v5

    .line 100
    invoke-virtual {v1, v5}, Lcom/kwad/components/ct/home/<USER>/b;->S(Landroid/view/View;)V

    .line 101
    .line 102
    .line 103
    iget-object v5, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 104
    .line 105
    invoke-static {v5}, Lcom/kwad/components/ct/detail/b/d;->n(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 106
    .line 107
    .line 108
    move-result-object v5

    .line 109
    iget-object v6, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 110
    .line 111
    invoke-static {v6}, Lcom/kwad/components/ct/detail/b/d;->q(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/e/a;

    .line 112
    .line 113
    .line 114
    move-result-object v6

    .line 115
    invoke-virtual {v1, v5, v6}, Lcom/kwad/components/ct/home/<USER>/b;->a(Lcom/kwad/components/ct/response/model/CtAdTemplate;Lcom/kwad/components/ct/detail/e/a;)V

    .line 116
    .line 117
    .line 118
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 119
    .line 120
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->n(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    invoke-virtual {v4, v1}, Lcom/kwad/components/ct/home/<USER>/c;->v(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 125
    .line 126
    .line 127
    const-string v1, "scrollVerticallyToPosition becomesAttachedOnPageSelected"

    .line 128
    .line 129
    invoke-static {v3, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 130
    .line 131
    .line 132
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 133
    .line 134
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 135
    .line 136
    .line 137
    move-result-object v1

    .line 138
    iget-object v5, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 139
    .line 140
    invoke-static {v5}, Lcom/kwad/components/ct/detail/b/d;->r(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Runnable;

    .line 141
    .line 142
    .line 143
    move-result-object v5

    .line 144
    invoke-virtual {v1, v5}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 145
    .line 146
    .line 147
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 148
    .line 149
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 150
    .line 151
    .line 152
    move-result-object v1

    .line 153
    iget-object v5, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 154
    .line 155
    invoke-static {v5}, Lcom/kwad/components/ct/detail/b/d;->s(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Runnable;

    .line 156
    .line 157
    .line 158
    move-result-object v5

    .line 159
    invoke-virtual {v1, v5}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 160
    .line 161
    .line 162
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 163
    .line 164
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 165
    .line 166
    .line 167
    move-result-object v1

    .line 168
    iget-object v5, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 169
    .line 170
    invoke-static {v5}, Lcom/kwad/components/ct/detail/b/d;->r(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Runnable;

    .line 171
    .line 172
    .line 173
    move-result-object v5

    .line 174
    invoke-virtual {v1, v5}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 175
    .line 176
    .line 177
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 178
    .line 179
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->t(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/api/a/a/b;

    .line 180
    .line 181
    .line 182
    move-result-object v1

    .line 183
    invoke-virtual {v4, v1}, Lcom/kwad/components/ct/home/<USER>/a;->a(Lcom/kwad/components/ct/api/a/a/b;)V

    .line 184
    .line 185
    .line 186
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 187
    .line 188
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 189
    .line 190
    .line 191
    move-result-object v1

    .line 192
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 193
    .line 194
    invoke-static {v4}, Lcom/kwad/components/ct/detail/b/d;->u(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView$OnScrollListener;

    .line 195
    .line 196
    .line 197
    move-result-object v4

    .line 198
    invoke-virtual {v1, v4}, Landroidx/recyclerview/widget/RecyclerView;->addOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$OnScrollListener;)V

    .line 199
    .line 200
    .line 201
    :cond_1
    new-instance v1, Ljava/lang/StringBuilder;

    .line 202
    .line 203
    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 204
    .line 205
    .line 206
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 207
    .line 208
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/d;->v(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/c;

    .line 209
    .line 210
    .line 211
    move-result-object v2

    .line 212
    iget v2, v2, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 213
    .line 214
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 215
    .line 216
    .line 217
    const-string v2, "--mSourceType=PROFILE--headerFooterAdapter="

    .line 218
    .line 219
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 220
    .line 221
    .line 222
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 223
    .line 224
    .line 225
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 226
    .line 227
    .line 228
    move-result-object v0

    .line 229
    invoke-static {v3, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 230
    .line 231
    .line 232
    goto :goto_0

    .line 233
    :cond_2
    new-instance v0, Ljava/lang/StringBuilder;

    .line 234
    .line 235
    invoke-direct {v0, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 236
    .line 237
    .line 238
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 239
    .line 240
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->w(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/c;

    .line 241
    .line 242
    .line 243
    move-result-object v1

    .line 244
    iget v1, v1, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 245
    .line 246
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 247
    .line 248
    .line 249
    const-string v1, "--mSourceType=FEED--headerFooterAdapter="

    .line 250
    .line 251
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 252
    .line 253
    .line 254
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 255
    .line 256
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 257
    .line 258
    .line 259
    move-result-object v1

    .line 260
    invoke-virtual {v1}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 261
    .line 262
    .line 263
    move-result-object v1

    .line 264
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 265
    .line 266
    .line 267
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 268
    .line 269
    .line 270
    move-result-object v0

    .line 271
    invoke-static {v3, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 272
    .line 273
    .line 274
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 275
    .line 276
    const/4 v1, 0x0

    .line 277
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->a(Lcom/kwad/components/ct/detail/b/d;Z)Z

    .line 278
    .line 279
    .line 280
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 281
    .line 282
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 283
    .line 284
    .line 285
    move-result-object v0

    .line 286
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 287
    .line 288
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->x(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager$a;

    .line 289
    .line 290
    .line 291
    move-result-object v1

    .line 292
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->a(Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager$a;)V

    .line 293
    .line 294
    .line 295
    return-void
.end method

.method public final pR()V
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/d;->c(Lcom/kwad/components/ct/detail/b/d;Z)Z

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->k(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/sdk/widget/swipe/c;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Lcom/kwad/sdk/widget/swipe/c;->aqf()Lcom/kwad/sdk/widget/swipe/a;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 18
    .line 19
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->m(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/sdk/widget/swipe/a;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    const/4 v2, 0x0

    .line 24
    if-ne v0, v1, :cond_0

    .line 25
    .line 26
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 27
    .line 28
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->k(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/sdk/widget/swipe/c;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {v0, v2}, Lcom/kwad/sdk/widget/swipe/c;->a(Lcom/kwad/sdk/widget/swipe/a;)V

    .line 33
    .line 34
    .line 35
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 36
    .line 37
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 42
    .line 43
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->r(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Runnable;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 48
    .line 49
    .line 50
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 51
    .line 52
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    check-cast v0, Lcom/kwad/sdk/lib/widget/a/d;

    .line 61
    .line 62
    if-eqz v0, :cond_3

    .line 63
    .line 64
    invoke-virtual {v0}, Lcom/kwad/sdk/lib/widget/a/d;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    check-cast v1, Lcom/kwad/components/ct/home/<USER>/b;

    .line 69
    .line 70
    invoke-virtual {v1}, Lcom/kwad/components/ct/home/<USER>/b;->Gs()Lcom/kwad/components/ct/home/<USER>/c;

    .line 71
    .line 72
    .line 73
    move-result-object v3

    .line 74
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 75
    .line 76
    invoke-static {v4}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 77
    .line 78
    .line 79
    move-result-object v4

    .line 80
    invoke-virtual {v4}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->getSourceType()I

    .line 81
    .line 82
    .line 83
    move-result v4

    .line 84
    if-nez v4, :cond_2

    .line 85
    .line 86
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 87
    .line 88
    invoke-static {v4}, Lcom/kwad/components/ct/detail/b/d;->y(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 89
    .line 90
    .line 91
    move-result-object v4

    .line 92
    if-eqz v4, :cond_1

    .line 93
    .line 94
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 95
    .line 96
    invoke-static {v4}, Lcom/kwad/components/ct/detail/b/d;->y(Lcom/kwad/components/ct/detail/b/d;)Landroid/view/View;

    .line 97
    .line 98
    .line 99
    move-result-object v4

    .line 100
    invoke-virtual {v0, v4}, Lcom/kwad/sdk/lib/widget/a/d;->removeFooterView(Landroid/view/View;)Z

    .line 101
    .line 102
    .line 103
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 104
    .line 105
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->z(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/lottie/LottieAnimationView;

    .line 106
    .line 107
    .line 108
    move-result-object v0

    .line 109
    invoke-virtual {v0}, Lcom/kwad/lottie/LottieAnimationView;->Ps()V

    .line 110
    .line 111
    .line 112
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 113
    .line 114
    invoke-static {v0, v2}, Lcom/kwad/components/ct/detail/b/d;->a(Lcom/kwad/components/ct/detail/b/d;Landroid/view/View;)Landroid/view/View;

    .line 115
    .line 116
    .line 117
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 118
    .line 119
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->t(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/api/a/a/b;

    .line 120
    .line 121
    .line 122
    move-result-object v0

    .line 123
    invoke-virtual {v3, v0}, Lcom/kwad/components/ct/home/<USER>/a;->b(Lcom/kwad/components/ct/api/a/a/b;)V

    .line 124
    .line 125
    .line 126
    invoke-virtual {v3}, Lcom/kwad/components/ct/home/<USER>/c;->release()V

    .line 127
    .line 128
    .line 129
    invoke-virtual {v1}, Lcom/kwad/sdk/lib/widget/a/c;->rl()V

    .line 130
    .line 131
    .line 132
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 133
    .line 134
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 135
    .line 136
    .line 137
    move-result-object v0

    .line 138
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 139
    .line 140
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->u(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView$OnScrollListener;

    .line 141
    .line 142
    .line 143
    move-result-object v1

    .line 144
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->removeOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$OnScrollListener;)V

    .line 145
    .line 146
    .line 147
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 148
    .line 149
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 150
    .line 151
    .line 152
    move-result-object v0

    .line 153
    invoke-virtual {v0, v2}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 154
    .line 155
    .line 156
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 157
    .line 158
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->A(Lcom/kwad/components/ct/detail/b/d;)Landroid/os/Handler;

    .line 159
    .line 160
    .line 161
    move-result-object v0

    .line 162
    invoke-virtual {v0, v2}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    .line 163
    .line 164
    .line 165
    goto :goto_0

    .line 166
    :cond_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 167
    .line 168
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->t(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/api/a/a/b;

    .line 169
    .line 170
    .line 171
    move-result-object v0

    .line 172
    invoke-virtual {v3, v0}, Lcom/kwad/components/ct/home/<USER>/a;->b(Lcom/kwad/components/ct/api/a/a/b;)V

    .line 173
    .line 174
    .line 175
    invoke-virtual {v3}, Lcom/kwad/components/ct/home/<USER>/c;->release()V

    .line 176
    .line 177
    .line 178
    :cond_3
    :goto_0
    new-instance v0, Ljava/lang/StringBuilder;

    .line 179
    .line 180
    const-string v1, "becomesDetachedOnPageSelected mPosition"

    .line 181
    .line 182
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 183
    .line 184
    .line 185
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 186
    .line 187
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->B(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/c;

    .line 188
    .line 189
    .line 190
    move-result-object v1

    .line 191
    iget v1, v1, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 192
    .line 193
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 194
    .line 195
    .line 196
    const-string v1, "--mSourceType="

    .line 197
    .line 198
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 199
    .line 200
    .line 201
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 202
    .line 203
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 204
    .line 205
    .line 206
    move-result-object v1

    .line 207
    invoke-virtual {v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->getSourceType()I

    .line 208
    .line 209
    .line 210
    move-result v1

    .line 211
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 212
    .line 213
    .line 214
    const-string v1, "--headerFooterAdapter="

    .line 215
    .line 216
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 217
    .line 218
    .line 219
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 220
    .line 221
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 222
    .line 223
    .line 224
    move-result-object v1

    .line 225
    invoke-virtual {v1}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 226
    .line 227
    .line 228
    move-result-object v1

    .line 229
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 230
    .line 231
    .line 232
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 233
    .line 234
    .line 235
    move-result-object v0

    .line 236
    const-string v1, "DetailProfileSlidePresenter"

    .line 237
    .line 238
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 239
    .line 240
    .line 241
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 242
    .line 243
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 244
    .line 245
    .line 246
    move-result-object v0

    .line 247
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$11;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 248
    .line 249
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/d;->x(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager$a;

    .line 250
    .line 251
    .line 252
    move-result-object v1

    .line 253
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->b(Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager$a;)V

    .line 254
    .line 255
    .line 256
    return-void
.end method
