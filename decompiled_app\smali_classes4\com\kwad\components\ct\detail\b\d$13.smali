.class final Lcom/kwad/components/ct/detail/b/d$13;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/components/ct/api/a/a/b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(ZZII)V
    .locals 0

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 4
    .line 5
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->M(Lcom/kwad/components/ct/detail/b/d;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final c(ZII)V
    .locals 0

    .line 1
    new-instance p2, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    const-string p3, "updateFeed onFinishLoading mPosition"

    .line 4
    .line 5
    invoke-direct {p2, p3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object p3, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 9
    .line 10
    invoke-static {p3}, Lcom/kwad/components/ct/detail/b/d;->N(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/c;

    .line 11
    .line 12
    .line 13
    move-result-object p3

    .line 14
    iget p3, p3, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 15
    .line 16
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 17
    .line 18
    .line 19
    const-string p3, "--mSourceType="

    .line 20
    .line 21
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 22
    .line 23
    .line 24
    iget-object p3, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 25
    .line 26
    invoke-static {p3}, Lcom/kwad/components/ct/detail/b/d;->j(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 27
    .line 28
    .line 29
    move-result-object p3

    .line 30
    invoke-virtual {p3}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->getSourceType()I

    .line 31
    .line 32
    .line 33
    move-result p3

    .line 34
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 35
    .line 36
    .line 37
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object p2

    .line 41
    const-string p3, "DetailProfileSlidePresenter"

    .line 42
    .line 43
    invoke-static {p3, p2}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 44
    .line 45
    .line 46
    if-eqz p1, :cond_0

    .line 47
    .line 48
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 49
    .line 50
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->O(Lcom/kwad/components/ct/detail/b/d;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 54
    .line 55
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->L(Lcom/kwad/components/ct/detail/b/d;)V

    .line 56
    .line 57
    .line 58
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 59
    .line 60
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iget-object p2, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 65
    .line 66
    invoke-static {p2}, Lcom/kwad/components/ct/detail/b/d;->r(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Runnable;

    .line 67
    .line 68
    .line 69
    move-result-object p2

    .line 70
    invoke-virtual {p1, p2}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 71
    .line 72
    .line 73
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 74
    .line 75
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    iget-object p2, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 80
    .line 81
    invoke-static {p2}, Lcom/kwad/components/ct/detail/b/d;->s(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Runnable;

    .line 82
    .line 83
    .line 84
    move-result-object p2

    .line 85
    invoke-virtual {p1, p2}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 86
    .line 87
    .line 88
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 89
    .line 90
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iget-object p2, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 95
    .line 96
    invoke-static {p2}, Lcom/kwad/components/ct/detail/b/d;->s(Lcom/kwad/components/ct/detail/b/d;)Ljava/lang/Runnable;

    .line 97
    .line 98
    .line 99
    move-result-object p2

    .line 100
    invoke-virtual {p1, p2}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 101
    .line 102
    .line 103
    goto :goto_0

    .line 104
    :cond_0
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 105
    .line 106
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->O(Lcom/kwad/components/ct/detail/b/d;)V

    .line 107
    .line 108
    .line 109
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 110
    .line 111
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->L(Lcom/kwad/components/ct/detail/b/d;)V

    .line 112
    .line 113
    .line 114
    :goto_0
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 115
    .line 116
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->P(Lcom/kwad/components/ct/detail/b/d;)V

    .line 117
    .line 118
    .line 119
    return-void
.end method

.method public final onError(ILjava/lang/String;)V
    .locals 1

    .line 1
    iget-object p2, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    invoke-static {p2}, Lcom/kwad/components/ct/detail/b/d;->P(Lcom/kwad/components/ct/detail/b/d;)V

    .line 4
    .line 5
    .line 6
    iget-object p2, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 7
    .line 8
    invoke-static {p2}, Lcom/kwad/components/ct/detail/b/d;->h(Lcom/kwad/components/ct/detail/b/d;)Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    .line 11
    move-result-object p2

    .line 12
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    check-cast p2, Lcom/kwad/sdk/lib/widget/a/d;

    .line 17
    .line 18
    invoke-virtual {p2}, Lcom/kwad/sdk/lib/widget/a/d;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 19
    .line 20
    .line 21
    move-result-object p2

    .line 22
    check-cast p2, Lcom/kwad/components/ct/home/<USER>/b;

    .line 23
    .line 24
    invoke-virtual {p2}, Lcom/kwad/components/ct/home/<USER>/b;->Gs()Lcom/kwad/components/ct/home/<USER>/c;

    .line 25
    .line 26
    .line 27
    move-result-object p2

    .line 28
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 29
    .line 30
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/d;->Q(Lcom/kwad/components/ct/detail/b/d;)Z

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    if-nez v0, :cond_0

    .line 35
    .line 36
    invoke-virtual {p2}, Lcom/kwad/components/ct/home/<USER>/a;->isEmpty()Z

    .line 37
    .line 38
    .line 39
    move-result p2

    .line 40
    if-eqz p2, :cond_2

    .line 41
    .line 42
    :cond_0
    sget-object p2, Lcom/kwad/sdk/core/network/e;->bzR:Lcom/kwad/sdk/core/network/e;

    .line 43
    .line 44
    iget p2, p2, Lcom/kwad/sdk/core/network/e;->errorCode:I

    .line 45
    .line 46
    if-ne p2, p1, :cond_1

    .line 47
    .line 48
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 49
    .line 50
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->R(Lcom/kwad/components/ct/detail/b/d;)Landroid/content/Context;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    invoke-static {p1}, Lcom/kwad/sdk/utils/ab;->cH(Landroid/content/Context;)V

    .line 55
    .line 56
    .line 57
    return-void

    .line 58
    :cond_1
    sget-object p2, Lcom/kwad/sdk/core/network/e;->bAd:Lcom/kwad/sdk/core/network/e;

    .line 59
    .line 60
    iget p2, p2, Lcom/kwad/sdk/core/network/e;->errorCode:I

    .line 61
    .line 62
    if-ne p2, p1, :cond_3

    .line 63
    .line 64
    invoke-static {}, Lcom/kwad/sdk/core/config/e;->isShowTips()Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-eqz p1, :cond_2

    .line 69
    .line 70
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 71
    .line 72
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->S(Lcom/kwad/components/ct/detail/b/d;)Landroid/content/Context;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    invoke-static {p1}, Lcom/kwad/sdk/utils/ab;->cJ(Landroid/content/Context;)V

    .line 77
    .line 78
    .line 79
    :cond_2
    return-void

    .line 80
    :cond_3
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/d$13;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 81
    .line 82
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d;->T(Lcom/kwad/components/ct/detail/b/d;)Landroid/content/Context;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    invoke-static {p1}, Lcom/kwad/sdk/utils/ab;->cI(Landroid/content/Context;)V

    .line 87
    .line 88
    .line 89
    return-void
.end method
