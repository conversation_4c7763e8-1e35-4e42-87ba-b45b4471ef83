.class Lcom/beizi/ad/v2/c/b$3;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/c/b;->a(Lcom/beizi/ad/internal/f/c;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/c/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/c/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/c/b$3;->a:Lcom/beizi/ad/v2/c/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/c/b$3;->a:Lcom/beizi/ad/v2/c/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/beizi/ad/v2/c/b;->c(Lcom/beizi/ad/v2/c/b;)Lcom/beizi/ad/h;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/beizi/ad/v2/c/b$3;->a:Lcom/beizi/ad/v2/c/b;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/beizi/ad/v2/c/b;->c(Lcom/beizi/ad/v2/c/b;)Lcom/beizi/ad/h;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    const/4 v1, 0x3

    .line 16
    invoke-interface {v0, v1}, Lcom/beizi/ad/h;->a(I)V

    .line 17
    .line 18
    .line 19
    :cond_0
    return-void
.end method
