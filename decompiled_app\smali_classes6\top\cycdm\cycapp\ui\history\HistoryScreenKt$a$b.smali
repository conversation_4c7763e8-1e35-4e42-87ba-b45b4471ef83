.class public final Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function3;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;->b(Ltop/cycdm/model/j;Landroidx/compose/runtime/Composer;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ltop/cycdm/model/j;

.field public final synthetic b:Landroidx/navigation/NavHostController;


# direct methods
.method public constructor <init>(Ltop/cycdm/model/j;Landroidx/navigation/NavHostController;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->a:Ltop/cycdm/model/j;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->b:Landroidx/navigation/NavHostController;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Landroidx/navigation/NavHostController;Ltop/cycdm/model/j;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->c(Landroidx/navigation/NavHostController;Ltop/cycdm/model/j;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Landroidx/navigation/NavHostController;Ltop/cycdm/model/j;)Lkotlin/t;
    .locals 1

    .line 1
    invoke-virtual {p1}, Ltop/cycdm/model/j;->e()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p1}, Ltop/cycdm/model/j;->b()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-static {p0, v0, p1}, Ltop/cycdm/cycapp/ui/common/x0;->g(Landroidx/navigation/NavHostController;ILjava/lang/String;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 13
    .line 14
    return-object p0
.end method


# virtual methods
.method public final b(Landroidx/compose/foundation/layout/RowScope;Landroidx/compose/runtime/Composer;I)V
    .locals 3

    .line 1
    and-int/lit8 p1, p3, 0x11

    .line 2
    .line 3
    const/16 v0, 0x10

    .line 4
    .line 5
    if-ne p1, v0, :cond_1

    .line 6
    .line 7
    invoke-interface {p2}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-nez p1, :cond_0

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    invoke-interface {p2}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 15
    .line 16
    .line 17
    return-void

    .line 18
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    if-eqz p1, :cond_2

    .line 23
    .line 24
    const/4 p1, -0x1

    .line 25
    const-string v0, "top.cycdm.cycapp.ui.history.HistoryList.<anonymous>.<anonymous> (HistoryScreen.kt:119)"

    .line 26
    .line 27
    const v1, 0x453373a3

    .line 28
    .line 29
    .line 30
    invoke-static {v1, p3, p1, v0}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 31
    .line 32
    .line 33
    :cond_2
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->a:Ltop/cycdm/model/j;

    .line 34
    .line 35
    iget-object p3, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->b:Landroidx/navigation/NavHostController;

    .line 36
    .line 37
    invoke-interface {p2, p3}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    move-result p3

    .line 41
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->a:Ltop/cycdm/model/j;

    .line 42
    .line 43
    invoke-interface {p2, v0}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    or-int/2addr p3, v0

    .line 48
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->b:Landroidx/navigation/NavHostController;

    .line 49
    .line 50
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->a:Ltop/cycdm/model/j;

    .line 51
    .line 52
    invoke-interface {p2}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object v2

    .line 56
    if-nez p3, :cond_3

    .line 57
    .line 58
    sget-object p3, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 59
    .line 60
    invoke-virtual {p3}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object p3

    .line 64
    if-ne v2, p3, :cond_4

    .line 65
    .line 66
    :cond_3
    new-instance v2, Ltop/cycdm/cycapp/ui/history/v;

    .line 67
    .line 68
    invoke-direct {v2, v0, v1}, Ltop/cycdm/cycapp/ui/history/v;-><init>(Landroidx/navigation/NavHostController;Ltop/cycdm/model/j;)V

    .line 69
    .line 70
    .line 71
    invoke-interface {p2, v2}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 72
    .line 73
    .line 74
    :cond_4
    check-cast v2, Lkotlin/jvm/functions/Function0;

    .line 75
    .line 76
    const/4 p3, 0x0

    .line 77
    invoke-static {p1, v2, p2, p3, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->X(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V

    .line 78
    .line 79
    .line 80
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    if-eqz p1, :cond_5

    .line 85
    .line 86
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 87
    .line 88
    .line 89
    :cond_5
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/layout/RowScope;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/Composer;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a$b;->b(Landroidx/compose/foundation/layout/RowScope;Landroidx/compose/runtime/Composer;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 15
    .line 16
    return-object p1
.end method
