.class final Lcom/kwad/components/ct/detail/ad/presenter/e$3;
.super Lcom/kwad/sdk/core/i/d;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amC:Lcom/kwad/components/ct/detail/ad/presenter/e;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/e;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/sdk/core/i/d;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final aT()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->c(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-boolean v0, v0, Lcom/kwad/components/ct/response/model/CtAdTemplate;->isDrawAdHasLook:Z

    .line 8
    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->d(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/detail/c;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 18
    .line 19
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/a;->bo(Lcom/kwad/sdk/core/response/model/AdInfo;)Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 30
    .line 31
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->c(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    const/4 v1, 0x1

    .line 36
    iput-boolean v1, v0, Lcom/kwad/components/ct/response/model/CtAdTemplate;->isDrawAdHasLook:Z

    .line 37
    .line 38
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 39
    .line 40
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->c(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    const/16 v2, 0xa1

    .line 45
    .line 46
    const/4 v3, 0x0

    .line 47
    invoke-static {v0, v2, v3}, Lcom/kwad/sdk/core/adlog/c;->b(Lcom/kwad/sdk/core/response/model/AdTemplate;ILorg/json/JSONObject;)V

    .line 48
    .line 49
    .line 50
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 51
    .line 52
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;Z)V

    .line 53
    .line 54
    .line 55
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$3;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 56
    .line 57
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->c(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    iget-wide v1, v1, Lcom/kwad/sdk/core/response/model/AdTemplate;->posId:J

    .line 62
    .line 63
    invoke-static {v1, v2}, Lcom/kwad/components/ct/home/<USER>/b;->aa(J)I

    .line 64
    .line 65
    .line 66
    move-result v1

    .line 67
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->b(Lcom/kwad/components/ct/detail/ad/presenter/e;I)V

    .line 68
    .line 69
    .line 70
    :cond_0
    return-void
.end method
