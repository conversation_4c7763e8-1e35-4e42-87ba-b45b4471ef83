.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$6;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->initView()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$6;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 3

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$6;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    const/4 v0, 0x2

    .line 4
    const/4 v1, 0x0

    .line 5
    const/16 v2, 0x5b

    .line 6
    .line 7
    invoke-static {p1, v2, v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;IIZ)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
