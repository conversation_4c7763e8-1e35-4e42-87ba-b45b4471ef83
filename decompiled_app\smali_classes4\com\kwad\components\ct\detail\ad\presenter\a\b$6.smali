.class final Lcom/kwad/components/ct/detail/ad/presenter/a/b$6;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnKeyListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/a/b;->I(Landroid/view/View;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$6;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onKey(Landroid/view/View;ILandroid/view/KeyEvent;)Z
    .locals 0

    .line 1
    invoke-virtual {p3}, Landroid/view/KeyEvent;->getAction()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    const/4 p3, 0x1

    .line 6
    if-ne p1, p3, :cond_1

    .line 7
    .line 8
    const/4 p1, 0x4

    .line 9
    if-ne p2, p1, :cond_1

    .line 10
    .line 11
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$6;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 12
    .line 13
    invoke-virtual {p1}, Lcom/kwad/sdk/mvp/Presenter;->getRootView()Landroid/view/View;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-static {p1}, Lcom/kwad/sdk/c/a/a;->X(Landroid/view/View;)Z

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    if-eqz p1, :cond_0

    .line 22
    .line 23
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$6;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 24
    .line 25
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->z(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    iget-object p1, p1, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 30
    .line 31
    invoke-virtual {p1}, Lcom/kwad/sdk/api/core/fragment/KsFragment;->getActivity()Landroid/app/Activity;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-static {p1}, Lcom/kwad/sdk/c/a/a;->m(Landroid/app/Activity;)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$6;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 40
    .line 41
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->u(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 42
    .line 43
    .line 44
    :goto_0
    return p3

    .line 45
    :cond_1
    const/4 p1, 0x0

    .line 46
    return p1
.end method
