.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/grid/LazyGridScope;Ltop/cycdm/model/IndexVideoInner;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Landroidx/navigation/NavHostController;

.field public final synthetic b:Ltop/cycdm/model/c0;


# direct methods
.method public constructor <init>(Landroidx/navigation/NavHostController;Ltop/cycdm/model/c0;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/c0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 3

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;

    .line 2
    .line 3
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/c0;

    .line 4
    .line 5
    invoke-virtual {v1}, Ltop/cycdm/model/c0;->f()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/c0;

    .line 10
    .line 11
    invoke-virtual {v2}, Ltop/cycdm/model/c0;->d()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-static {v0, v1, v2}, Ltop/cycdm/cycapp/ui/common/x0;->g(Landroidx/navigation/NavHostController;ILjava/lang/String;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    .line 4
    sget-object v0, Lkotlin/t;->a:Lkotlin/t;

    .line 5
    .line 6
    return-object v0
.end method
