.class public final synthetic Ltop/cycdm/cycapp/ui/history/y;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/history/HistoryVM;


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/y;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/y;->a:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/a;

    invoke-static {v0, p1}, Ltop/cycdm/cycapp/ui/history/HistoryVM$deleteHistory$1$2;->a(Ltop/cycdm/cycapp/ui/history/HistoryVM;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/history/w;

    move-result-object p1

    return-object p1
.end method
