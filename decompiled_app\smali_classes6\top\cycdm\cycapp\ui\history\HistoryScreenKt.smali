.class public abstract Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$b;
    }
.end annotation


# direct methods
.method public static final A(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)Lkotlin/t;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-interface {p0, v0}, Landroidx/compose/runtime/MutableState;->setValue(Ljava/lang/Object;)V

    .line 3
    .line 4
    .line 5
    invoke-virtual {p2}, Ltop/cycdm/model/j;->e()I

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    invoke-virtual {p1, p0}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->deleteHistory(I)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/t;->a:<PERSON><PERSON><PERSON>/t;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final B(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->x(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final C(Landroidx/paging/compose/LazyPagingItems;Landroidx/compose/runtime/Composer;I)V
    .locals 18

    .line 1
    move-object/from16 v4, p0

    .line 2
    .line 3
    move/from16 v6, p2

    .line 4
    .line 5
    const v0, -0x190d32fd

    .line 6
    .line 7
    .line 8
    move-object/from16 v1, p1

    .line 9
    .line 10
    invoke-interface {v1, v0}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 11
    .line 12
    .line 13
    move-result-object v10

    .line 14
    and-int/lit8 v1, v6, 0x6

    .line 15
    .line 16
    const/4 v2, 0x2

    .line 17
    const/4 v3, 0x4

    .line 18
    if-nez v1, :cond_2

    .line 19
    .line 20
    and-int/lit8 v1, v6, 0x8

    .line 21
    .line 22
    if-nez v1, :cond_0

    .line 23
    .line 24
    invoke-interface {v10, v4}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    invoke-interface {v10, v4}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    :goto_0
    if-eqz v1, :cond_1

    .line 34
    .line 35
    move v1, v3

    .line 36
    goto :goto_1

    .line 37
    :cond_1
    move v1, v2

    .line 38
    :goto_1
    or-int/2addr v1, v6

    .line 39
    goto :goto_2

    .line 40
    :cond_2
    move v1, v6

    .line 41
    :goto_2
    and-int/lit8 v5, v1, 0x3

    .line 42
    .line 43
    if-ne v5, v2, :cond_4

    .line 44
    .line 45
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 46
    .line 47
    .line 48
    move-result v2

    .line 49
    if-nez v2, :cond_3

    .line 50
    .line 51
    goto :goto_3

    .line 52
    :cond_3
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 53
    .line 54
    .line 55
    goto/16 :goto_9

    .line 56
    .line 57
    :cond_4
    :goto_3
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 58
    .line 59
    .line 60
    move-result v2

    .line 61
    if-eqz v2, :cond_5

    .line 62
    .line 63
    const/4 v2, -0x1

    .line 64
    const-string v5, "top.cycdm.cycapp.ui.history.HandleSideEffect (HistoryScreen.kt:254)"

    .line 65
    .line 66
    invoke-static {v0, v1, v2, v5}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 67
    .line 68
    .line 69
    :cond_5
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    sget-object v2, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 74
    .line 75
    invoke-virtual {v2}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v5

    .line 79
    if-ne v0, v5, :cond_6

    .line 80
    .line 81
    new-instance v0, Ltop/cycdm/cycapp/ui/history/o;

    .line 82
    .line 83
    invoke-direct {v0}, Ltop/cycdm/cycapp/ui/history/o;-><init>()V

    .line 84
    .line 85
    .line 86
    invoke-interface {v10, v0}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 87
    .line 88
    .line 89
    :cond_6
    check-cast v0, Lkotlin/jvm/functions/Function0;

    .line 90
    .line 91
    const/4 v5, 0x0

    .line 92
    const/16 v7, 0x30

    .line 93
    .line 94
    const/4 v15, 0x1

    .line 95
    invoke-static {v5, v0, v10, v7, v15}, Ltop/cycdm/cycapp/ui/common/ExtensionKt;->a0(Ljava/lang/Object;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)Landroidx/compose/runtime/MutableState;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 100
    .line 101
    .line 102
    move-result-object v8

    .line 103
    invoke-virtual {v2}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 104
    .line 105
    .line 106
    move-result-object v9

    .line 107
    if-ne v8, v9, :cond_7

    .line 108
    .line 109
    new-instance v8, Ltop/cycdm/cycapp/ui/history/p;

    .line 110
    .line 111
    invoke-direct {v8}, Ltop/cycdm/cycapp/ui/history/p;-><init>()V

    .line 112
    .line 113
    .line 114
    invoke-interface {v10, v8}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 115
    .line 116
    .line 117
    :cond_7
    check-cast v8, Lkotlin/jvm/functions/Function0;

    .line 118
    .line 119
    invoke-static {v5, v8, v10, v7, v15}, Ltop/cycdm/cycapp/ui/common/ExtensionKt;->a0(Ljava/lang/Object;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)Landroidx/compose/runtime/MutableState;

    .line 120
    .line 121
    .line 122
    move-result-object v5

    .line 123
    const/4 v7, 0x0

    .line 124
    invoke-static {v10, v7}, Ltop/cycdm/cycapp/ui/common/ExtensionKt;->T(Landroidx/compose/runtime/Composer;I)Landroidx/compose/material3/SnackbarHostState;

    .line 125
    .line 126
    .line 127
    move-result-object v8

    .line 128
    const v9, 0x70b323c8

    .line 129
    .line 130
    .line 131
    invoke-interface {v10, v9}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 132
    .line 133
    .line 134
    sget-object v9, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 135
    .line 136
    sget v11, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 137
    .line 138
    invoke-virtual {v9, v10, v11}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 139
    .line 140
    .line 141
    move-result-object v9

    .line 142
    if-eqz v9, :cond_f

    .line 143
    .line 144
    invoke-static {v9, v10, v7}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 145
    .line 146
    .line 147
    move-result-object v11

    .line 148
    const v12, 0x671a9c9b

    .line 149
    .line 150
    .line 151
    invoke-interface {v10, v12}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 152
    .line 153
    .line 154
    instance-of v12, v9, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 155
    .line 156
    if-eqz v12, :cond_8

    .line 157
    .line 158
    move-object v12, v9

    .line 159
    check-cast v12, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 160
    .line 161
    invoke-interface {v12}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 162
    .line 163
    .line 164
    move-result-object v12

    .line 165
    goto :goto_4

    .line 166
    :cond_8
    sget-object v12, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 167
    .line 168
    :goto_4
    const v13, 0x9048

    .line 169
    .line 170
    .line 171
    const/4 v14, 0x0

    .line 172
    move/from16 v16, v7

    .line 173
    .line 174
    const-class v7, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 175
    .line 176
    move-object/from16 v17, v8

    .line 177
    .line 178
    move-object v8, v9

    .line 179
    const/4 v9, 0x0

    .line 180
    move-object v15, v12

    .line 181
    move-object v12, v10

    .line 182
    move-object v10, v11

    .line 183
    move-object v11, v15

    .line 184
    move-object/from16 v15, v17

    .line 185
    .line 186
    invoke-static/range {v7 .. v14}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 187
    .line 188
    .line 189
    move-result-object v7

    .line 190
    move-object v10, v12

    .line 191
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 192
    .line 193
    .line 194
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 195
    .line 196
    .line 197
    check-cast v7, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 198
    .line 199
    invoke-interface {v10, v5}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 200
    .line 201
    .line 202
    move-result v8

    .line 203
    invoke-interface {v10, v0}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 204
    .line 205
    .line 206
    move-result v9

    .line 207
    or-int/2addr v8, v9

    .line 208
    invoke-interface {v10, v15}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 209
    .line 210
    .line 211
    move-result v9

    .line 212
    or-int/2addr v8, v9

    .line 213
    and-int/lit8 v9, v1, 0xe

    .line 214
    .line 215
    if-eq v9, v3, :cond_a

    .line 216
    .line 217
    and-int/lit8 v1, v1, 0x8

    .line 218
    .line 219
    if-eqz v1, :cond_9

    .line 220
    .line 221
    invoke-interface {v10, v4}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 222
    .line 223
    .line 224
    move-result v1

    .line 225
    if-eqz v1, :cond_9

    .line 226
    .line 227
    goto :goto_5

    .line 228
    :cond_9
    const/4 v1, 0x0

    .line 229
    goto :goto_6

    .line 230
    :cond_a
    :goto_5
    const/4 v1, 0x1

    .line 231
    :goto_6
    or-int/2addr v1, v8

    .line 232
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v3

    .line 236
    if-nez v1, :cond_b

    .line 237
    .line 238
    invoke-virtual {v2}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 239
    .line 240
    .line 241
    move-result-object v1

    .line 242
    if-ne v3, v1, :cond_c

    .line 243
    .line 244
    :cond_b
    move-object v2, v0

    .line 245
    goto :goto_7

    .line 246
    :cond_c
    move-object v2, v0

    .line 247
    move-object v1, v5

    .line 248
    goto :goto_8

    .line 249
    :goto_7
    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;

    .line 250
    .line 251
    move-object v1, v5

    .line 252
    const/4 v5, 0x0

    .line 253
    move-object v3, v15

    .line 254
    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$HandleSideEffect$1$1;-><init>(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;Landroidx/compose/material3/SnackbarHostState;Landroidx/paging/compose/LazyPagingItems;Lkotlin/coroutines/e;)V

    .line 255
    .line 256
    .line 257
    invoke-interface {v10, v0}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 258
    .line 259
    .line 260
    move-object v3, v0

    .line 261
    :goto_8
    move-object v9, v3

    .line 262
    check-cast v9, Lkotlin/jvm/functions/Function2;

    .line 263
    .line 264
    const/4 v11, 0x0

    .line 265
    const/4 v12, 0x1

    .line 266
    const/4 v8, 0x0

    .line 267
    invoke-static/range {v7 .. v12}, Lorg/orbitmvi/orbit/compose/ContainerHostExtensionsKt;->d(Lorg/orbitmvi/orbit/b;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Landroidx/compose/runtime/Composer;II)V

    .line 268
    .line 269
    .line 270
    const/4 v0, 0x0

    .line 271
    invoke-static {v2, v10, v0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->x(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/Composer;I)V

    .line 272
    .line 273
    .line 274
    invoke-static {v1, v10, v0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->t(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/Composer;I)V

    .line 275
    .line 276
    .line 277
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 278
    .line 279
    .line 280
    move-result v0

    .line 281
    if-eqz v0, :cond_d

    .line 282
    .line 283
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 284
    .line 285
    .line 286
    :cond_d
    :goto_9
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 287
    .line 288
    .line 289
    move-result-object v0

    .line 290
    if-eqz v0, :cond_e

    .line 291
    .line 292
    new-instance v1, Ltop/cycdm/cycapp/ui/history/q;

    .line 293
    .line 294
    invoke-direct {v1, v4, v6}, Ltop/cycdm/cycapp/ui/history/q;-><init>(Landroidx/paging/compose/LazyPagingItems;I)V

    .line 295
    .line 296
    .line 297
    invoke-interface {v0, v1}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 298
    .line 299
    .line 300
    :cond_e
    return-void

    .line 301
    :cond_f
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 302
    .line 303
    const-string v1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 304
    .line 305
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 306
    .line 307
    .line 308
    throw v0
.end method

.method public static final D()Ltop/cycdm/model/j;
    .locals 1

    .line 1
    const/4 v0, 0x0

    return-object v0
.end method

.method public static final E()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    return v0
.end method

.method public static final F(Landroidx/paging/compose/LazyPagingItems;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->C(Landroidx/paging/compose/LazyPagingItems;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final G(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V
    .locals 96

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move/from16 v1, p3

    .line 4
    .line 5
    move/from16 v2, p4

    .line 6
    .line 7
    const/4 v3, 0x6

    .line 8
    const v4, -0x37d210c6

    .line 9
    .line 10
    .line 11
    move-object/from16 v5, p2

    .line 12
    .line 13
    invoke-interface {v5, v4}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 14
    .line 15
    .line 16
    move-result-object v8

    .line 17
    const/4 v11, 0x1

    .line 18
    and-int/lit8 v5, v2, 0x1

    .line 19
    .line 20
    const/4 v6, 0x2

    .line 21
    if-eqz v5, :cond_0

    .line 22
    .line 23
    or-int/lit8 v5, v1, 0x6

    .line 24
    .line 25
    goto :goto_1

    .line 26
    :cond_0
    and-int/lit8 v5, v1, 0x6

    .line 27
    .line 28
    if-nez v5, :cond_2

    .line 29
    .line 30
    invoke-interface {v8, v0}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    move-result v5

    .line 34
    if-eqz v5, :cond_1

    .line 35
    .line 36
    const/4 v5, 0x4

    .line 37
    goto :goto_0

    .line 38
    :cond_1
    move v5, v6

    .line 39
    :goto_0
    or-int/2addr v5, v1

    .line 40
    goto :goto_1

    .line 41
    :cond_2
    move v5, v1

    .line 42
    :goto_1
    and-int/lit8 v7, v2, 0x2

    .line 43
    .line 44
    const/16 v9, 0x20

    .line 45
    .line 46
    if-eqz v7, :cond_4

    .line 47
    .line 48
    or-int/lit8 v5, v5, 0x30

    .line 49
    .line 50
    :cond_3
    move-object/from16 v10, p1

    .line 51
    .line 52
    goto :goto_3

    .line 53
    :cond_4
    and-int/lit8 v10, v1, 0x30

    .line 54
    .line 55
    if-nez v10, :cond_3

    .line 56
    .line 57
    move-object/from16 v10, p1

    .line 58
    .line 59
    invoke-interface {v8, v10}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 60
    .line 61
    .line 62
    move-result v13

    .line 63
    if-eqz v13, :cond_5

    .line 64
    .line 65
    move v13, v9

    .line 66
    goto :goto_2

    .line 67
    :cond_5
    const/16 v13, 0x10

    .line 68
    .line 69
    :goto_2
    or-int/2addr v5, v13

    .line 70
    :goto_3
    and-int/lit8 v13, v5, 0x13

    .line 71
    .line 72
    const/16 v14, 0x12

    .line 73
    .line 74
    if-ne v13, v14, :cond_7

    .line 75
    .line 76
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 77
    .line 78
    .line 79
    move-result v13

    .line 80
    if-nez v13, :cond_6

    .line 81
    .line 82
    goto :goto_4

    .line 83
    :cond_6
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 84
    .line 85
    .line 86
    move-object/from16 v26, v8

    .line 87
    .line 88
    goto/16 :goto_9

    .line 89
    .line 90
    :cond_7
    :goto_4
    if-eqz v7, :cond_9

    .line 91
    .line 92
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object v7

    .line 96
    sget-object v10, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 97
    .line 98
    invoke-virtual {v10}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object v10

    .line 102
    if-ne v7, v10, :cond_8

    .line 103
    .line 104
    new-instance v7, Ltop/cycdm/cycapp/ui/history/h;

    .line 105
    .line 106
    invoke-direct {v7}, Ltop/cycdm/cycapp/ui/history/h;-><init>()V

    .line 107
    .line 108
    .line 109
    invoke-interface {v8, v7}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 110
    .line 111
    .line 112
    :cond_8
    check-cast v7, Lkotlin/jvm/functions/Function0;

    .line 113
    .line 114
    move-object v13, v7

    .line 115
    goto :goto_5

    .line 116
    :cond_9
    move-object v13, v10

    .line 117
    :goto_5
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 118
    .line 119
    .line 120
    move-result v7

    .line 121
    if-eqz v7, :cond_a

    .line 122
    .line 123
    const/4 v7, -0x1

    .line 124
    const-string v10, "top.cycdm.cycapp.ui.history.HistoryItem (HistoryScreen.kt:165)"

    .line 125
    .line 126
    invoke-static {v4, v5, v7, v10}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 127
    .line 128
    .line 129
    :cond_a
    sget-object v14, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 130
    .line 131
    const/4 v4, 0x0

    .line 132
    const/4 v15, 0x0

    .line 133
    invoke-static {v14, v4, v11, v15}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxWidth$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 134
    .line 135
    .line 136
    move-result-object v16

    .line 137
    const/4 v7, 0x0

    .line 138
    invoke-static {v8, v7}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 139
    .line 140
    .line 141
    move-result-object v10

    .line 142
    invoke-virtual {v10}, Lw7/a;->n()J

    .line 143
    .line 144
    .line 145
    move-result-wide v17

    .line 146
    const/16 v20, 0x2

    .line 147
    .line 148
    const/16 v21, 0x0

    .line 149
    .line 150
    const/16 v19, 0x0

    .line 151
    .line 152
    invoke-static/range {v16 .. v21}, Landroidx/compose/foundation/BackgroundKt;->background-bw27NRU$default(Landroidx/compose/ui/Modifier;JLandroidx/compose/ui/graphics/Shape;ILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 153
    .line 154
    .line 155
    move-result-object v22

    .line 156
    and-int/lit8 v5, v5, 0x70

    .line 157
    .line 158
    if-ne v5, v9, :cond_b

    .line 159
    .line 160
    move v5, v11

    .line 161
    goto :goto_6

    .line 162
    :cond_b
    move v5, v7

    .line 163
    :goto_6
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 164
    .line 165
    .line 166
    move-result-object v9

    .line 167
    if-nez v5, :cond_c

    .line 168
    .line 169
    sget-object v5, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 170
    .line 171
    invoke-virtual {v5}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 172
    .line 173
    .line 174
    move-result-object v5

    .line 175
    if-ne v9, v5, :cond_d

    .line 176
    .line 177
    :cond_c
    new-instance v9, Ltop/cycdm/cycapp/ui/history/i;

    .line 178
    .line 179
    invoke-direct {v9, v13}, Ltop/cycdm/cycapp/ui/history/i;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 180
    .line 181
    .line 182
    invoke-interface {v8, v9}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 183
    .line 184
    .line 185
    :cond_d
    move-object/from16 v26, v9

    .line 186
    .line 187
    check-cast v26, Lkotlin/jvm/functions/Function0;

    .line 188
    .line 189
    const/16 v27, 0x7

    .line 190
    .line 191
    const/16 v28, 0x0

    .line 192
    .line 193
    const/16 v23, 0x0

    .line 194
    .line 195
    const/16 v24, 0x0

    .line 196
    .line 197
    const/16 v25, 0x0

    .line 198
    .line 199
    invoke-static/range {v22 .. v28}, Landroidx/compose/foundation/ClickableKt;->clickable-XHw0xAI$default(Landroidx/compose/ui/Modifier;ZLjava/lang/String;Landroidx/compose/ui/semantics/Role;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 200
    .line 201
    .line 202
    move-result-object v5

    .line 203
    const/16 v9, 0xf

    .line 204
    .line 205
    int-to-float v9, v9

    .line 206
    invoke-static {v9}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 207
    .line 208
    .line 209
    move-result v10

    .line 210
    const/16 p2, 0x10

    .line 211
    .line 212
    const/16 v12, 0x8

    .line 213
    .line 214
    int-to-float v12, v12

    .line 215
    invoke-static {v12}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 216
    .line 217
    .line 218
    move-result v12

    .line 219
    invoke-static {v5, v10, v12}, Landroidx/compose/foundation/layout/PaddingKt;->padding-VpY3zN4(Landroidx/compose/ui/Modifier;FF)Landroidx/compose/ui/Modifier;

    .line 220
    .line 221
    .line 222
    move-result-object v5

    .line 223
    const/16 v10, 0x50

    .line 224
    .line 225
    int-to-float v10, v10

    .line 226
    invoke-static {v10}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 227
    .line 228
    .line 229
    move-result v10

    .line 230
    invoke-static {v5, v10}, Landroidx/compose/foundation/layout/SizeKt;->height-3ABfNKs(Landroidx/compose/ui/Modifier;F)Landroidx/compose/ui/Modifier;

    .line 231
    .line 232
    .line 233
    move-result-object v5

    .line 234
    sget-object v12, Landroidx/compose/foundation/layout/Arrangement;->INSTANCE:Landroidx/compose/foundation/layout/Arrangement;

    .line 235
    .line 236
    invoke-virtual {v12}, Landroidx/compose/foundation/layout/Arrangement;->getStart()Landroidx/compose/foundation/layout/Arrangement$Horizontal;

    .line 237
    .line 238
    .line 239
    move-result-object v10

    .line 240
    sget-object v16, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 241
    .line 242
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/Alignment$Companion;->getTop()Landroidx/compose/ui/Alignment$Vertical;

    .line 243
    .line 244
    .line 245
    move-result-object v3

    .line 246
    invoke-static {v10, v3, v8, v7}, Landroidx/compose/foundation/layout/RowKt;->rowMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Horizontal;Landroidx/compose/ui/Alignment$Vertical;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 247
    .line 248
    .line 249
    move-result-object v3

    .line 250
    invoke-static {v8, v7}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 251
    .line 252
    .line 253
    move-result v10

    .line 254
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 255
    .line 256
    .line 257
    move-result-object v6

    .line 258
    invoke-static {v8, v5}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 259
    .line 260
    .line 261
    move-result-object v5

    .line 262
    sget-object v18, Landroidx/compose/ui/node/ComposeUiNode;->Companion:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 263
    .line 264
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 265
    .line 266
    .line 267
    move-result-object v7

    .line 268
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 269
    .line 270
    .line 271
    move-result-object v19

    .line 272
    if-nez v19, :cond_e

    .line 273
    .line 274
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 275
    .line 276
    .line 277
    :cond_e
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 278
    .line 279
    .line 280
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 281
    .line 282
    .line 283
    move-result v19

    .line 284
    if-eqz v19, :cond_f

    .line 285
    .line 286
    invoke-interface {v8, v7}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 287
    .line 288
    .line 289
    goto :goto_7

    .line 290
    :cond_f
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 291
    .line 292
    .line 293
    :goto_7
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 294
    .line 295
    .line 296
    move-result-object v7

    .line 297
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 298
    .line 299
    .line 300
    move-result-object v4

    .line 301
    invoke-static {v7, v3, v4}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 302
    .line 303
    .line 304
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 305
    .line 306
    .line 307
    move-result-object v3

    .line 308
    invoke-static {v7, v6, v3}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 309
    .line 310
    .line 311
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 312
    .line 313
    .line 314
    move-result-object v3

    .line 315
    invoke-interface {v7}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 316
    .line 317
    .line 318
    move-result v4

    .line 319
    if-nez v4, :cond_10

    .line 320
    .line 321
    invoke-interface {v7}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 322
    .line 323
    .line 324
    move-result-object v4

    .line 325
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 326
    .line 327
    .line 328
    move-result-object v6

    .line 329
    invoke-static {v4, v6}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 330
    .line 331
    .line 332
    move-result v4

    .line 333
    if-nez v4, :cond_11

    .line 334
    .line 335
    :cond_10
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 336
    .line 337
    .line 338
    move-result-object v4

    .line 339
    invoke-interface {v7, v4}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 340
    .line 341
    .line 342
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 343
    .line 344
    .line 345
    move-result-object v4

    .line 346
    invoke-interface {v7, v4, v3}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 347
    .line 348
    .line 349
    :cond_11
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 350
    .line 351
    .line 352
    move-result-object v3

    .line 353
    invoke-static {v7, v5, v3}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 354
    .line 355
    .line 356
    sget-object v20, Landroidx/compose/foundation/layout/RowScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/RowScopeInstance;

    .line 357
    .line 358
    invoke-virtual {v0}, Ltop/cycdm/model/j;->a()Ljava/lang/String;

    .line 359
    .line 360
    .line 361
    move-result-object v5

    .line 362
    const/4 v3, 0x0

    .line 363
    invoke-static {v14, v3, v11, v15}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxHeight$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 364
    .line 365
    .line 366
    move-result-object v4

    .line 367
    const v6, 0x3fd55555

    .line 368
    .line 369
    .line 370
    const/4 v7, 0x2

    .line 371
    const/4 v10, 0x0

    .line 372
    invoke-static {v4, v6, v10, v7, v15}, Landroidx/compose/foundation/layout/AspectRatioKt;->aspectRatio$default(Landroidx/compose/ui/Modifier;FZILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 373
    .line 374
    .line 375
    move-result-object v6

    .line 376
    invoke-virtual {v0}, Ltop/cycdm/model/j;->f()J

    .line 377
    .line 378
    .line 379
    move-result-wide v21

    .line 380
    invoke-static/range {v21 .. v22}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->a0(J)Ljava/lang/String;

    .line 381
    .line 382
    .line 383
    move-result-object v7

    .line 384
    move v4, v9

    .line 385
    const/16 v9, 0x30

    .line 386
    .line 387
    move/from16 v17, v10

    .line 388
    .line 389
    const/4 v10, 0x0

    .line 390
    move/from16 v95, v17

    .line 391
    .line 392
    move/from16 v17, v4

    .line 393
    .line 394
    move/from16 v4, v95

    .line 395
    .line 396
    invoke-static/range {v5 .. v10}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->V(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;Landroidx/compose/runtime/Composer;II)V

    .line 397
    .line 398
    .line 399
    invoke-static {v14, v3, v11, v15}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxHeight$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 400
    .line 401
    .line 402
    move-result-object v21

    .line 403
    const/16 v24, 0x2

    .line 404
    .line 405
    const/16 v25, 0x0

    .line 406
    .line 407
    const/high16 v22, 0x3f800000    # 1.0f

    .line 408
    .line 409
    const/16 v23, 0x0

    .line 410
    .line 411
    invoke-static/range {v20 .. v25}, Landroidx/compose/foundation/layout/RowScope;->weight$default(Landroidx/compose/foundation/layout/RowScope;Landroidx/compose/ui/Modifier;FZILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 412
    .line 413
    .line 414
    move-result-object v30

    .line 415
    invoke-static/range {v17 .. v17}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 416
    .line 417
    .line 418
    move-result v31

    .line 419
    const/16 v35, 0xe

    .line 420
    .line 421
    const/16 v36, 0x0

    .line 422
    .line 423
    const/16 v32, 0x0

    .line 424
    .line 425
    const/16 v33, 0x0

    .line 426
    .line 427
    const/16 v34, 0x0

    .line 428
    .line 429
    invoke-static/range {v30 .. v36}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 430
    .line 431
    .line 432
    move-result-object v3

    .line 433
    invoke-virtual {v12}, Landroidx/compose/foundation/layout/Arrangement;->getTop()Landroidx/compose/foundation/layout/Arrangement$Vertical;

    .line 434
    .line 435
    .line 436
    move-result-object v5

    .line 437
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/Alignment$Companion;->getStart()Landroidx/compose/ui/Alignment$Horizontal;

    .line 438
    .line 439
    .line 440
    move-result-object v6

    .line 441
    invoke-static {v5, v6, v8, v4}, Landroidx/compose/foundation/layout/ColumnKt;->columnMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/ui/Alignment$Horizontal;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 442
    .line 443
    .line 444
    move-result-object v5

    .line 445
    invoke-static {v8, v4}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 446
    .line 447
    .line 448
    move-result v6

    .line 449
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 450
    .line 451
    .line 452
    move-result-object v7

    .line 453
    invoke-static {v8, v3}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 454
    .line 455
    .line 456
    move-result-object v3

    .line 457
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 458
    .line 459
    .line 460
    move-result-object v9

    .line 461
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 462
    .line 463
    .line 464
    move-result-object v10

    .line 465
    if-nez v10, :cond_12

    .line 466
    .line 467
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 468
    .line 469
    .line 470
    :cond_12
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 471
    .line 472
    .line 473
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 474
    .line 475
    .line 476
    move-result v10

    .line 477
    if-eqz v10, :cond_13

    .line 478
    .line 479
    invoke-interface {v8, v9}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 480
    .line 481
    .line 482
    goto :goto_8

    .line 483
    :cond_13
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 484
    .line 485
    .line 486
    :goto_8
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 487
    .line 488
    .line 489
    move-result-object v9

    .line 490
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 491
    .line 492
    .line 493
    move-result-object v10

    .line 494
    invoke-static {v9, v5, v10}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 495
    .line 496
    .line 497
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 498
    .line 499
    .line 500
    move-result-object v5

    .line 501
    invoke-static {v9, v7, v5}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 502
    .line 503
    .line 504
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 505
    .line 506
    .line 507
    move-result-object v5

    .line 508
    invoke-interface {v9}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 509
    .line 510
    .line 511
    move-result v7

    .line 512
    if-nez v7, :cond_14

    .line 513
    .line 514
    invoke-interface {v9}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 515
    .line 516
    .line 517
    move-result-object v7

    .line 518
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 519
    .line 520
    .line 521
    move-result-object v10

    .line 522
    invoke-static {v7, v10}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 523
    .line 524
    .line 525
    move-result v7

    .line 526
    if-nez v7, :cond_15

    .line 527
    .line 528
    :cond_14
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 529
    .line 530
    .line 531
    move-result-object v7

    .line 532
    invoke-interface {v9, v7}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 533
    .line 534
    .line 535
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 536
    .line 537
    .line 538
    move-result-object v6

    .line 539
    invoke-interface {v9, v6, v5}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 540
    .line 541
    .line 542
    :cond_15
    invoke-virtual/range {v18 .. v18}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 543
    .line 544
    .line 545
    move-result-object v5

    .line 546
    invoke-static {v9, v3, v5}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 547
    .line 548
    .line 549
    sget-object v3, Landroidx/compose/foundation/layout/ColumnScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/ColumnScopeInstance;

    .line 550
    .line 551
    invoke-virtual {v0}, Ltop/cycdm/model/j;->b()Ljava/lang/String;

    .line 552
    .line 553
    .line 554
    move-result-object v5

    .line 555
    invoke-static/range {v17 .. v17}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 556
    .line 557
    .line 558
    move-result v17

    .line 559
    const/16 v19, 0xb

    .line 560
    .line 561
    const/16 v20, 0x0

    .line 562
    .line 563
    const/4 v15, 0x0

    .line 564
    const/16 v16, 0x0

    .line 565
    .line 566
    const/16 v18, 0x0

    .line 567
    .line 568
    invoke-static/range {v14 .. v20}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 569
    .line 570
    .line 571
    move-result-object v6

    .line 572
    move-object v3, v14

    .line 573
    invoke-static/range {p2 .. p2}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 574
    .line 575
    .line 576
    move-result-wide v33

    .line 577
    invoke-static {v8, v4}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 578
    .line 579
    .line 580
    move-result-object v7

    .line 581
    invoke-virtual {v7}, Lw7/a;->o()J

    .line 582
    .line 583
    .line 584
    move-result-wide v31

    .line 585
    sget-object v62, Landroidx/compose/ui/text/font/FontWeight;->Companion:Landroidx/compose/ui/text/font/FontWeight$Companion;

    .line 586
    .line 587
    invoke-virtual/range {v62 .. v62}, Landroidx/compose/ui/text/font/FontWeight$Companion;->getW700()Landroidx/compose/ui/text/font/FontWeight;

    .line 588
    .line 589
    .line 590
    move-result-object v35

    .line 591
    new-instance v30, Landroidx/compose/ui/text/TextStyle;

    .line 592
    .line 593
    const v60, 0xfffff8

    .line 594
    .line 595
    .line 596
    const/16 v61, 0x0

    .line 597
    .line 598
    const/16 v36, 0x0

    .line 599
    .line 600
    const/16 v37, 0x0

    .line 601
    .line 602
    const/16 v38, 0x0

    .line 603
    .line 604
    const/16 v39, 0x0

    .line 605
    .line 606
    const-wide/16 v40, 0x0

    .line 607
    .line 608
    const/16 v42, 0x0

    .line 609
    .line 610
    const/16 v43, 0x0

    .line 611
    .line 612
    const/16 v44, 0x0

    .line 613
    .line 614
    const-wide/16 v45, 0x0

    .line 615
    .line 616
    const/16 v47, 0x0

    .line 617
    .line 618
    const/16 v48, 0x0

    .line 619
    .line 620
    const/16 v49, 0x0

    .line 621
    .line 622
    const/16 v50, 0x0

    .line 623
    .line 624
    const/16 v51, 0x0

    .line 625
    .line 626
    const-wide/16 v52, 0x0

    .line 627
    .line 628
    const/16 v54, 0x0

    .line 629
    .line 630
    const/16 v55, 0x0

    .line 631
    .line 632
    const/16 v56, 0x0

    .line 633
    .line 634
    const/16 v57, 0x0

    .line 635
    .line 636
    const/16 v58, 0x0

    .line 637
    .line 638
    const/16 v59, 0x0

    .line 639
    .line 640
    invoke-direct/range {v30 .. v61}, Landroidx/compose/ui/text/TextStyle;-><init>(JJLandroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontSynthesis;Landroidx/compose/ui/text/font/FontFamily;Ljava/lang/String;JLandroidx/compose/ui/text/style/BaselineShift;Landroidx/compose/ui/text/style/TextGeometricTransform;Landroidx/compose/ui/text/intl/LocaleList;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/graphics/Shadow;Landroidx/compose/ui/graphics/drawscope/DrawStyle;IIJLandroidx/compose/ui/text/style/TextIndent;Landroidx/compose/ui/text/PlatformTextStyle;Landroidx/compose/ui/text/style/LineHeightStyle;IILandroidx/compose/ui/text/style/TextMotion;ILkotlin/jvm/internal/n;)V

    .line 641
    .line 642
    .line 643
    const/16 v28, 0xc00

    .line 644
    .line 645
    const v29, 0xdffc

    .line 646
    .line 647
    .line 648
    move-object/from16 v26, v8

    .line 649
    .line 650
    const-wide/16 v7, 0x0

    .line 651
    .line 652
    const-wide/16 v9, 0x0

    .line 653
    .line 654
    const/4 v11, 0x0

    .line 655
    const/4 v12, 0x0

    .line 656
    move-object v14, v13

    .line 657
    const/4 v13, 0x0

    .line 658
    move-object/from16 v16, v14

    .line 659
    .line 660
    const-wide/16 v14, 0x0

    .line 661
    .line 662
    move-object/from16 v17, v16

    .line 663
    .line 664
    const/16 v16, 0x0

    .line 665
    .line 666
    move-object/from16 v18, v17

    .line 667
    .line 668
    const/16 v17, 0x0

    .line 669
    .line 670
    move-object/from16 v20, v18

    .line 671
    .line 672
    const-wide/16 v18, 0x0

    .line 673
    .line 674
    move-object/from16 v21, v20

    .line 675
    .line 676
    const/16 v20, 0x0

    .line 677
    .line 678
    move-object/from16 v22, v21

    .line 679
    .line 680
    const/16 v21, 0x0

    .line 681
    .line 682
    move-object/from16 v23, v22

    .line 683
    .line 684
    const/16 v22, 0x2

    .line 685
    .line 686
    move-object/from16 v24, v23

    .line 687
    .line 688
    const/16 v23, 0x0

    .line 689
    .line 690
    move-object/from16 v25, v24

    .line 691
    .line 692
    const/16 v24, 0x0

    .line 693
    .line 694
    const/16 v27, 0x30

    .line 695
    .line 696
    move-object/from16 v95, v30

    .line 697
    .line 698
    move-object/from16 v30, v25

    .line 699
    .line 700
    move-object/from16 v25, v95

    .line 701
    .line 702
    invoke-static/range {v5 .. v29}, Landroidx/compose/material3/TextKt;->Text--4IGK_g(Ljava/lang/String;Landroidx/compose/ui/Modifier;JJLandroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontFamily;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/text/style/TextAlign;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/TextStyle;Landroidx/compose/runtime/Composer;III)V

    .line 703
    .line 704
    .line 705
    move-object/from16 v8, v26

    .line 706
    .line 707
    new-instance v5, Ljava/lang/StringBuilder;

    .line 708
    .line 709
    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    .line 710
    .line 711
    .line 712
    invoke-virtual {v0}, Ltop/cycdm/model/j;->d()Ljava/lang/String;

    .line 713
    .line 714
    .line 715
    move-result-object v6

    .line 716
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 717
    .line 718
    .line 719
    const-string v6, " \u5269\u4f59 "

    .line 720
    .line 721
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 722
    .line 723
    .line 724
    invoke-virtual {v0}, Ltop/cycdm/model/j;->c()J

    .line 725
    .line 726
    .line 727
    move-result-wide v6

    .line 728
    invoke-virtual {v0}, Ltop/cycdm/model/j;->f()J

    .line 729
    .line 730
    .line 731
    move-result-wide v9

    .line 732
    sub-long/2addr v6, v9

    .line 733
    invoke-static {v6, v7}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->a0(J)Ljava/lang/String;

    .line 734
    .line 735
    .line 736
    move-result-object v6

    .line 737
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 738
    .line 739
    .line 740
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 741
    .line 742
    .line 743
    move-result-object v5

    .line 744
    const/16 v6, 0xe

    .line 745
    .line 746
    invoke-static {v6}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 747
    .line 748
    .line 749
    move-result-wide v66

    .line 750
    invoke-static {v8, v4}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 751
    .line 752
    .line 753
    move-result-object v4

    .line 754
    invoke-virtual {v4}, Lw7/a;->g()J

    .line 755
    .line 756
    .line 757
    move-result-wide v64

    .line 758
    invoke-virtual/range {v62 .. v62}, Landroidx/compose/ui/text/font/FontWeight$Companion;->getW400()Landroidx/compose/ui/text/font/FontWeight;

    .line 759
    .line 760
    .line 761
    move-result-object v68

    .line 762
    new-instance v63, Landroidx/compose/ui/text/TextStyle;

    .line 763
    .line 764
    const v93, 0xfffff8

    .line 765
    .line 766
    .line 767
    const/16 v94, 0x0

    .line 768
    .line 769
    const/16 v69, 0x0

    .line 770
    .line 771
    const/16 v70, 0x0

    .line 772
    .line 773
    const/16 v71, 0x0

    .line 774
    .line 775
    const/16 v72, 0x0

    .line 776
    .line 777
    const-wide/16 v73, 0x0

    .line 778
    .line 779
    const/16 v75, 0x0

    .line 780
    .line 781
    const/16 v76, 0x0

    .line 782
    .line 783
    const/16 v77, 0x0

    .line 784
    .line 785
    const-wide/16 v78, 0x0

    .line 786
    .line 787
    const/16 v80, 0x0

    .line 788
    .line 789
    const/16 v81, 0x0

    .line 790
    .line 791
    const/16 v82, 0x0

    .line 792
    .line 793
    const/16 v83, 0x0

    .line 794
    .line 795
    const/16 v84, 0x0

    .line 796
    .line 797
    const-wide/16 v85, 0x0

    .line 798
    .line 799
    const/16 v87, 0x0

    .line 800
    .line 801
    const/16 v88, 0x0

    .line 802
    .line 803
    const/16 v89, 0x0

    .line 804
    .line 805
    const/16 v90, 0x0

    .line 806
    .line 807
    const/16 v91, 0x0

    .line 808
    .line 809
    const/16 v92, 0x0

    .line 810
    .line 811
    invoke-direct/range {v63 .. v94}, Landroidx/compose/ui/text/TextStyle;-><init>(JJLandroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontSynthesis;Landroidx/compose/ui/text/font/FontFamily;Ljava/lang/String;JLandroidx/compose/ui/text/style/BaselineShift;Landroidx/compose/ui/text/style/TextGeometricTransform;Landroidx/compose/ui/text/intl/LocaleList;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/graphics/Shadow;Landroidx/compose/ui/graphics/drawscope/DrawStyle;IIJLandroidx/compose/ui/text/style/TextIndent;Landroidx/compose/ui/text/PlatformTextStyle;Landroidx/compose/ui/text/style/LineHeightStyle;IILandroidx/compose/ui/text/style/TextMotion;ILkotlin/jvm/internal/n;)V

    .line 812
    .line 813
    .line 814
    const/4 v4, 0x6

    .line 815
    int-to-float v4, v4

    .line 816
    invoke-static {v4}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 817
    .line 818
    .line 819
    move-result v16

    .line 820
    const/16 v19, 0xd

    .line 821
    .line 822
    const/16 v20, 0x0

    .line 823
    .line 824
    const/4 v15, 0x0

    .line 825
    const/16 v17, 0x0

    .line 826
    .line 827
    const/16 v18, 0x0

    .line 828
    .line 829
    move-object v14, v3

    .line 830
    invoke-static/range {v14 .. v20}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 831
    .line 832
    .line 833
    move-result-object v6

    .line 834
    const/16 v28, 0x0

    .line 835
    .line 836
    const v29, 0xfffc

    .line 837
    .line 838
    .line 839
    const-wide/16 v7, 0x0

    .line 840
    .line 841
    const-wide/16 v9, 0x0

    .line 842
    .line 843
    const-wide/16 v14, 0x0

    .line 844
    .line 845
    const/16 v16, 0x0

    .line 846
    .line 847
    const/16 v17, 0x0

    .line 848
    .line 849
    const-wide/16 v18, 0x0

    .line 850
    .line 851
    const/16 v20, 0x0

    .line 852
    .line 853
    const/16 v22, 0x0

    .line 854
    .line 855
    move-object/from16 v25, v63

    .line 856
    .line 857
    invoke-static/range {v5 .. v29}, Landroidx/compose/material3/TextKt;->Text--4IGK_g(Ljava/lang/String;Landroidx/compose/ui/Modifier;JJLandroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontFamily;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/text/style/TextAlign;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/TextStyle;Landroidx/compose/runtime/Composer;III)V

    .line 858
    .line 859
    .line 860
    invoke-interface/range {v26 .. v26}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 861
    .line 862
    .line 863
    invoke-interface/range {v26 .. v26}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 864
    .line 865
    .line 866
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 867
    .line 868
    .line 869
    move-result v3

    .line 870
    if-eqz v3, :cond_16

    .line 871
    .line 872
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 873
    .line 874
    .line 875
    :cond_16
    move-object/from16 v10, v30

    .line 876
    .line 877
    :goto_9
    invoke-interface/range {v26 .. v26}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 878
    .line 879
    .line 880
    move-result-object v3

    .line 881
    if-eqz v3, :cond_17

    .line 882
    .line 883
    new-instance v4, Ltop/cycdm/cycapp/ui/history/j;

    .line 884
    .line 885
    invoke-direct {v4, v0, v10, v1, v2}, Ltop/cycdm/cycapp/ui/history/j;-><init>(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;II)V

    .line 886
    .line 887
    .line 888
    invoke-interface {v3, v4}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 889
    .line 890
    .line 891
    :cond_17
    return-void
.end method

.method public static final H()Lkotlin/t;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/t;->a:Lkotlin/t;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final I(Lkotlin/jvm/functions/Function0;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final J(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    invoke-static {p2}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p2

    invoke-static {p0, p1, p4, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->G(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final K(Landroidx/compose/runtime/Composer;I)V
    .locals 25

    .line 1
    move/from16 v0, p1

    .line 2
    .line 3
    const v1, 0x41d7a275

    .line 4
    .line 5
    .line 6
    move-object/from16 v2, p0

    .line 7
    .line 8
    invoke-interface {v2, v1}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 9
    .line 10
    .line 11
    move-result-object v7

    .line 12
    if-nez v0, :cond_1

    .line 13
    .line 14
    invoke-interface {v7}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 15
    .line 16
    .line 17
    move-result v2

    .line 18
    if-nez v2, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    invoke-interface {v7}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 22
    .line 23
    .line 24
    goto/16 :goto_3

    .line 25
    .line 26
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    if-eqz v2, :cond_2

    .line 31
    .line 32
    const/4 v2, -0x1

    .line 33
    const-string v3, "top.cycdm.cycapp.ui.history.HistoryList (HistoryScreen.kt:92)"

    .line 34
    .line 35
    invoke-static {v1, v0, v2, v3}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 36
    .line 37
    .line 38
    :cond_2
    const v1, 0x70b323c8

    .line 39
    .line 40
    .line 41
    invoke-interface {v7, v1}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 42
    .line 43
    .line 44
    sget-object v1, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 45
    .line 46
    sget v2, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 47
    .line 48
    invoke-virtual {v1, v7, v2}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    if-eqz v3, :cond_6

    .line 53
    .line 54
    const/4 v1, 0x0

    .line 55
    invoke-static {v3, v7, v1}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 56
    .line 57
    .line 58
    move-result-object v5

    .line 59
    const v2, 0x671a9c9b

    .line 60
    .line 61
    .line 62
    invoke-interface {v7, v2}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 63
    .line 64
    .line 65
    instance-of v2, v3, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 66
    .line 67
    if-eqz v2, :cond_3

    .line 68
    .line 69
    move-object v2, v3

    .line 70
    check-cast v2, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 71
    .line 72
    invoke-interface {v2}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    :goto_1
    move-object v6, v2

    .line 77
    goto :goto_2

    .line 78
    :cond_3
    sget-object v2, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 79
    .line 80
    goto :goto_1

    .line 81
    :goto_2
    const v8, 0x9048

    .line 82
    .line 83
    .line 84
    const/4 v9, 0x0

    .line 85
    const-class v2, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 86
    .line 87
    const/4 v4, 0x0

    .line 88
    invoke-static/range {v2 .. v9}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 89
    .line 90
    .line 91
    move-result-object v2

    .line 92
    invoke-interface {v7}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 93
    .line 94
    .line 95
    invoke-interface {v7}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 96
    .line 97
    .line 98
    check-cast v2, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 99
    .line 100
    invoke-static {}, Ltop/cycdm/cycapp/RouterKt;->i()Landroidx/compose/runtime/ProvidableCompositionLocal;

    .line 101
    .line 102
    .line 103
    move-result-object v3

    .line 104
    invoke-interface {v7, v3}, Landroidx/compose/runtime/Composer;->consume(Landroidx/compose/runtime/CompositionLocal;)Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v3

    .line 108
    check-cast v3, Landroidx/navigation/NavHostController;

    .line 109
    .line 110
    const/4 v5, 0x1

    .line 111
    invoke-static {v2, v4, v7, v1, v5}, Lorg/orbitmvi/orbit/compose/ContainerHostExtensionsKt;->c(Lorg/orbitmvi/orbit/b;Landroidx/lifecycle/Lifecycle$State;Landroidx/compose/runtime/Composer;II)Landroidx/compose/runtime/State;

    .line 112
    .line 113
    .line 114
    move-result-object v6

    .line 115
    invoke-virtual {v2}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->getHistoryPager$app_adRelease()Lkotlinx/coroutines/flow/d;

    .line 116
    .line 117
    .line 118
    move-result-object v8

    .line 119
    invoke-static {v8, v4, v7, v1, v5}, Landroidx/paging/compose/LazyPagingItemsKt;->collectAsLazyPagingItems(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/i;Landroidx/compose/runtime/Composer;II)Landroidx/paging/compose/LazyPagingItems;

    .line 120
    .line 121
    .line 122
    move-result-object v1

    .line 123
    sget-object v8, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 124
    .line 125
    const/4 v9, 0x0

    .line 126
    invoke-static {v8, v9, v5, v4}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxSize$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 127
    .line 128
    .line 129
    move-result-object v4

    .line 130
    sget-object v8, Landroidx/compose/foundation/layout/WindowInsets;->Companion:Landroidx/compose/foundation/layout/WindowInsets$Companion;

    .line 131
    .line 132
    const/4 v9, 0x6

    .line 133
    invoke-static {v8, v7, v9}, Landroidx/compose/foundation/layout/WindowInsets_androidKt;->getSystemBars(Landroidx/compose/foundation/layout/WindowInsets$Companion;Landroidx/compose/runtime/Composer;I)Landroidx/compose/foundation/layout/WindowInsets;

    .line 134
    .line 135
    .line 136
    move-result-object v10

    .line 137
    new-instance v8, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;

    .line 138
    .line 139
    invoke-direct {v8, v2, v6, v3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$a;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;Landroidx/compose/runtime/State;Landroidx/navigation/NavHostController;)V

    .line 140
    .line 141
    .line 142
    const/16 v2, 0x36

    .line 143
    .line 144
    const v3, -0x5d6cd0c0

    .line 145
    .line 146
    .line 147
    invoke-static {v3, v5, v8, v7, v2}, Landroidx/compose/runtime/internal/ComposableLambdaKt;->rememberComposableLambda(IZLjava/lang/Object;Landroidx/compose/runtime/Composer;I)Landroidx/compose/runtime/internal/ComposableLambda;

    .line 148
    .line 149
    .line 150
    move-result-object v19

    .line 151
    sget v2, Landroidx/paging/compose/LazyPagingItems;->$stable:I

    .line 152
    .line 153
    or-int/lit8 v21, v2, 0x30

    .line 154
    .line 155
    const/high16 v22, 0xc00000

    .line 156
    .line 157
    const v23, 0x1fefc

    .line 158
    .line 159
    .line 160
    move-object v3, v4

    .line 161
    const/4 v4, 0x0

    .line 162
    const/4 v5, 0x0

    .line 163
    const/4 v6, 0x0

    .line 164
    move-object/from16 v20, v7

    .line 165
    .line 166
    const/4 v7, 0x0

    .line 167
    const/4 v8, 0x0

    .line 168
    const/4 v9, 0x0

    .line 169
    const/4 v11, 0x0

    .line 170
    const/4 v12, 0x0

    .line 171
    const/4 v13, 0x0

    .line 172
    const/4 v14, 0x0

    .line 173
    const/4 v15, 0x0

    .line 174
    const/16 v16, 0x0

    .line 175
    .line 176
    const/16 v17, 0x0

    .line 177
    .line 178
    const/16 v18, 0x0

    .line 179
    .line 180
    move/from16 v24, v2

    .line 181
    .line 182
    move-object v2, v1

    .line 183
    move/from16 v1, v24

    .line 184
    .line 185
    invoke-static/range {v2 .. v23}, Ltop/cycdm/cycapp/ui/common/StatusLazyLayoutKt;->C(Landroidx/paging/compose/LazyPagingItems;Landroidx/compose/ui/Modifier;Lkotlin/jvm/functions/Function1;Landroidx/compose/foundation/lazy/LazyListState;Landroidx/compose/foundation/layout/PaddingValues;ZLandroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/ui/Alignment$Horizontal;Landroidx/compose/foundation/layout/WindowInsets;Ljava/lang/String;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function3;Lkotlin/jvm/functions/Function3;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function3;Landroidx/compose/runtime/Composer;III)V

    .line 186
    .line 187
    .line 188
    move-object/from16 v7, v20

    .line 189
    .line 190
    invoke-static {v2, v7, v1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->C(Landroidx/paging/compose/LazyPagingItems;Landroidx/compose/runtime/Composer;I)V

    .line 191
    .line 192
    .line 193
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 194
    .line 195
    .line 196
    move-result v1

    .line 197
    if-eqz v1, :cond_4

    .line 198
    .line 199
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 200
    .line 201
    .line 202
    :cond_4
    :goto_3
    invoke-interface {v7}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 203
    .line 204
    .line 205
    move-result-object v1

    .line 206
    if-eqz v1, :cond_5

    .line 207
    .line 208
    new-instance v2, Ltop/cycdm/cycapp/ui/history/n;

    .line 209
    .line 210
    invoke-direct {v2, v0}, Ltop/cycdm/cycapp/ui/history/n;-><init>(I)V

    .line 211
    .line 212
    .line 213
    invoke-interface {v1, v2}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 214
    .line 215
    .line 216
    :cond_5
    return-void

    .line 217
    :cond_6
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 218
    .line 219
    const-string v1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 220
    .line 221
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 222
    .line 223
    .line 224
    throw v0
.end method

.method public static final L(Landroidx/compose/runtime/State;)Ltop/cycdm/cycapp/ui/history/w;
    .locals 0

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/State;->getValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Ltop/cycdm/cycapp/ui/history/w;

    .line 6
    .line 7
    return-object p0
.end method

.method public static final M(ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p0, p0, 0x1

    invoke-static {p0}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p0

    invoke-static {p1, p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->K(Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final N(Landroidx/compose/runtime/Composer;I)V
    .locals 47

    .line 1
    move/from16 v0, p1

    .line 2
    .line 3
    const v1, 0x154ddfc3

    .line 4
    .line 5
    .line 6
    move-object/from16 v2, p0

    .line 7
    .line 8
    invoke-interface {v2, v1}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 9
    .line 10
    .line 11
    move-result-object v14

    .line 12
    if-nez v0, :cond_1

    .line 13
    .line 14
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 15
    .line 16
    .line 17
    move-result v2

    .line 18
    if-nez v2, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 22
    .line 23
    .line 24
    goto/16 :goto_4

    .line 25
    .line 26
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    if-eqz v2, :cond_2

    .line 31
    .line 32
    const/4 v2, -0x1

    .line 33
    const-string v3, "top.cycdm.cycapp.ui.history.HistoryScreen (HistoryScreen.kt:63)"

    .line 34
    .line 35
    invoke-static {v1, v0, v2, v3}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 36
    .line 37
    .line 38
    :cond_2
    invoke-static {}, Ltop/cycdm/cycapp/RouterKt;->i()Landroidx/compose/runtime/ProvidableCompositionLocal;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-interface {v14, v1}, Landroidx/compose/runtime/Composer;->consume(Landroidx/compose/runtime/CompositionLocal;)Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    check-cast v1, Landroidx/navigation/NavHostController;

    .line 47
    .line 48
    const v2, 0x70b323c8

    .line 49
    .line 50
    .line 51
    invoke-interface {v14, v2}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 52
    .line 53
    .line 54
    sget-object v2, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 55
    .line 56
    sget v3, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 57
    .line 58
    invoke-virtual {v2, v14, v3}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 59
    .line 60
    .line 61
    move-result-object v3

    .line 62
    if-eqz v3, :cond_e

    .line 63
    .line 64
    const/4 v10, 0x0

    .line 65
    invoke-static {v3, v14, v10}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 66
    .line 67
    .line 68
    move-result-object v5

    .line 69
    const v2, 0x671a9c9b

    .line 70
    .line 71
    .line 72
    invoke-interface {v14, v2}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 73
    .line 74
    .line 75
    instance-of v2, v3, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 76
    .line 77
    if-eqz v2, :cond_3

    .line 78
    .line 79
    move-object v2, v3

    .line 80
    check-cast v2, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 81
    .line 82
    invoke-interface {v2}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    :goto_1
    move-object v6, v2

    .line 87
    goto :goto_2

    .line 88
    :cond_3
    sget-object v2, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 89
    .line 90
    goto :goto_1

    .line 91
    :goto_2
    const v8, 0x9048

    .line 92
    .line 93
    .line 94
    const/4 v9, 0x0

    .line 95
    const-class v2, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 96
    .line 97
    const/4 v4, 0x0

    .line 98
    move-object v7, v14

    .line 99
    invoke-static/range {v2 .. v9}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 100
    .line 101
    .line 102
    move-result-object v2

    .line 103
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 104
    .line 105
    .line 106
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 107
    .line 108
    .line 109
    check-cast v2, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 110
    .line 111
    sget-object v3, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 112
    .line 113
    const/4 v4, 0x1

    .line 114
    const/4 v5, 0x0

    .line 115
    const/4 v6, 0x0

    .line 116
    invoke-static {v3, v6, v4, v5}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxSize$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 117
    .line 118
    .line 119
    move-result-object v15

    .line 120
    invoke-static {v14, v10}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 121
    .line 122
    .line 123
    move-result-object v3

    .line 124
    invoke-virtual {v3}, Lw7/a;->n()J

    .line 125
    .line 126
    .line 127
    move-result-wide v16

    .line 128
    const/16 v19, 0x2

    .line 129
    .line 130
    const/16 v20, 0x0

    .line 131
    .line 132
    const/16 v18, 0x0

    .line 133
    .line 134
    invoke-static/range {v15 .. v20}, Landroidx/compose/foundation/BackgroundKt;->background-bw27NRU$default(Landroidx/compose/ui/Modifier;JLandroidx/compose/ui/graphics/Shape;ILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 135
    .line 136
    .line 137
    move-result-object v3

    .line 138
    sget-object v4, Landroidx/compose/foundation/layout/Arrangement;->INSTANCE:Landroidx/compose/foundation/layout/Arrangement;

    .line 139
    .line 140
    invoke-virtual {v4}, Landroidx/compose/foundation/layout/Arrangement;->getTop()Landroidx/compose/foundation/layout/Arrangement$Vertical;

    .line 141
    .line 142
    .line 143
    move-result-object v4

    .line 144
    sget-object v5, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 145
    .line 146
    invoke-virtual {v5}, Landroidx/compose/ui/Alignment$Companion;->getStart()Landroidx/compose/ui/Alignment$Horizontal;

    .line 147
    .line 148
    .line 149
    move-result-object v5

    .line 150
    invoke-static {v4, v5, v14, v10}, Landroidx/compose/foundation/layout/ColumnKt;->columnMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/ui/Alignment$Horizontal;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 151
    .line 152
    .line 153
    move-result-object v4

    .line 154
    invoke-static {v14, v10}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 155
    .line 156
    .line 157
    move-result v5

    .line 158
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 159
    .line 160
    .line 161
    move-result-object v6

    .line 162
    invoke-static {v14, v3}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 163
    .line 164
    .line 165
    move-result-object v3

    .line 166
    sget-object v7, Landroidx/compose/ui/node/ComposeUiNode;->Companion:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 167
    .line 168
    invoke-virtual {v7}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 169
    .line 170
    .line 171
    move-result-object v8

    .line 172
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 173
    .line 174
    .line 175
    move-result-object v9

    .line 176
    if-nez v9, :cond_4

    .line 177
    .line 178
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 179
    .line 180
    .line 181
    :cond_4
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 182
    .line 183
    .line 184
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 185
    .line 186
    .line 187
    move-result v9

    .line 188
    if-eqz v9, :cond_5

    .line 189
    .line 190
    invoke-interface {v14, v8}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 191
    .line 192
    .line 193
    goto :goto_3

    .line 194
    :cond_5
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 195
    .line 196
    .line 197
    :goto_3
    invoke-static {v14}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 198
    .line 199
    .line 200
    move-result-object v8

    .line 201
    invoke-virtual {v7}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 202
    .line 203
    .line 204
    move-result-object v9

    .line 205
    invoke-static {v8, v4, v9}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 206
    .line 207
    .line 208
    invoke-virtual {v7}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 209
    .line 210
    .line 211
    move-result-object v4

    .line 212
    invoke-static {v8, v6, v4}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 213
    .line 214
    .line 215
    invoke-virtual {v7}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 216
    .line 217
    .line 218
    move-result-object v4

    .line 219
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 220
    .line 221
    .line 222
    move-result v6

    .line 223
    if-nez v6, :cond_6

    .line 224
    .line 225
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 226
    .line 227
    .line 228
    move-result-object v6

    .line 229
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 230
    .line 231
    .line 232
    move-result-object v9

    .line 233
    invoke-static {v6, v9}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 234
    .line 235
    .line 236
    move-result v6

    .line 237
    if-nez v6, :cond_7

    .line 238
    .line 239
    :cond_6
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 240
    .line 241
    .line 242
    move-result-object v6

    .line 243
    invoke-interface {v8, v6}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 244
    .line 245
    .line 246
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 247
    .line 248
    .line 249
    move-result-object v5

    .line 250
    invoke-interface {v8, v5, v4}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 251
    .line 252
    .line 253
    :cond_7
    invoke-virtual {v7}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 254
    .line 255
    .line 256
    move-result-object v4

    .line 257
    invoke-static {v8, v3, v4}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 258
    .line 259
    .line 260
    sget-object v3, Landroidx/compose/foundation/layout/ColumnScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/ColumnScopeInstance;

    .line 261
    .line 262
    sget v3, Ltop/cycdm/cycapp/R$string;->title_history:I

    .line 263
    .line 264
    invoke-static {v3, v14, v10}, Landroidx/compose/ui/res/StringResources_androidKt;->stringResource(ILandroidx/compose/runtime/Composer;I)Ljava/lang/String;

    .line 265
    .line 266
    .line 267
    move-result-object v3

    .line 268
    const/16 v4, 0x14

    .line 269
    .line 270
    invoke-static {v4}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 271
    .line 272
    .line 273
    move-result-wide v18

    .line 274
    invoke-static {v14, v10}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 275
    .line 276
    .line 277
    move-result-object v4

    .line 278
    invoke-virtual {v4}, Lw7/a;->o()J

    .line 279
    .line 280
    .line 281
    move-result-wide v16

    .line 282
    new-instance v15, Landroidx/compose/ui/text/TextStyle;

    .line 283
    .line 284
    const v45, 0xfffffc

    .line 285
    .line 286
    .line 287
    const/16 v46, 0x0

    .line 288
    .line 289
    const/16 v20, 0x0

    .line 290
    .line 291
    const/16 v21, 0x0

    .line 292
    .line 293
    const/16 v22, 0x0

    .line 294
    .line 295
    const/16 v23, 0x0

    .line 296
    .line 297
    const/16 v24, 0x0

    .line 298
    .line 299
    const-wide/16 v25, 0x0

    .line 300
    .line 301
    const/16 v27, 0x0

    .line 302
    .line 303
    const/16 v28, 0x0

    .line 304
    .line 305
    const/16 v29, 0x0

    .line 306
    .line 307
    const-wide/16 v30, 0x0

    .line 308
    .line 309
    const/16 v32, 0x0

    .line 310
    .line 311
    const/16 v33, 0x0

    .line 312
    .line 313
    const/16 v34, 0x0

    .line 314
    .line 315
    const/16 v35, 0x0

    .line 316
    .line 317
    const/16 v36, 0x0

    .line 318
    .line 319
    const-wide/16 v37, 0x0

    .line 320
    .line 321
    const/16 v39, 0x0

    .line 322
    .line 323
    const/16 v40, 0x0

    .line 324
    .line 325
    const/16 v41, 0x0

    .line 326
    .line 327
    const/16 v42, 0x0

    .line 328
    .line 329
    const/16 v43, 0x0

    .line 330
    .line 331
    const/16 v44, 0x0

    .line 332
    .line 333
    invoke-direct/range {v15 .. v46}, Landroidx/compose/ui/text/TextStyle;-><init>(JJLandroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontSynthesis;Landroidx/compose/ui/text/font/FontFamily;Ljava/lang/String;JLandroidx/compose/ui/text/style/BaselineShift;Landroidx/compose/ui/text/style/TextGeometricTransform;Landroidx/compose/ui/text/intl/LocaleList;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/graphics/Shadow;Landroidx/compose/ui/graphics/drawscope/DrawStyle;IIJLandroidx/compose/ui/text/style/TextIndent;Landroidx/compose/ui/text/PlatformTextStyle;Landroidx/compose/ui/text/style/LineHeightStyle;IILandroidx/compose/ui/text/style/TextMotion;ILkotlin/jvm/internal/n;)V

    .line 334
    .line 335
    .line 336
    sget v4, Ltop/cycdm/cycapp/R$drawable;->ic_back:I

    .line 337
    .line 338
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 339
    .line 340
    .line 341
    move-result-object v5

    .line 342
    invoke-interface {v14, v1}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 343
    .line 344
    .line 345
    move-result v4

    .line 346
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 347
    .line 348
    .line 349
    move-result-object v6

    .line 350
    if-nez v4, :cond_8

    .line 351
    .line 352
    sget-object v4, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 353
    .line 354
    invoke-virtual {v4}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 355
    .line 356
    .line 357
    move-result-object v4

    .line 358
    if-ne v6, v4, :cond_9

    .line 359
    .line 360
    :cond_8
    new-instance v6, Ltop/cycdm/cycapp/ui/history/b;

    .line 361
    .line 362
    invoke-direct {v6, v1}, Ltop/cycdm/cycapp/ui/history/b;-><init>(Landroidx/navigation/NavHostController;)V

    .line 363
    .line 364
    .line 365
    invoke-interface {v14, v6}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 366
    .line 367
    .line 368
    :cond_9
    check-cast v6, Lkotlin/jvm/functions/Function0;

    .line 369
    .line 370
    sget v1, Ltop/cycdm/cycapp/R$drawable;->ic_delete:I

    .line 371
    .line 372
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 373
    .line 374
    .line 375
    move-result-object v9

    .line 376
    invoke-interface {v14, v2}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 377
    .line 378
    .line 379
    move-result v1

    .line 380
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 381
    .line 382
    .line 383
    move-result-object v4

    .line 384
    if-nez v1, :cond_a

    .line 385
    .line 386
    sget-object v1, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 387
    .line 388
    invoke-virtual {v1}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 389
    .line 390
    .line 391
    move-result-object v1

    .line 392
    if-ne v4, v1, :cond_b

    .line 393
    .line 394
    :cond_a
    new-instance v4, Ltop/cycdm/cycapp/ui/history/l;

    .line 395
    .line 396
    invoke-direct {v4, v2}, Ltop/cycdm/cycapp/ui/history/l;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;)V

    .line 397
    .line 398
    .line 399
    invoke-interface {v14, v4}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 400
    .line 401
    .line 402
    :cond_b
    move-object v12, v4

    .line 403
    check-cast v12, Lkotlin/jvm/functions/Function0;

    .line 404
    .line 405
    move-object v4, v15

    .line 406
    const/4 v15, 0x0

    .line 407
    const/16 v16, 0x2a2

    .line 408
    .line 409
    move-object v2, v3

    .line 410
    const/4 v3, 0x0

    .line 411
    const-wide/16 v7, 0x0

    .line 412
    .line 413
    move v1, v10

    .line 414
    const-wide/16 v10, 0x0

    .line 415
    .line 416
    const/4 v13, 0x0

    .line 417
    invoke-static/range {v2 .. v16}, Ltop/cycdm/cycapp/ui/common/h;->d(Ljava/lang/String;Landroidx/compose/ui/Modifier;Landroidx/compose/ui/text/TextStyle;Ljava/lang/Integer;Lkotlin/jvm/functions/Function0;JLjava/lang/Integer;JLkotlin/jvm/functions/Function0;Landroidx/compose/material3/TopAppBarColors;Landroidx/compose/runtime/Composer;II)V

    .line 418
    .line 419
    .line 420
    invoke-static {v14, v1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->K(Landroidx/compose/runtime/Composer;I)V

    .line 421
    .line 422
    .line 423
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 424
    .line 425
    .line 426
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 427
    .line 428
    .line 429
    move-result v1

    .line 430
    if-eqz v1, :cond_c

    .line 431
    .line 432
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 433
    .line 434
    .line 435
    :cond_c
    :goto_4
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 436
    .line 437
    .line 438
    move-result-object v1

    .line 439
    if-eqz v1, :cond_d

    .line 440
    .line 441
    new-instance v2, Ltop/cycdm/cycapp/ui/history/m;

    .line 442
    .line 443
    invoke-direct {v2, v0}, Ltop/cycdm/cycapp/ui/history/m;-><init>(I)V

    .line 444
    .line 445
    .line 446
    invoke-interface {v1, v2}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 447
    .line 448
    .line 449
    :cond_d
    return-void

    .line 450
    :cond_e
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 451
    .line 452
    const-string v1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 453
    .line 454
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 455
    .line 456
    .line 457
    throw v0
.end method

.method public static final O(Landroidx/navigation/NavHostController;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/navigation/NavController;->popBackStack()Z

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final P(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lkotlin/t;
    .locals 1

    .line 1
    sget-object v0, Ltop/cycdm/cycapp/ui/history/a$a;->a:Ltop/cycdm/cycapp/ui/history/a$a;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Ltop/cycdm/cycapp/BaseVM;->postSideEffectNotSuspend(Ljava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 7
    .line 8
    return-object p0
.end method

.method public static final Q(ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p0, p0, 0x1

    invoke-static {p0}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p0

    invoke-static {p1, p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->N(Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final R(Landroidx/compose/material3/SwipeToDismissBoxState;Landroidx/compose/runtime/Composer;I)V
    .locals 20

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move/from16 v1, p2

    .line 4
    .line 5
    const v2, -0x3cb05ecb

    .line 6
    .line 7
    .line 8
    move-object/from16 v3, p1

    .line 9
    .line 10
    invoke-interface {v3, v2}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 11
    .line 12
    .line 13
    move-result-object v8

    .line 14
    and-int/lit8 v3, v1, 0x6

    .line 15
    .line 16
    const/4 v11, 0x2

    .line 17
    if-nez v3, :cond_2

    .line 18
    .line 19
    and-int/lit8 v3, v1, 0x8

    .line 20
    .line 21
    if-nez v3, :cond_0

    .line 22
    .line 23
    invoke-interface {v8, v0}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    goto :goto_0

    .line 28
    :cond_0
    invoke-interface {v8, v0}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    :goto_0
    if-eqz v3, :cond_1

    .line 33
    .line 34
    const/4 v3, 0x4

    .line 35
    goto :goto_1

    .line 36
    :cond_1
    move v3, v11

    .line 37
    :goto_1
    or-int/2addr v3, v1

    .line 38
    goto :goto_2

    .line 39
    :cond_2
    move v3, v1

    .line 40
    :goto_2
    and-int/lit8 v4, v3, 0x3

    .line 41
    .line 42
    if-ne v4, v11, :cond_4

    .line 43
    .line 44
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 45
    .line 46
    .line 47
    move-result v4

    .line 48
    if-nez v4, :cond_3

    .line 49
    .line 50
    goto :goto_3

    .line 51
    :cond_3
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 52
    .line 53
    .line 54
    goto/16 :goto_8

    .line 55
    .line 56
    :cond_4
    :goto_3
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 57
    .line 58
    .line 59
    move-result v4

    .line 60
    if-eqz v4, :cond_5

    .line 61
    .line 62
    const/4 v4, -0x1

    .line 63
    const-string v5, "top.cycdm.cycapp.ui.history.SwipeBackgroundContent (HistoryScreen.kt:130)"

    .line 64
    .line 65
    invoke-static {v2, v3, v4, v5}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 66
    .line 67
    .line 68
    :cond_5
    invoke-virtual {v0}, Landroidx/compose/material3/SwipeToDismissBoxState;->getTargetValue()Landroidx/compose/material3/SwipeToDismissBoxValue;

    .line 69
    .line 70
    .line 71
    move-result-object v2

    .line 72
    sget-object v12, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt$b;->a:[I

    .line 73
    .line 74
    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    .line 75
    .line 76
    .line 77
    move-result v2

    .line 78
    aget v2, v12, v2

    .line 79
    .line 80
    const/4 v13, 0x1

    .line 81
    const/4 v14, 0x3

    .line 82
    if-eq v2, v13, :cond_8

    .line 83
    .line 84
    if-eq v2, v11, :cond_7

    .line 85
    .line 86
    if-ne v2, v14, :cond_6

    .line 87
    .line 88
    const v2, 0x2dec5b5a

    .line 89
    .line 90
    .line 91
    invoke-interface {v8, v2}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 92
    .line 93
    .line 94
    sget-object v2, Landroidx/compose/material3/MaterialTheme;->INSTANCE:Landroidx/compose/material3/MaterialTheme;

    .line 95
    .line 96
    sget v3, Landroidx/compose/material3/MaterialTheme;->$stable:I

    .line 97
    .line 98
    invoke-virtual {v2, v8, v3}, Landroidx/compose/material3/MaterialTheme;->getColorScheme(Landroidx/compose/runtime/Composer;I)Landroidx/compose/material3/ColorScheme;

    .line 99
    .line 100
    .line 101
    move-result-object v2

    .line 102
    invoke-virtual {v2}, Landroidx/compose/material3/ColorScheme;->getError-0d7_KjU()J

    .line 103
    .line 104
    .line 105
    move-result-wide v2

    .line 106
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 107
    .line 108
    .line 109
    :goto_4
    move-wide v3, v2

    .line 110
    goto :goto_5

    .line 111
    :cond_6
    const v0, 0x2dec3eb1

    .line 112
    .line 113
    .line 114
    invoke-interface {v8, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 115
    .line 116
    .line 117
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 118
    .line 119
    .line 120
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 121
    .line 122
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 123
    .line 124
    .line 125
    throw v0

    .line 126
    :cond_7
    const v2, 0x2dec513a

    .line 127
    .line 128
    .line 129
    invoke-interface {v8, v2}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 130
    .line 131
    .line 132
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 133
    .line 134
    .line 135
    sget-object v2, Landroidx/compose/ui/graphics/Color;->Companion:Landroidx/compose/ui/graphics/Color$Companion;

    .line 136
    .line 137
    invoke-virtual {v2}, Landroidx/compose/ui/graphics/Color$Companion;->getGreen-0d7_KjU()J

    .line 138
    .line 139
    .line 140
    move-result-wide v2

    .line 141
    goto :goto_4

    .line 142
    :cond_8
    const v2, 0x2dec49b9

    .line 143
    .line 144
    .line 145
    invoke-interface {v8, v2}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 146
    .line 147
    .line 148
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 149
    .line 150
    .line 151
    sget-object v2, Landroidx/compose/ui/graphics/Color;->Companion:Landroidx/compose/ui/graphics/Color$Companion;

    .line 152
    .line 153
    invoke-virtual {v2}, Landroidx/compose/ui/graphics/Color$Companion;->getGray-0d7_KjU()J

    .line 154
    .line 155
    .line 156
    move-result-wide v2

    .line 157
    goto :goto_4

    .line 158
    :goto_5
    const/4 v9, 0x0

    .line 159
    const/16 v10, 0xe

    .line 160
    .line 161
    const/4 v5, 0x0

    .line 162
    const/4 v6, 0x0

    .line 163
    const/4 v7, 0x0

    .line 164
    invoke-static/range {v3 .. v10}, Landroidx/compose/animation/SingleValueAnimationKt;->animateColorAsState-euL9pac(JLandroidx/compose/animation/core/AnimationSpec;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/Composer;II)Landroidx/compose/runtime/State;

    .line 165
    .line 166
    .line 167
    move-result-object v2

    .line 168
    invoke-virtual {v0}, Landroidx/compose/material3/SwipeToDismissBoxState;->getTargetValue()Landroidx/compose/material3/SwipeToDismissBoxValue;

    .line 169
    .line 170
    .line 171
    move-result-object v3

    .line 172
    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    .line 173
    .line 174
    .line 175
    move-result v3

    .line 176
    aget v3, v12, v3

    .line 177
    .line 178
    if-eq v3, v13, :cond_b

    .line 179
    .line 180
    if-eq v3, v11, :cond_a

    .line 181
    .line 182
    if-ne v3, v14, :cond_9

    .line 183
    .line 184
    const v3, 0x2dec8a1e

    .line 185
    .line 186
    .line 187
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 188
    .line 189
    .line 190
    sget-object v3, Landroidx/compose/material3/MaterialTheme;->INSTANCE:Landroidx/compose/material3/MaterialTheme;

    .line 191
    .line 192
    sget v4, Landroidx/compose/material3/MaterialTheme;->$stable:I

    .line 193
    .line 194
    invoke-virtual {v3, v8, v4}, Landroidx/compose/material3/MaterialTheme;->getColorScheme(Landroidx/compose/runtime/Composer;I)Landroidx/compose/material3/ColorScheme;

    .line 195
    .line 196
    .line 197
    move-result-object v3

    .line 198
    invoke-virtual {v3}, Landroidx/compose/material3/ColorScheme;->getOnPrimary-0d7_KjU()J

    .line 199
    .line 200
    .line 201
    move-result-wide v3

    .line 202
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 203
    .line 204
    .line 205
    goto :goto_6

    .line 206
    :cond_9
    const v0, 0x2dec67c4

    .line 207
    .line 208
    .line 209
    invoke-interface {v8, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 210
    .line 211
    .line 212
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 213
    .line 214
    .line 215
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 216
    .line 217
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 218
    .line 219
    .line 220
    throw v0

    .line 221
    :cond_a
    const v3, 0x2dec7f7e

    .line 222
    .line 223
    .line 224
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 225
    .line 226
    .line 227
    sget-object v3, Landroidx/compose/material3/MaterialTheme;->INSTANCE:Landroidx/compose/material3/MaterialTheme;

    .line 228
    .line 229
    sget v4, Landroidx/compose/material3/MaterialTheme;->$stable:I

    .line 230
    .line 231
    invoke-virtual {v3, v8, v4}, Landroidx/compose/material3/MaterialTheme;->getColorScheme(Landroidx/compose/runtime/Composer;I)Landroidx/compose/material3/ColorScheme;

    .line 232
    .line 233
    .line 234
    move-result-object v3

    .line 235
    invoke-virtual {v3}, Landroidx/compose/material3/ColorScheme;->getOnPrimary-0d7_KjU()J

    .line 236
    .line 237
    .line 238
    move-result-wide v3

    .line 239
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 240
    .line 241
    .line 242
    goto :goto_6

    .line 243
    :cond_b
    const v3, 0x2dec751c

    .line 244
    .line 245
    .line 246
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 247
    .line 248
    .line 249
    sget-object v3, Landroidx/compose/material3/MaterialTheme;->INSTANCE:Landroidx/compose/material3/MaterialTheme;

    .line 250
    .line 251
    sget v4, Landroidx/compose/material3/MaterialTheme;->$stable:I

    .line 252
    .line 253
    invoke-virtual {v3, v8, v4}, Landroidx/compose/material3/MaterialTheme;->getColorScheme(Landroidx/compose/runtime/Composer;I)Landroidx/compose/material3/ColorScheme;

    .line 254
    .line 255
    .line 256
    move-result-object v3

    .line 257
    invoke-virtual {v3}, Landroidx/compose/material3/ColorScheme;->getPrimary-0d7_KjU()J

    .line 258
    .line 259
    .line 260
    move-result-wide v3

    .line 261
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 262
    .line 263
    .line 264
    :goto_6
    const/4 v9, 0x0

    .line 265
    const/16 v10, 0xe

    .line 266
    .line 267
    const/4 v5, 0x0

    .line 268
    const/4 v6, 0x0

    .line 269
    const/4 v7, 0x0

    .line 270
    invoke-static/range {v3 .. v10}, Landroidx/compose/animation/SingleValueAnimationKt;->animateColorAsState-euL9pac(JLandroidx/compose/animation/core/AnimationSpec;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/Composer;II)Landroidx/compose/runtime/State;

    .line 271
    .line 272
    .line 273
    move-result-object v3

    .line 274
    sget-object v4, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 275
    .line 276
    const/4 v5, 0x0

    .line 277
    invoke-static {v4, v5, v13, v6}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxSize$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 278
    .line 279
    .line 280
    move-result-object v14

    .line 281
    invoke-static {v2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->T(Landroidx/compose/runtime/State;)J

    .line 282
    .line 283
    .line 284
    move-result-wide v15

    .line 285
    const/16 v18, 0x2

    .line 286
    .line 287
    const/16 v19, 0x0

    .line 288
    .line 289
    const/16 v17, 0x0

    .line 290
    .line 291
    invoke-static/range {v14 .. v19}, Landroidx/compose/foundation/BackgroundKt;->background-bw27NRU$default(Landroidx/compose/ui/Modifier;JLandroidx/compose/ui/graphics/Shape;ILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 292
    .line 293
    .line 294
    move-result-object v2

    .line 295
    sget-object v5, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 296
    .line 297
    invoke-virtual {v5}, Landroidx/compose/ui/Alignment$Companion;->getTopStart()Landroidx/compose/ui/Alignment;

    .line 298
    .line 299
    .line 300
    move-result-object v6

    .line 301
    const/4 v7, 0x0

    .line 302
    invoke-static {v6, v7}, Landroidx/compose/foundation/layout/BoxKt;->maybeCachedBoxMeasurePolicy(Landroidx/compose/ui/Alignment;Z)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 303
    .line 304
    .line 305
    move-result-object v6

    .line 306
    invoke-static {v8, v7}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 307
    .line 308
    .line 309
    move-result v7

    .line 310
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 311
    .line 312
    .line 313
    move-result-object v9

    .line 314
    invoke-static {v8, v2}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 315
    .line 316
    .line 317
    move-result-object v2

    .line 318
    sget-object v10, Landroidx/compose/ui/node/ComposeUiNode;->Companion:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 319
    .line 320
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 321
    .line 322
    .line 323
    move-result-object v11

    .line 324
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 325
    .line 326
    .line 327
    move-result-object v12

    .line 328
    if-nez v12, :cond_c

    .line 329
    .line 330
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 331
    .line 332
    .line 333
    :cond_c
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 334
    .line 335
    .line 336
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 337
    .line 338
    .line 339
    move-result v12

    .line 340
    if-eqz v12, :cond_d

    .line 341
    .line 342
    invoke-interface {v8, v11}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 343
    .line 344
    .line 345
    goto :goto_7

    .line 346
    :cond_d
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 347
    .line 348
    .line 349
    :goto_7
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 350
    .line 351
    .line 352
    move-result-object v11

    .line 353
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 354
    .line 355
    .line 356
    move-result-object v12

    .line 357
    invoke-static {v11, v6, v12}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 358
    .line 359
    .line 360
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 361
    .line 362
    .line 363
    move-result-object v6

    .line 364
    invoke-static {v11, v9, v6}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 365
    .line 366
    .line 367
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 368
    .line 369
    .line 370
    move-result-object v6

    .line 371
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 372
    .line 373
    .line 374
    move-result v9

    .line 375
    if-nez v9, :cond_e

    .line 376
    .line 377
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 378
    .line 379
    .line 380
    move-result-object v9

    .line 381
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 382
    .line 383
    .line 384
    move-result-object v12

    .line 385
    invoke-static {v9, v12}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 386
    .line 387
    .line 388
    move-result v9

    .line 389
    if-nez v9, :cond_f

    .line 390
    .line 391
    :cond_e
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 392
    .line 393
    .line 394
    move-result-object v9

    .line 395
    invoke-interface {v11, v9}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 396
    .line 397
    .line 398
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 399
    .line 400
    .line 401
    move-result-object v7

    .line 402
    invoke-interface {v11, v7, v6}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 403
    .line 404
    .line 405
    :cond_f
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 406
    .line 407
    .line 408
    move-result-object v6

    .line 409
    invoke-static {v11, v2, v6}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 410
    .line 411
    .line 412
    sget-object v2, Landroidx/compose/foundation/layout/BoxScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 413
    .line 414
    const/16 v6, 0x10

    .line 415
    .line 416
    int-to-float v6, v6

    .line 417
    invoke-static {v6}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 418
    .line 419
    .line 420
    move-result v6

    .line 421
    invoke-static {v4, v6}, Landroidx/compose/foundation/layout/PaddingKt;->padding-3ABfNKs(Landroidx/compose/ui/Modifier;F)Landroidx/compose/ui/Modifier;

    .line 422
    .line 423
    .line 424
    move-result-object v4

    .line 425
    invoke-virtual {v5}, Landroidx/compose/ui/Alignment$Companion;->getCenterEnd()Landroidx/compose/ui/Alignment;

    .line 426
    .line 427
    .line 428
    move-result-object v5

    .line 429
    invoke-interface {v2, v4, v5}, Landroidx/compose/foundation/layout/BoxScope;->align(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/Alignment;)Landroidx/compose/ui/Modifier;

    .line 430
    .line 431
    .line 432
    move-result-object v5

    .line 433
    sget-object v2, Landroidx/compose/material/icons/Icons$Rounded;->INSTANCE:Landroidx/compose/material/icons/Icons$Rounded;

    .line 434
    .line 435
    invoke-static {v2}, Landroidx/compose/material/icons/rounded/DeleteKt;->getDelete(Landroidx/compose/material/icons/Icons$Rounded;)Landroidx/compose/ui/graphics/vector/ImageVector;

    .line 436
    .line 437
    .line 438
    move-result-object v2

    .line 439
    invoke-static {v3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->U(Landroidx/compose/runtime/State;)J

    .line 440
    .line 441
    .line 442
    move-result-wide v6

    .line 443
    const/16 v9, 0x30

    .line 444
    .line 445
    const/4 v10, 0x0

    .line 446
    const-string v4, "\u5220\u9664\u5386\u53f2"

    .line 447
    .line 448
    move-object v3, v2

    .line 449
    invoke-static/range {v3 .. v10}, Landroidx/compose/material3/IconKt;->Icon-ww6aTOc(Landroidx/compose/ui/graphics/vector/ImageVector;Ljava/lang/String;Landroidx/compose/ui/Modifier;JLandroidx/compose/runtime/Composer;II)V

    .line 450
    .line 451
    .line 452
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 453
    .line 454
    .line 455
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 456
    .line 457
    .line 458
    move-result v2

    .line 459
    if-eqz v2, :cond_10

    .line 460
    .line 461
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 462
    .line 463
    .line 464
    :cond_10
    :goto_8
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 465
    .line 466
    .line 467
    move-result-object v2

    .line 468
    if-eqz v2, :cond_11

    .line 469
    .line 470
    new-instance v3, Ltop/cycdm/cycapp/ui/history/r;

    .line 471
    .line 472
    invoke-direct {v3, v0, v1}, Ltop/cycdm/cycapp/ui/history/r;-><init>(Landroidx/compose/material3/SwipeToDismissBoxState;I)V

    .line 473
    .line 474
    .line 475
    invoke-interface {v2, v3}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 476
    .line 477
    .line 478
    :cond_11
    return-void
.end method

.method public static final S(Landroidx/compose/material3/SwipeToDismissBoxState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->R(Landroidx/compose/material3/SwipeToDismissBoxState;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final T(Landroidx/compose/runtime/State;)J
    .locals 2

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/State;->getValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Landroidx/compose/ui/graphics/Color;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/compose/ui/graphics/Color;->unbox-impl()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    return-wide v0
.end method

.method public static final U(Landroidx/compose/runtime/State;)J
    .locals 2

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/State;->getValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Landroidx/compose/ui/graphics/Color;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/compose/ui/graphics/Color;->unbox-impl()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    return-wide v0
.end method

.method public static final V(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;Landroidx/compose/runtime/Composer;II)V
    .locals 30

    .line 1
    move/from16 v4, p4

    .line 2
    .line 3
    const/4 v0, 0x2

    .line 4
    const/4 v1, 0x4

    .line 5
    const/4 v2, 0x6

    .line 6
    const v3, -0x61104f95

    .line 7
    .line 8
    .line 9
    move-object/from16 v5, p3

    .line 10
    .line 11
    invoke-interface {v5, v3}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 12
    .line 13
    .line 14
    move-result-object v11

    .line 15
    const/4 v14, 0x1

    .line 16
    and-int/lit8 v5, p5, 0x1

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    or-int/lit8 v5, v4, 0x6

    .line 21
    .line 22
    move v6, v5

    .line 23
    move-object/from16 v5, p0

    .line 24
    .line 25
    goto :goto_1

    .line 26
    :cond_0
    and-int/lit8 v5, v4, 0x6

    .line 27
    .line 28
    if-nez v5, :cond_2

    .line 29
    .line 30
    move-object/from16 v5, p0

    .line 31
    .line 32
    invoke-interface {v11, v5}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    move-result v6

    .line 36
    if-eqz v6, :cond_1

    .line 37
    .line 38
    move v6, v1

    .line 39
    goto :goto_0

    .line 40
    :cond_1
    move v6, v0

    .line 41
    :goto_0
    or-int/2addr v6, v4

    .line 42
    goto :goto_1

    .line 43
    :cond_2
    move-object/from16 v5, p0

    .line 44
    .line 45
    move v6, v4

    .line 46
    :goto_1
    and-int/lit8 v0, p5, 0x2

    .line 47
    .line 48
    if-eqz v0, :cond_4

    .line 49
    .line 50
    or-int/lit8 v6, v6, 0x30

    .line 51
    .line 52
    :cond_3
    move-object/from16 v7, p1

    .line 53
    .line 54
    goto :goto_3

    .line 55
    :cond_4
    and-int/lit8 v7, v4, 0x30

    .line 56
    .line 57
    if-nez v7, :cond_3

    .line 58
    .line 59
    move-object/from16 v7, p1

    .line 60
    .line 61
    invoke-interface {v11, v7}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 62
    .line 63
    .line 64
    move-result v8

    .line 65
    if-eqz v8, :cond_5

    .line 66
    .line 67
    const/16 v8, 0x20

    .line 68
    .line 69
    goto :goto_2

    .line 70
    :cond_5
    const/16 v8, 0x10

    .line 71
    .line 72
    :goto_2
    or-int/2addr v6, v8

    .line 73
    :goto_3
    and-int/lit8 v1, p5, 0x4

    .line 74
    .line 75
    if-eqz v1, :cond_7

    .line 76
    .line 77
    or-int/lit16 v6, v6, 0x180

    .line 78
    .line 79
    :cond_6
    move-object/from16 v8, p2

    .line 80
    .line 81
    :goto_4
    move v15, v6

    .line 82
    goto :goto_6

    .line 83
    :cond_7
    and-int/lit16 v8, v4, 0x180

    .line 84
    .line 85
    if-nez v8, :cond_6

    .line 86
    .line 87
    move-object/from16 v8, p2

    .line 88
    .line 89
    invoke-interface {v11, v8}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 90
    .line 91
    .line 92
    move-result v9

    .line 93
    if-eqz v9, :cond_8

    .line 94
    .line 95
    const/16 v9, 0x100

    .line 96
    .line 97
    goto :goto_5

    .line 98
    :cond_8
    const/16 v9, 0x80

    .line 99
    .line 100
    :goto_5
    or-int/2addr v6, v9

    .line 101
    goto :goto_4

    .line 102
    :goto_6
    and-int/lit16 v6, v15, 0x93

    .line 103
    .line 104
    const/16 v9, 0x92

    .line 105
    .line 106
    if-ne v6, v9, :cond_a

    .line 107
    .line 108
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 109
    .line 110
    .line 111
    move-result v6

    .line 112
    if-nez v6, :cond_9

    .line 113
    .line 114
    goto :goto_7

    .line 115
    :cond_9
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 116
    .line 117
    .line 118
    move-object v2, v7

    .line 119
    move-object v3, v8

    .line 120
    goto/16 :goto_d

    .line 121
    .line 122
    :cond_a
    :goto_7
    if-eqz v0, :cond_b

    .line 123
    .line 124
    sget-object v0, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 125
    .line 126
    goto :goto_8

    .line 127
    :cond_b
    move-object v0, v7

    .line 128
    :goto_8
    const/4 v6, 0x0

    .line 129
    if-eqz v1, :cond_c

    .line 130
    .line 131
    move-object v1, v6

    .line 132
    goto :goto_9

    .line 133
    :cond_c
    move-object v1, v8

    .line 134
    :goto_9
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 135
    .line 136
    .line 137
    move-result v7

    .line 138
    if-eqz v7, :cond_d

    .line 139
    .line 140
    const/4 v7, -0x1

    .line 141
    const-string v8, "top.cycdm.cycapp.ui.history.VideoViewItem (HistoryScreen.kt:215)"

    .line 142
    .line 143
    invoke-static {v3, v15, v7, v8}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 144
    .line 145
    .line 146
    :cond_d
    sget-object v16, Landroidx/compose/ui/graphics/Brush;->Companion:Landroidx/compose/ui/graphics/Brush$Companion;

    .line 147
    .line 148
    sget-object v3, Landroidx/compose/ui/graphics/Color;->Companion:Landroidx/compose/ui/graphics/Color$Companion;

    .line 149
    .line 150
    invoke-virtual {v3}, Landroidx/compose/ui/graphics/Color$Companion;->getTransparent-0d7_KjU()J

    .line 151
    .line 152
    .line 153
    move-result-wide v7

    .line 154
    invoke-static {v7, v8}, Landroidx/compose/ui/graphics/Color;->box-impl(J)Landroidx/compose/ui/graphics/Color;

    .line 155
    .line 156
    .line 157
    move-result-object v7

    .line 158
    invoke-virtual {v3}, Landroidx/compose/ui/graphics/Color$Companion;->getTransparent-0d7_KjU()J

    .line 159
    .line 160
    .line 161
    move-result-wide v8

    .line 162
    invoke-static {v8, v9}, Landroidx/compose/ui/graphics/Color;->box-impl(J)Landroidx/compose/ui/graphics/Color;

    .line 163
    .line 164
    .line 165
    move-result-object v8

    .line 166
    invoke-virtual {v3}, Landroidx/compose/ui/graphics/Color$Companion;->getBlack-0d7_KjU()J

    .line 167
    .line 168
    .line 169
    move-result-wide v9

    .line 170
    invoke-static {v9, v10}, Landroidx/compose/ui/graphics/Color;->box-impl(J)Landroidx/compose/ui/graphics/Color;

    .line 171
    .line 172
    .line 173
    move-result-object v9

    .line 174
    filled-new-array {v7, v8, v9}, [Landroidx/compose/ui/graphics/Color;

    .line 175
    .line 176
    .line 177
    move-result-object v7

    .line 178
    invoke-static {v7}, Lkotlin/collections/w;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 179
    .line 180
    .line 181
    move-result-object v17

    .line 182
    const/16 v21, 0xe

    .line 183
    .line 184
    const/16 v22, 0x0

    .line 185
    .line 186
    const/16 v18, 0x0

    .line 187
    .line 188
    const/16 v19, 0x0

    .line 189
    .line 190
    const/16 v20, 0x0

    .line 191
    .line 192
    invoke-static/range {v16 .. v22}, Landroidx/compose/ui/graphics/Brush$Companion;->verticalGradient-8A-3gB4$default(Landroidx/compose/ui/graphics/Brush$Companion;Ljava/util/List;FFIILjava/lang/Object;)Landroidx/compose/ui/graphics/Brush;

    .line 193
    .line 194
    .line 195
    move-result-object v24

    .line 196
    sget-object v7, Landroidx/compose/foundation/layout/Arrangement;->INSTANCE:Landroidx/compose/foundation/layout/Arrangement;

    .line 197
    .line 198
    invoke-virtual {v7}, Landroidx/compose/foundation/layout/Arrangement;->getTop()Landroidx/compose/foundation/layout/Arrangement$Vertical;

    .line 199
    .line 200
    .line 201
    move-result-object v7

    .line 202
    sget-object v16, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 203
    .line 204
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/Alignment$Companion;->getStart()Landroidx/compose/ui/Alignment$Horizontal;

    .line 205
    .line 206
    .line 207
    move-result-object v8

    .line 208
    const/4 v9, 0x0

    .line 209
    invoke-static {v7, v8, v11, v9}, Landroidx/compose/foundation/layout/ColumnKt;->columnMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/ui/Alignment$Horizontal;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 210
    .line 211
    .line 212
    move-result-object v7

    .line 213
    invoke-static {v11, v9}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 214
    .line 215
    .line 216
    move-result v8

    .line 217
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 218
    .line 219
    .line 220
    move-result-object v10

    .line 221
    invoke-static {v11, v0}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 222
    .line 223
    .line 224
    move-result-object v12

    .line 225
    sget-object v13, Landroidx/compose/ui/node/ComposeUiNode;->Companion:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 226
    .line 227
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 228
    .line 229
    .line 230
    move-result-object v2

    .line 231
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 232
    .line 233
    .line 234
    move-result-object v18

    .line 235
    if-nez v18, :cond_e

    .line 236
    .line 237
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 238
    .line 239
    .line 240
    :cond_e
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 241
    .line 242
    .line 243
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 244
    .line 245
    .line 246
    move-result v18

    .line 247
    if-eqz v18, :cond_f

    .line 248
    .line 249
    invoke-interface {v11, v2}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 250
    .line 251
    .line 252
    goto :goto_a

    .line 253
    :cond_f
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 254
    .line 255
    .line 256
    :goto_a
    invoke-static {v11}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 257
    .line 258
    .line 259
    move-result-object v2

    .line 260
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 261
    .line 262
    .line 263
    move-result-object v9

    .line 264
    invoke-static {v2, v7, v9}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 265
    .line 266
    .line 267
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 268
    .line 269
    .line 270
    move-result-object v7

    .line 271
    invoke-static {v2, v10, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 272
    .line 273
    .line 274
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 275
    .line 276
    .line 277
    move-result-object v7

    .line 278
    invoke-interface {v2}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 279
    .line 280
    .line 281
    move-result v9

    .line 282
    if-nez v9, :cond_10

    .line 283
    .line 284
    invoke-interface {v2}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 285
    .line 286
    .line 287
    move-result-object v9

    .line 288
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 289
    .line 290
    .line 291
    move-result-object v10

    .line 292
    invoke-static {v9, v10}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 293
    .line 294
    .line 295
    move-result v9

    .line 296
    if-nez v9, :cond_11

    .line 297
    .line 298
    :cond_10
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 299
    .line 300
    .line 301
    move-result-object v9

    .line 302
    invoke-interface {v2, v9}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 303
    .line 304
    .line 305
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 306
    .line 307
    .line 308
    move-result-object v8

    .line 309
    invoke-interface {v2, v8, v7}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 310
    .line 311
    .line 312
    :cond_11
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 313
    .line 314
    .line 315
    move-result-object v7

    .line 316
    invoke-static {v2, v12, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 317
    .line 318
    .line 319
    sget-object v2, Landroidx/compose/foundation/layout/ColumnScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/ColumnScopeInstance;

    .line 320
    .line 321
    sget-object v2, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 322
    .line 323
    const/4 v7, 0x0

    .line 324
    invoke-static {v2, v7, v14, v6}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxWidth$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 325
    .line 326
    .line 327
    move-result-object v8

    .line 328
    const/16 v9, 0xa

    .line 329
    .line 330
    int-to-float v9, v9

    .line 331
    invoke-static {v9}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 332
    .line 333
    .line 334
    move-result v9

    .line 335
    invoke-static {v9}, Landroidx/compose/foundation/shape/RoundedCornerShapeKt;->RoundedCornerShape-0680j_4(F)Landroidx/compose/foundation/shape/RoundedCornerShape;

    .line 336
    .line 337
    .line 338
    move-result-object v9

    .line 339
    invoke-static {v8, v9}, Landroidx/compose/ui/draw/ClipKt;->clip(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/graphics/Shape;)Landroidx/compose/ui/Modifier;

    .line 340
    .line 341
    .line 342
    move-result-object v8

    .line 343
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/Alignment$Companion;->getTopStart()Landroidx/compose/ui/Alignment;

    .line 344
    .line 345
    .line 346
    move-result-object v9

    .line 347
    const/4 v10, 0x0

    .line 348
    invoke-static {v9, v10}, Landroidx/compose/foundation/layout/BoxKt;->maybeCachedBoxMeasurePolicy(Landroidx/compose/ui/Alignment;Z)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 349
    .line 350
    .line 351
    move-result-object v9

    .line 352
    invoke-static {v11, v10}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 353
    .line 354
    .line 355
    move-result v10

    .line 356
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 357
    .line 358
    .line 359
    move-result-object v12

    .line 360
    invoke-static {v11, v8}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 361
    .line 362
    .line 363
    move-result-object v8

    .line 364
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 365
    .line 366
    .line 367
    move-result-object v6

    .line 368
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 369
    .line 370
    .line 371
    move-result-object v18

    .line 372
    if-nez v18, :cond_12

    .line 373
    .line 374
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 375
    .line 376
    .line 377
    :cond_12
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 378
    .line 379
    .line 380
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 381
    .line 382
    .line 383
    move-result v18

    .line 384
    if-eqz v18, :cond_13

    .line 385
    .line 386
    invoke-interface {v11, v6}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 387
    .line 388
    .line 389
    goto :goto_b

    .line 390
    :cond_13
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 391
    .line 392
    .line 393
    :goto_b
    invoke-static {v11}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 394
    .line 395
    .line 396
    move-result-object v6

    .line 397
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 398
    .line 399
    .line 400
    move-result-object v7

    .line 401
    invoke-static {v6, v9, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 402
    .line 403
    .line 404
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 405
    .line 406
    .line 407
    move-result-object v7

    .line 408
    invoke-static {v6, v12, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 409
    .line 410
    .line 411
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 412
    .line 413
    .line 414
    move-result-object v7

    .line 415
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 416
    .line 417
    .line 418
    move-result v9

    .line 419
    if-nez v9, :cond_14

    .line 420
    .line 421
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 422
    .line 423
    .line 424
    move-result-object v9

    .line 425
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 426
    .line 427
    .line 428
    move-result-object v12

    .line 429
    invoke-static {v9, v12}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 430
    .line 431
    .line 432
    move-result v9

    .line 433
    if-nez v9, :cond_15

    .line 434
    .line 435
    :cond_14
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 436
    .line 437
    .line 438
    move-result-object v9

    .line 439
    invoke-interface {v6, v9}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 440
    .line 441
    .line 442
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 443
    .line 444
    .line 445
    move-result-object v9

    .line 446
    invoke-interface {v6, v9, v7}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 447
    .line 448
    .line 449
    :cond_15
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 450
    .line 451
    .line 452
    move-result-object v7

    .line 453
    invoke-static {v6, v8, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 454
    .line 455
    .line 456
    sget-object v6, Landroidx/compose/foundation/layout/BoxScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 457
    .line 458
    move-object v9, v6

    .line 459
    const/4 v7, 0x0

    .line 460
    const/4 v8, 0x0

    .line 461
    invoke-static {v0, v8, v14, v7}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxWidth$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 462
    .line 463
    .line 464
    move-result-object v6

    .line 465
    sget-object v10, Landroidx/compose/ui/layout/ContentScale;->Companion:Landroidx/compose/ui/layout/ContentScale$Companion;

    .line 466
    .line 467
    invoke-virtual {v10}, Landroidx/compose/ui/layout/ContentScale$Companion;->getCrop()Landroidx/compose/ui/layout/ContentScale;

    .line 468
    .line 469
    .line 470
    move-result-object v10

    .line 471
    and-int/lit8 v12, v15, 0xe

    .line 472
    .line 473
    or-int/lit16 v12, v12, 0x6000

    .line 474
    .line 475
    const/16 v13, 0x2c

    .line 476
    .line 477
    move-object/from16 v18, v7

    .line 478
    .line 479
    const/4 v7, 0x0

    .line 480
    move/from16 v19, v8

    .line 481
    .line 482
    const/4 v8, 0x0

    .line 483
    move-object/from16 v20, v9

    .line 484
    .line 485
    move-object v9, v10

    .line 486
    const/4 v10, 0x0

    .line 487
    move-object/from16 p1, v0

    .line 488
    .line 489
    move-object/from16 p2, v1

    .line 490
    .line 491
    move-object/from16 p3, v3

    .line 492
    .line 493
    move-object/from16 v0, v18

    .line 494
    .line 495
    move/from16 v1, v19

    .line 496
    .line 497
    move-object/from16 v3, v20

    .line 498
    .line 499
    invoke-static/range {v5 .. v13}, Ltop/cycdm/cycapp/ui/common/n1;->f(Ljava/lang/Object;Landroidx/compose/ui/Modifier;Landroidx/compose/ui/Modifier;Ljava/lang/String;Landroidx/compose/ui/layout/ContentScale;Landroidx/compose/ui/graphics/ColorFilter;Landroidx/compose/runtime/Composer;II)V

    .line 500
    .line 501
    .line 502
    invoke-static {v2, v1, v14, v0}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxSize$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 503
    .line 504
    .line 505
    move-result-object v23

    .line 506
    const/16 v27, 0x6

    .line 507
    .line 508
    const/16 v28, 0x0

    .line 509
    .line 510
    const/16 v25, 0x0

    .line 511
    .line 512
    const/16 v26, 0x0

    .line 513
    .line 514
    invoke-static/range {v23 .. v28}, Landroidx/compose/foundation/BackgroundKt;->background$default(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/graphics/Brush;Landroidx/compose/ui/graphics/Shape;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 515
    .line 516
    .line 517
    move-result-object v0

    .line 518
    const/4 v1, 0x6

    .line 519
    invoke-static {v0, v11, v1}, Landroidx/compose/foundation/layout/SpacerKt;->Spacer(Landroidx/compose/ui/Modifier;Landroidx/compose/runtime/Composer;I)V

    .line 520
    .line 521
    .line 522
    if-eqz p2, :cond_16

    .line 523
    .line 524
    const v0, -0x5dcd1847

    .line 525
    .line 526
    .line 527
    invoke-interface {v11, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 528
    .line 529
    .line 530
    invoke-virtual/range {p3 .. p3}, Landroidx/compose/ui/graphics/Color$Companion;->getWhite-0d7_KjU()J

    .line 531
    .line 532
    .line 533
    move-result-wide v7

    .line 534
    const/16 v0, 0xc

    .line 535
    .line 536
    invoke-static {v0}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 537
    .line 538
    .line 539
    move-result-wide v9

    .line 540
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/Alignment$Companion;->getBottomEnd()Landroidx/compose/ui/Alignment;

    .line 541
    .line 542
    .line 543
    move-result-object v0

    .line 544
    invoke-interface {v3, v2, v0}, Landroidx/compose/foundation/layout/BoxScope;->align(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/Alignment;)Landroidx/compose/ui/Modifier;

    .line 545
    .line 546
    .line 547
    move-result-object v16

    .line 548
    int-to-float v0, v1

    .line 549
    invoke-static {v0}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 550
    .line 551
    .line 552
    move-result v19

    .line 553
    const/4 v0, 0x3

    .line 554
    int-to-float v0, v0

    .line 555
    invoke-static {v0}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 556
    .line 557
    .line 558
    move-result v20

    .line 559
    const/16 v21, 0x3

    .line 560
    .line 561
    const/16 v22, 0x0

    .line 562
    .line 563
    const/16 v17, 0x0

    .line 564
    .line 565
    const/16 v18, 0x0

    .line 566
    .line 567
    invoke-static/range {v16 .. v22}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 568
    .line 569
    .line 570
    move-result-object v6

    .line 571
    shr-int/lit8 v0, v15, 0x6

    .line 572
    .line 573
    and-int/lit8 v0, v0, 0xe

    .line 574
    .line 575
    or-int/lit16 v0, v0, 0xd80

    .line 576
    .line 577
    const/16 v28, 0x0

    .line 578
    .line 579
    const v29, 0x1fff0

    .line 580
    .line 581
    .line 582
    move-object/from16 v26, v11

    .line 583
    .line 584
    const/4 v11, 0x0

    .line 585
    const/4 v12, 0x0

    .line 586
    const/4 v13, 0x0

    .line 587
    const-wide/16 v14, 0x0

    .line 588
    .line 589
    const/16 v16, 0x0

    .line 590
    .line 591
    const/16 v17, 0x0

    .line 592
    .line 593
    const-wide/16 v18, 0x0

    .line 594
    .line 595
    const/16 v20, 0x0

    .line 596
    .line 597
    const/16 v21, 0x0

    .line 598
    .line 599
    const/16 v22, 0x0

    .line 600
    .line 601
    const/16 v23, 0x0

    .line 602
    .line 603
    const/16 v24, 0x0

    .line 604
    .line 605
    const/16 v25, 0x0

    .line 606
    .line 607
    move-object/from16 v5, p2

    .line 608
    .line 609
    move/from16 v27, v0

    .line 610
    .line 611
    invoke-static/range {v5 .. v29}, Landroidx/compose/material3/TextKt;->Text--4IGK_g(Ljava/lang/String;Landroidx/compose/ui/Modifier;JJLandroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontFamily;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/text/style/TextAlign;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/TextStyle;Landroidx/compose/runtime/Composer;III)V

    .line 612
    .line 613
    .line 614
    move-object/from16 v11, v26

    .line 615
    .line 616
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 617
    .line 618
    .line 619
    goto :goto_c

    .line 620
    :cond_16
    move-object/from16 v5, p2

    .line 621
    .line 622
    const v0, -0x5dc85f85

    .line 623
    .line 624
    .line 625
    invoke-interface {v11, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 626
    .line 627
    .line 628
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 629
    .line 630
    .line 631
    :goto_c
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 632
    .line 633
    .line 634
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 635
    .line 636
    .line 637
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 638
    .line 639
    .line 640
    move-result v0

    .line 641
    if-eqz v0, :cond_17

    .line 642
    .line 643
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 644
    .line 645
    .line 646
    :cond_17
    move-object/from16 v2, p1

    .line 647
    .line 648
    move-object v3, v5

    .line 649
    :goto_d
    invoke-interface {v11}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 650
    .line 651
    .line 652
    move-result-object v6

    .line 653
    if-eqz v6, :cond_18

    .line 654
    .line 655
    new-instance v0, Ltop/cycdm/cycapp/ui/history/k;

    .line 656
    .line 657
    move-object/from16 v1, p0

    .line 658
    .line 659
    move/from16 v5, p5

    .line 660
    .line 661
    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/history/k;-><init>(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;II)V

    .line 662
    .line 663
    .line 664
    invoke-interface {v6, v0}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 665
    .line 666
    .line 667
    :cond_18
    return-void
.end method

.method public static final W(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    invoke-static {p3}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result v4

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v5, p4

    move-object v3, p5

    invoke-static/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->V(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;Landroidx/compose/runtime/Composer;II)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final synthetic X(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->G(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Y(Landroidx/compose/runtime/State;)Ltop/cycdm/cycapp/ui/history/w;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->L(Landroidx/compose/runtime/State;)Ltop/cycdm/cycapp/ui/history/w;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Z(Landroidx/compose/material3/SwipeToDismissBoxState;Landroidx/compose/runtime/Composer;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->R(Landroidx/compose/material3/SwipeToDismissBoxState;Landroidx/compose/runtime/Composer;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic a()Ltop/cycdm/model/j;
    .locals 1

    .line 1
    invoke-static {}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->D()Ltop/cycdm/model/j;

    move-result-object v0

    return-object v0
.end method

.method public static final a0(J)Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/time/DurationUnit;->MILLISECONDS:Lkotlin/time/DurationUnit;

    .line 2
    .line 3
    invoke-static {p0, p1, v0}, Lkotlin/time/d;->t(JLkotlin/time/DurationUnit;)J

    .line 4
    .line 5
    .line 6
    move-result-wide p0

    .line 7
    sget-object v0, Lp7/b;->a:Lp7/b;

    .line 8
    .line 9
    invoke-virtual {v0, p0, p1}, Lp7/b;->c(J)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    return-object p0
.end method

.method public static synthetic b(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->w(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->v(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->A(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->Q(ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->J(Ltop/cycdm/model/j;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->y(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h(Landroidx/navigation/NavHostController;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->O(Landroidx/navigation/NavHostController;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(Lkotlin/jvm/functions/Function0;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->I(Lkotlin/jvm/functions/Function0;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic j(Landroidx/paging/compose/LazyPagingItems;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->F(Landroidx/paging/compose/LazyPagingItems;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic k(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->B(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic l()Z
    .locals 1

    .line 1
    invoke-static {}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->E()Z

    move-result v0

    return v0
.end method

.method public static synthetic m(Landroidx/compose/runtime/MutableState;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->u(Landroidx/compose/runtime/MutableState;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n()Lkotlin/t;
    .locals 1

    .line 1
    invoke-static {}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->H()Lkotlin/t;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic o(ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->M(ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->W(Ljava/lang/String;Landroidx/compose/ui/Modifier;Ljava/lang/String;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->P(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r(Landroidx/compose/runtime/MutableState;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->z(Landroidx/compose/runtime/MutableState;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s(Landroidx/compose/material3/SwipeToDismissBoxState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->S(Landroidx/compose/material3/SwipeToDismissBoxState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static final t(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/Composer;I)V
    .locals 11

    .line 1
    const v0, -0x6bf2b39d

    .line 2
    .line 3
    .line 4
    invoke-interface {p1, v0}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 5
    .line 6
    .line 7
    move-result-object v6

    .line 8
    and-int/lit8 p1, p2, 0x6

    .line 9
    .line 10
    const/4 v1, 0x2

    .line 11
    const/4 v9, 0x4

    .line 12
    if-nez p1, :cond_1

    .line 13
    .line 14
    invoke-interface {v6, p0}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    if-eqz p1, :cond_0

    .line 19
    .line 20
    move p1, v9

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    move p1, v1

    .line 23
    :goto_0
    or-int/2addr p1, p2

    .line 24
    goto :goto_1

    .line 25
    :cond_1
    move p1, p2

    .line 26
    :goto_1
    and-int/lit8 v2, p1, 0x3

    .line 27
    .line 28
    if-ne v2, v1, :cond_3

    .line 29
    .line 30
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    if-nez v1, :cond_2

    .line 35
    .line 36
    goto :goto_2

    .line 37
    :cond_2
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 38
    .line 39
    .line 40
    move-object v1, p0

    .line 41
    goto/16 :goto_6

    .line 42
    .line 43
    :cond_3
    :goto_2
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 44
    .line 45
    .line 46
    move-result v1

    .line 47
    if-eqz v1, :cond_4

    .line 48
    .line 49
    const/4 v1, -0x1

    .line 50
    const-string v2, "top.cycdm.cycapp.ui.history.ClearHistoryDialog (HistoryScreen.kt:292)"

    .line 51
    .line 52
    invoke-static {v0, p1, v1, v2}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 53
    .line 54
    .line 55
    :cond_4
    const v0, 0x70b323c8

    .line 56
    .line 57
    .line 58
    invoke-interface {v6, v0}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 59
    .line 60
    .line 61
    sget-object v0, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 62
    .line 63
    sget v1, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 64
    .line 65
    invoke-virtual {v0, v6, v1}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 66
    .line 67
    .line 68
    move-result-object v2

    .line 69
    if-eqz v2, :cond_e

    .line 70
    .line 71
    const/4 v0, 0x0

    .line 72
    invoke-static {v2, v6, v0}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 73
    .line 74
    .line 75
    move-result-object v4

    .line 76
    const v1, 0x671a9c9b

    .line 77
    .line 78
    .line 79
    invoke-interface {v6, v1}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 80
    .line 81
    .line 82
    instance-of v1, v2, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 83
    .line 84
    if-eqz v1, :cond_5

    .line 85
    .line 86
    move-object v1, v2

    .line 87
    check-cast v1, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 88
    .line 89
    invoke-interface {v1}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    :goto_3
    move-object v5, v1

    .line 94
    goto :goto_4

    .line 95
    :cond_5
    sget-object v1, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 96
    .line 97
    goto :goto_3

    .line 98
    :goto_4
    const v7, 0x9048

    .line 99
    .line 100
    .line 101
    const/4 v8, 0x0

    .line 102
    const-class v1, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 103
    .line 104
    const/4 v3, 0x0

    .line 105
    invoke-static/range {v1 .. v8}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 110
    .line 111
    .line 112
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 113
    .line 114
    .line 115
    check-cast v1, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 116
    .line 117
    and-int/lit8 p1, p1, 0xe

    .line 118
    .line 119
    const/4 v2, 0x1

    .line 120
    if-ne p1, v9, :cond_6

    .line 121
    .line 122
    move v3, v2

    .line 123
    goto :goto_5

    .line 124
    :cond_6
    move v3, v0

    .line 125
    :goto_5
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v4

    .line 129
    if-nez v3, :cond_7

    .line 130
    .line 131
    sget-object v3, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 132
    .line 133
    invoke-virtual {v3}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 134
    .line 135
    .line 136
    move-result-object v3

    .line 137
    if-ne v4, v3, :cond_8

    .line 138
    .line 139
    :cond_7
    new-instance v4, Ltop/cycdm/cycapp/ui/history/s;

    .line 140
    .line 141
    invoke-direct {v4, p0}, Ltop/cycdm/cycapp/ui/history/s;-><init>(Landroidx/compose/runtime/MutableState;)V

    .line 142
    .line 143
    .line 144
    invoke-interface {v6, v4}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 145
    .line 146
    .line 147
    :cond_8
    move-object v5, v4

    .line 148
    check-cast v5, Lkotlin/jvm/functions/Function0;

    .line 149
    .line 150
    if-ne p1, v9, :cond_9

    .line 151
    .line 152
    move v0, v2

    .line 153
    :cond_9
    invoke-interface {v6, v1}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 154
    .line 155
    .line 156
    move-result v2

    .line 157
    or-int/2addr v0, v2

    .line 158
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 159
    .line 160
    .line 161
    move-result-object v2

    .line 162
    if-nez v0, :cond_a

    .line 163
    .line 164
    sget-object v0, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 165
    .line 166
    invoke-virtual {v0}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 167
    .line 168
    .line 169
    move-result-object v0

    .line 170
    if-ne v2, v0, :cond_b

    .line 171
    .line 172
    :cond_a
    new-instance v2, Ltop/cycdm/cycapp/ui/history/t;

    .line 173
    .line 174
    invoke-direct {v2, p0, v1}, Ltop/cycdm/cycapp/ui/history/t;-><init>(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;)V

    .line 175
    .line 176
    .line 177
    invoke-interface {v6, v2}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 178
    .line 179
    .line 180
    :cond_b
    move-object v7, v2

    .line 181
    check-cast v7, Lkotlin/jvm/functions/Function0;

    .line 182
    .line 183
    const v0, 0x30db0

    .line 184
    .line 185
    .line 186
    or-int v9, p1, v0

    .line 187
    .line 188
    const/4 v10, 0x0

    .line 189
    const-string v2, "\u5220\u9664\u5386\u53f2\u8bb0\u5f55"

    .line 190
    .line 191
    const-string v3, "\u786e\u5b9a\u5220\u9664\u5386\u53f2\u8bb0\u5f55\u5417\uff1f"

    .line 192
    .line 193
    const-string v4, "\u53d6\u6d88"

    .line 194
    .line 195
    move-object v8, v6

    .line 196
    const-string v6, "\u786e\u5b9a"

    .line 197
    .line 198
    move-object v1, p0

    .line 199
    invoke-static/range {v1 .. v10}, Ltop/cycdm/cycapp/ui/common/BaseDialogKt;->u(Landroidx/compose/runtime/MutableState;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/jvm/functions/Function0;Ljava/lang/String;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V

    .line 200
    .line 201
    .line 202
    move-object v6, v8

    .line 203
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 204
    .line 205
    .line 206
    move-result p0

    .line 207
    if-eqz p0, :cond_c

    .line 208
    .line 209
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 210
    .line 211
    .line 212
    :cond_c
    :goto_6
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 213
    .line 214
    .line 215
    move-result-object p0

    .line 216
    if-eqz p0, :cond_d

    .line 217
    .line 218
    new-instance p1, Ltop/cycdm/cycapp/ui/history/c;

    .line 219
    .line 220
    invoke-direct {p1, v1, p2}, Ltop/cycdm/cycapp/ui/history/c;-><init>(Landroidx/compose/runtime/MutableState;I)V

    .line 221
    .line 222
    .line 223
    invoke-interface {p0, p1}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 224
    .line 225
    .line 226
    :cond_d
    return-void

    .line 227
    :cond_e
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 228
    .line 229
    const-string p1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 230
    .line 231
    invoke-direct {p0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 232
    .line 233
    .line 234
    throw p0
.end method

.method public static final u(Landroidx/compose/runtime/MutableState;)Lkotlin/t;
    .locals 1

    .line 1
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 2
    .line 3
    invoke-interface {p0, v0}, Landroidx/compose/runtime/MutableState;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 7
    .line 8
    return-object p0
.end method

.method public static final v(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lkotlin/t;
    .locals 1

    .line 1
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 2
    .line 3
    invoke-interface {p0, v0}, Landroidx/compose/runtime/MutableState;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->clearHistory()Lkotlinx/coroutines/w1;

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final w(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->t(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final x(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/Composer;I)V
    .locals 11

    .line 1
    const v0, -0x5d86b8d7

    .line 2
    .line 3
    .line 4
    invoke-interface {p1, v0}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 5
    .line 6
    .line 7
    move-result-object v6

    .line 8
    and-int/lit8 p1, p2, 0x6

    .line 9
    .line 10
    const/4 v1, 0x2

    .line 11
    const/4 v9, 0x4

    .line 12
    if-nez p1, :cond_1

    .line 13
    .line 14
    invoke-interface {v6, p0}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    if-eqz p1, :cond_0

    .line 19
    .line 20
    move p1, v9

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    move p1, v1

    .line 23
    :goto_0
    or-int/2addr p1, p2

    .line 24
    goto :goto_1

    .line 25
    :cond_1
    move p1, p2

    .line 26
    :goto_1
    and-int/lit8 v2, p1, 0x3

    .line 27
    .line 28
    if-ne v2, v1, :cond_3

    .line 29
    .line 30
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    if-nez v1, :cond_2

    .line 35
    .line 36
    goto :goto_2

    .line 37
    :cond_2
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 38
    .line 39
    .line 40
    goto/16 :goto_6

    .line 41
    .line 42
    :cond_3
    :goto_2
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-eqz v1, :cond_4

    .line 47
    .line 48
    const/4 v1, -0x1

    .line 49
    const-string v2, "top.cycdm.cycapp.ui.history.DeleteHistoryDialog (HistoryScreen.kt:272)"

    .line 50
    .line 51
    invoke-static {v0, p1, v1, v2}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 52
    .line 53
    .line 54
    :cond_4
    invoke-interface {p0}, Landroidx/compose/runtime/MutableState;->getValue()Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    check-cast v0, Ltop/cycdm/model/j;

    .line 59
    .line 60
    if-nez v0, :cond_6

    .line 61
    .line 62
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 63
    .line 64
    .line 65
    move-result p1

    .line 66
    if-eqz p1, :cond_5

    .line 67
    .line 68
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 69
    .line 70
    .line 71
    :cond_5
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    if-eqz p1, :cond_f

    .line 76
    .line 77
    new-instance v0, Ltop/cycdm/cycapp/ui/history/d;

    .line 78
    .line 79
    invoke-direct {v0, p0, p2}, Ltop/cycdm/cycapp/ui/history/d;-><init>(Landroidx/compose/runtime/MutableState;I)V

    .line 80
    .line 81
    .line 82
    invoke-interface {p1, v0}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 83
    .line 84
    .line 85
    return-void

    .line 86
    :cond_6
    const v1, 0x70b323c8

    .line 87
    .line 88
    .line 89
    invoke-interface {v6, v1}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 90
    .line 91
    .line 92
    sget-object v1, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 93
    .line 94
    sget v2, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 95
    .line 96
    invoke-virtual {v1, v6, v2}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 97
    .line 98
    .line 99
    move-result-object v2

    .line 100
    if-eqz v2, :cond_10

    .line 101
    .line 102
    const/4 v10, 0x0

    .line 103
    invoke-static {v2, v6, v10}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 104
    .line 105
    .line 106
    move-result-object v4

    .line 107
    const v1, 0x671a9c9b

    .line 108
    .line 109
    .line 110
    invoke-interface {v6, v1}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 111
    .line 112
    .line 113
    instance-of v1, v2, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 114
    .line 115
    if-eqz v1, :cond_7

    .line 116
    .line 117
    move-object v1, v2

    .line 118
    check-cast v1, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 119
    .line 120
    invoke-interface {v1}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    :goto_3
    move-object v5, v1

    .line 125
    goto :goto_4

    .line 126
    :cond_7
    sget-object v1, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 127
    .line 128
    goto :goto_3

    .line 129
    :goto_4
    const v7, 0x9048

    .line 130
    .line 131
    .line 132
    const/4 v8, 0x0

    .line 133
    const-class v1, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 134
    .line 135
    const/4 v3, 0x0

    .line 136
    invoke-static/range {v1 .. v8}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 137
    .line 138
    .line 139
    move-result-object v1

    .line 140
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 141
    .line 142
    .line 143
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 144
    .line 145
    .line 146
    check-cast v1, Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 147
    .line 148
    move-object v2, v1

    .line 149
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/common/ExtensionKt;->b0(Landroidx/compose/runtime/MutableState;)Landroidx/compose/runtime/MutableState;

    .line 150
    .line 151
    .line 152
    move-result-object v1

    .line 153
    and-int/lit8 p1, p1, 0xe

    .line 154
    .line 155
    const/4 v3, 0x1

    .line 156
    if-ne p1, v9, :cond_8

    .line 157
    .line 158
    move v4, v3

    .line 159
    goto :goto_5

    .line 160
    :cond_8
    move v4, v10

    .line 161
    :goto_5
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object v5

    .line 165
    if-nez v4, :cond_9

    .line 166
    .line 167
    sget-object v4, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 168
    .line 169
    invoke-virtual {v4}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 170
    .line 171
    .line 172
    move-result-object v4

    .line 173
    if-ne v5, v4, :cond_a

    .line 174
    .line 175
    :cond_9
    new-instance v5, Ltop/cycdm/cycapp/ui/history/e;

    .line 176
    .line 177
    invoke-direct {v5, p0}, Ltop/cycdm/cycapp/ui/history/e;-><init>(Landroidx/compose/runtime/MutableState;)V

    .line 178
    .line 179
    .line 180
    invoke-interface {v6, v5}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 181
    .line 182
    .line 183
    :cond_a
    check-cast v5, Lkotlin/jvm/functions/Function0;

    .line 184
    .line 185
    if-ne p1, v9, :cond_b

    .line 186
    .line 187
    move v10, v3

    .line 188
    :cond_b
    invoke-interface {v6, v2}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 189
    .line 190
    .line 191
    move-result p1

    .line 192
    or-int/2addr p1, v10

    .line 193
    invoke-interface {v6, v0}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 194
    .line 195
    .line 196
    move-result v3

    .line 197
    or-int/2addr p1, v3

    .line 198
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 199
    .line 200
    .line 201
    move-result-object v3

    .line 202
    if-nez p1, :cond_c

    .line 203
    .line 204
    sget-object p1, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 205
    .line 206
    invoke-virtual {p1}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    if-ne v3, p1, :cond_d

    .line 211
    .line 212
    :cond_c
    new-instance v3, Ltop/cycdm/cycapp/ui/history/f;

    .line 213
    .line 214
    invoke-direct {v3, p0, v2, v0}, Ltop/cycdm/cycapp/ui/history/f;-><init>(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)V

    .line 215
    .line 216
    .line 217
    invoke-interface {v6, v3}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 218
    .line 219
    .line 220
    :cond_d
    move-object v7, v3

    .line 221
    check-cast v7, Lkotlin/jvm/functions/Function0;

    .line 222
    .line 223
    const v9, 0x30db0

    .line 224
    .line 225
    .line 226
    const/4 v10, 0x0

    .line 227
    const-string v2, "\u5220\u9664\u5386\u53f2\u8bb0\u5f55"

    .line 228
    .line 229
    const-string v3, "\u786e\u5b9a\u5220\u9664\u5386\u53f2\u8bb0\u5f55\u5417\uff1f"

    .line 230
    .line 231
    const-string v4, "\u53d6\u6d88"

    .line 232
    .line 233
    move-object v8, v6

    .line 234
    const-string v6, "\u786e\u5b9a"

    .line 235
    .line 236
    invoke-static/range {v1 .. v10}, Ltop/cycdm/cycapp/ui/common/BaseDialogKt;->u(Landroidx/compose/runtime/MutableState;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/jvm/functions/Function0;Ljava/lang/String;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V

    .line 237
    .line 238
    .line 239
    move-object v6, v8

    .line 240
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 241
    .line 242
    .line 243
    move-result p1

    .line 244
    if-eqz p1, :cond_e

    .line 245
    .line 246
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 247
    .line 248
    .line 249
    :cond_e
    :goto_6
    invoke-interface {v6}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 250
    .line 251
    .line 252
    move-result-object p1

    .line 253
    if-eqz p1, :cond_f

    .line 254
    .line 255
    new-instance v0, Ltop/cycdm/cycapp/ui/history/g;

    .line 256
    .line 257
    invoke-direct {v0, p0, p2}, Ltop/cycdm/cycapp/ui/history/g;-><init>(Landroidx/compose/runtime/MutableState;I)V

    .line 258
    .line 259
    .line 260
    invoke-interface {p1, v0}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 261
    .line 262
    .line 263
    :cond_f
    return-void

    .line 264
    :cond_10
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 265
    .line 266
    const-string p1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 267
    .line 268
    invoke-direct {p0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 269
    .line 270
    .line 271
    throw p0
.end method

.method public static final y(Landroidx/compose/runtime/MutableState;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->x(Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final z(Landroidx/compose/runtime/MutableState;)Lkotlin/t;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-interface {p0, v0}, Landroidx/compose/runtime/MutableState;->setValue(Ljava/lang/Object;)V

    .line 3
    .line 4
    .line 5
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 6
    .line 7
    return-object p0
.end method
