.class public Lcom/beizi/ad/model/d$c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/beizi/ad/model/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "c"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/beizi/ad/model/d$c$a;
    }
.end annotation


# instance fields
.field private a:Lcom/beizi/ad/model/e$d;

.field private b:Lcom/beizi/ad/model/e$c;

.field private c:Lcom/beizi/ad/model/d$b;


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lcom/beizi/ad/model/d$1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/model/d$c;-><init>()V

    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/model/d$c;Lcom/beizi/ad/model/d$b;)Lcom/beizi/ad/model/d$b;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/model/d$c;->c:Lcom/beizi/ad/model/d$b;

    return-object p1
.end method

.method public static synthetic a(Lcom/beizi/ad/model/d$c;Lcom/beizi/ad/model/e$c;)Lcom/beizi/ad/model/e$c;
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/d$c;->b:Lcom/beizi/ad/model/e$c;

    return-object p1
.end method

.method public static synthetic a(Lcom/beizi/ad/model/d$c;Lcom/beizi/ad/model/e$d;)Lcom/beizi/ad/model/e$d;
    .locals 0

    .line 3
    iput-object p1, p0, Lcom/beizi/ad/model/d$c;->a:Lcom/beizi/ad/model/e$d;

    return-object p1
.end method


# virtual methods
.method public a()Lorg/json/JSONObject;
    .locals 3

    .line 4
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    .line 5
    :try_start_0
    const-string v1, "net"

    iget-object v2, p0, Lcom/beizi/ad/model/d$c;->a:Lcom/beizi/ad/model/e$d;

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    .line 6
    const-string v1, "isp"

    iget-object v2, p0, Lcom/beizi/ad/model/d$c;->b:Lcom/beizi/ad/model/e$c;

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    .line 7
    iget-object v1, p0, Lcom/beizi/ad/model/d$c;->c:Lcom/beizi/ad/model/d$b;

    if-eqz v1, :cond_0

    .line 8
    const-string v2, "geo"

    invoke-virtual {v1}, Lcom/beizi/ad/model/d$b;->a()Lorg/json/JSONObject;

    move-result-object v1

    invoke-virtual {v0, v2, v1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v0

    :catch_0
    move-exception v0

    goto :goto_0

    :cond_0
    return-object v0

    .line 9
    :goto_0
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    const/4 v0, 0x0

    return-object v0
.end method

.method public b()[B
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/beizi/ad/model/d$c;->a()Lorg/json/JSONObject;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    return-object v0

    .line 9
    :cond_0
    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Ljava/lang/String;->getBytes()[B

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    return-object v0
.end method
