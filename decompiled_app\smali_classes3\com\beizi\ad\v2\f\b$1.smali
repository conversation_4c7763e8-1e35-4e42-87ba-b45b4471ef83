.class Lcom/beizi/ad/v2/f/b$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/f/b;->a(Landroid/view/View;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/f/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->a(Lcom/beizi/ad/v2/f/b;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/beizi/ad/v2/f/b;->t()V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->b(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/internal/f/c;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 20
    .line 21
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->e(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/internal/f/c;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 26
    .line 27
    invoke-static {v1}, Lcom/beizi/ad/v2/f/b;->c(Lcom/beizi/ad/v2/f/b;)Landroid/view/ViewGroup;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget-object v2, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 32
    .line 33
    invoke-static {v2}, Lcom/beizi/ad/v2/f/b;->d(Lcom/beizi/ad/v2/f/b;)Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-virtual {v0, v1, v2}, Lcom/beizi/ad/internal/f/c;->a(Landroid/view/View;Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    goto :goto_0

    .line 41
    :catch_0
    move-exception v0

    .line 42
    goto :goto_1

    .line 43
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 44
    .line 45
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->f(Lcom/beizi/ad/v2/f/b;)Z

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    if-eqz v0, :cond_1

    .line 50
    .line 51
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 52
    .line 53
    const/4 v1, 0x1

    .line 54
    invoke-static {v0, v1}, Lcom/beizi/ad/v2/f/b;->a(Lcom/beizi/ad/v2/f/b;Z)Z

    .line 55
    .line 56
    .line 57
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 62
    .line 63
    invoke-static {v1}, Lcom/beizi/ad/v2/f/b;->g(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/internal/a/c;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    invoke-virtual {v0, v1}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/a/c;)V

    .line 68
    .line 69
    .line 70
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 71
    .line 72
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->h(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/a;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    if-eqz v0, :cond_2

    .line 77
    .line 78
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 79
    .line 80
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->h(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/a;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    invoke-virtual {v0}, Lcom/beizi/ad/a;->b()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 85
    .line 86
    .line 87
    return-void

    .line 88
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 89
    .line 90
    .line 91
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$1;->a:Lcom/beizi/ad/v2/f/b;

    .line 92
    .line 93
    const/16 v1, 0xb

    .line 94
    .line 95
    invoke-virtual {v0, v1}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 96
    .line 97
    .line 98
    :cond_2
    return-void
.end method
