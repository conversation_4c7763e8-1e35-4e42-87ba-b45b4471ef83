.class public final synthetic Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlinx/coroutines/o0;

.field public final synthetic b:Landroidx/compose/foundation/lazy/grid/LazyGridState;


# direct methods
.method public synthetic constructor <init>(Lkotlinx/coroutines/o0;Landroidx/compose/foundation/lazy/grid/LazyGridState;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/grid/LazyGridState;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/grid/LazyGridState;

    invoke-static {v0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;Landroidx/compose/foundation/lazy/grid/LazyGridState;)Lkotlin/t;

    move-result-object v0

    return-object v0
.end method
