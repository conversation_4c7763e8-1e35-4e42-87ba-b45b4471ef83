.class final Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;
.super Landroid/animation/AnimatorListenerAdapter;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aH()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    invoke-direct {p0}, Landroid/animation/AnimatorListenerAdapter;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onAnimationEnd(Landroid/animation/Animator;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, Landroid/animation/AnimatorListenerAdapter;->onAnimationEnd(Landroid/animation/Animator;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 5
    .line 6
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->y(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/core/webview/jshandler/ba;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    if-eqz p1, :cond_0

    .line 11
    .line 12
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 13
    .line 14
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->y(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/core/webview/jshandler/ba;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-virtual {p1}, Lcom/kwad/components/core/webview/jshandler/ba;->uw()V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public final onAnimationStart(Landroid/animation/Animator;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, Landroid/animation/AnimatorListenerAdapter;->onAnimationStart(Landroid/animation/Animator;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 5
    .line 6
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->y(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/core/webview/jshandler/ba;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    if-eqz p1, :cond_0

    .line 11
    .line 12
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 13
    .line 14
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->y(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/core/webview/jshandler/ba;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-virtual {p1}, Lcom/kwad/components/core/webview/jshandler/ba;->uv()V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method
