.class public final Lcom/kwad/components/ct/detail/b/a/a;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field private asc:Z

.field private awb:Landroid/widget/LinearLayout;

.field private awc:Landroid/widget/TextView;

.field private awd:Landroid/widget/TextView;

.field private awe:Landroid/widget/TextView;

.field private fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private bo(I)V
    .locals 2

    .line 1
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 6
    .line 7
    invoke-virtual {v0, v1, p1}, Lcom/kwad/components/ct/e/b;->e(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method private handleAdClick()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 4
    .line 5
    instance-of v0, v0, Lcom/kwad/components/ct/detail/ad/a;

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-static {}, Lcom/kwad/components/core/t/d;->sM()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 21
    .line 22
    iget-object v2, v2, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 23
    .line 24
    invoke-virtual {v0, v2, v1}, Lcom/kwad/components/ct/e/b;->b(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 25
    .line 26
    .line 27
    :cond_0
    new-instance v0, Lcom/kwad/components/core/e/d/a$a;

    .line 28
    .line 29
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-direct {v0, v2}, Lcom/kwad/components/core/e/d/a$a;-><init>(Landroid/content/Context;)V

    .line 34
    .line 35
    .line 36
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 37
    .line 38
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->aC(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/components/core/e/d/a$a;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/a;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 43
    .line 44
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->b(Lcom/kwad/components/core/e/d/c;)Lcom/kwad/components/core/e/d/a$a;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    const/4 v2, 0x2

    .line 49
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->ap(I)Lcom/kwad/components/core/e/d/a$a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    const/4 v2, 0x0

    .line 54
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->aq(Z)Lcom/kwad/components/core/e/d/a$a;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    const/16 v2, 0x18

    .line 59
    .line 60
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->ao(I)Lcom/kwad/components/core/e/d/a$a;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/a;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 65
    .line 66
    invoke-virtual {v2}, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;->getTouchCoords()Lcom/kwad/sdk/utils/ai$a;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->d(Lcom/kwad/sdk/utils/ai$a;)Lcom/kwad/components/core/e/d/a$a;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/a$a;->as(Z)Lcom/kwad/components/core/e/d/a$a;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-static {v0}, Lcom/kwad/components/core/e/d/a;->a(Lcom/kwad/components/core/e/d/a$a;)I

    .line 79
    .line 80
    .line 81
    return-void
.end method

.method private zW()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 4
    .line 5
    iget-object v1, v1, Lcom/kwad/sdk/core/response/model/AdTemplate;->mAdScene:Lcom/kwad/sdk/internal/api/SceneImpl;

    .line 6
    .line 7
    if-eqz v1, :cond_1

    .line 8
    .line 9
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alV:Lcom/kwad/components/ct/request/live/model/LiveStatusResultData$LiveStatus;

    .line 10
    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    iget-boolean v0, v0, Lcom/kwad/components/ct/detail/c;->alU:Z

    .line 14
    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/16 v0, 0x9

    .line 19
    .line 20
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/b/a/a;->bo(I)V

    .line 21
    .line 22
    .line 23
    const-class v0, Lcom/kwad/components/ec/api/EcLiveComponents;

    .line 24
    .line 25
    invoke-static {v0}, Lcom/kwad/sdk/components/d;->g(Ljava/lang/Class;)Lcom/kwad/sdk/components/b;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    check-cast v0, Lcom/kwad/components/ec/api/EcLiveComponents;

    .line 30
    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 34
    .line 35
    .line 36
    :cond_1
    :goto_0
    return-void
.end method

.method private zX()V
    .locals 5

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->asc:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/a;->ay(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Lcom/kwad/components/ct/response/model/CtPhotoInfo;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 13
    .line 14
    iget-object v1, v1, Lcom/kwad/sdk/core/response/model/AdTemplate;->mAdScene:Lcom/kwad/sdk/internal/api/SceneImpl;

    .line 15
    .line 16
    if-eqz v1, :cond_1

    .line 17
    .line 18
    const/4 v2, 0x3

    .line 19
    invoke-direct {p0, v2}, Lcom/kwad/components/ct/detail/b/a/a;->bo(I)V

    .line 20
    .line 21
    .line 22
    new-instance v2, Lcom/kwad/components/ct/profile/home/<USER>

    .line 23
    .line 24
    invoke-direct {v2}, Lcom/kwad/components/ct/profile/home/<USER>

    .line 25
    .line 26
    .line 27
    iget-wide v3, v1, Lcom/kwad/sdk/internal/api/SceneImpl;->entryScene:J

    .line 28
    .line 29
    iput-wide v3, v2, Lcom/kwad/components/ct/profile/home/<USER>

    .line 30
    .line 31
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/h;->j(Lcom/kwad/sdk/core/response/model/PhotoInfo;)J

    .line 32
    .line 33
    .line 34
    move-result-wide v0

    .line 35
    iput-wide v0, v2, Lcom/kwad/components/ct/profile/home/<USER>

    .line 36
    .line 37
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 38
    .line 39
    iput-object v0, v2, Lcom/kwad/components/ct/profile/home/<USER>/kwad/components/ct/response/model/CtAdTemplate;

    .line 40
    .line 41
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-static {v0, v2}, Lcom/kwad/components/ct/profile/home/<USER>/content/Context;Lcom/kwad/components/ct/profile/home/<USER>

    .line 46
    .line 47
    .line 48
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 49
    .line 50
    const/4 v1, 0x1

    .line 51
    iput-boolean v1, v0, Lcom/kwad/components/ct/response/model/CtAdTemplate;->mIsNotNeedAvatarGuider:Z

    .line 52
    .line 53
    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 5

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 13
    .line 14
    invoke-static {v1}, Lcom/kwad/components/ct/response/a/a;->aJ(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-static {v0}, Lcom/kwad/sdk/utils/bq;->isNullString(Ljava/lang/String;)Z

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-eqz v1, :cond_0

    .line 23
    .line 24
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 25
    .line 26
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    sget v1, Lcom/kwad/sdk/R$string;->ksad_ad_default_username:I

    .line 37
    .line 38
    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    :cond_0
    invoke-static {v0}, Lcom/kwad/sdk/utils/bq;->isNullString(Ljava/lang/String;)Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    const/4 v2, 0x0

    .line 47
    const/16 v3, 0x8

    .line 48
    .line 49
    if-nez v1, :cond_2

    .line 50
    .line 51
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 52
    .line 53
    invoke-static {v1}, Lcom/kwad/components/ct/response/a/a;->aW(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Z

    .line 54
    .line 55
    .line 56
    move-result v1

    .line 57
    if-eqz v1, :cond_1

    .line 58
    .line 59
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 60
    .line 61
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 62
    .line 63
    iget-object v1, v1, Lcom/kwad/components/ct/home/<USER>/kwad/components/ct/api/tube/KSTubeParamInner;

    .line 64
    .line 65
    iget-boolean v1, v1, Lcom/kwad/components/ct/api/tube/KSTubeParamInner;->hideDetailBottomTitle:Z

    .line 66
    .line 67
    if-eqz v1, :cond_1

    .line 68
    .line 69
    goto :goto_0

    .line 70
    :cond_1
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->awc:Landroid/widget/TextView;

    .line 71
    .line 72
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 73
    .line 74
    .line 75
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awc:Landroid/widget/TextView;

    .line 76
    .line 77
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 78
    .line 79
    invoke-static {v1}, Lcom/kwad/components/ct/response/a/a;->aK(Lcom/kwad/components/ct/response/model/CtAdTemplate;)I

    .line 80
    .line 81
    .line 82
    move-result v1

    .line 83
    int-to-float v1, v1

    .line 84
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextSize(F)V

    .line 85
    .line 86
    .line 87
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awc:Landroid/widget/TextView;

    .line 88
    .line 89
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 90
    .line 91
    .line 92
    goto :goto_1

    .line 93
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awc:Landroid/widget/TextView;

    .line 94
    .line 95
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 96
    .line 97
    .line 98
    :goto_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 99
    .line 100
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 101
    .line 102
    .line 103
    move-result v0

    .line 104
    if-nez v0, :cond_5

    .line 105
    .line 106
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 107
    .line 108
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/a;->aW(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Z

    .line 109
    .line 110
    .line 111
    move-result v0

    .line 112
    if-eqz v0, :cond_3

    .line 113
    .line 114
    goto :goto_2

    .line 115
    :cond_3
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 116
    .line 117
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/a;->aU(Lcom/kwad/components/ct/response/model/CtAdTemplate;)J

    .line 118
    .line 119
    .line 120
    move-result-wide v0

    .line 121
    invoke-static {v0, v1}, Lcom/kwad/sdk/utils/bq;->bK(J)Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    invoke-static {v0}, Lcom/kwad/sdk/utils/bq;->isNullString(Ljava/lang/String;)Z

    .line 126
    .line 127
    .line 128
    move-result v1

    .line 129
    if-eqz v1, :cond_4

    .line 130
    .line 131
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awd:Landroid/widget/TextView;

    .line 132
    .line 133
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 134
    .line 135
    .line 136
    goto :goto_3

    .line 137
    :cond_4
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->awd:Landroid/widget/TextView;

    .line 138
    .line 139
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 140
    .line 141
    .line 142
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awd:Landroid/widget/TextView;

    .line 143
    .line 144
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 145
    .line 146
    .line 147
    goto :goto_3

    .line 148
    :cond_5
    :goto_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awd:Landroid/widget/TextView;

    .line 149
    .line 150
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 151
    .line 152
    .line 153
    :goto_3
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 154
    .line 155
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/a;->ay(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Lcom/kwad/components/ct/response/model/CtPhotoInfo;

    .line 156
    .line 157
    .line 158
    move-result-object v0

    .line 159
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/c;->j(Lcom/kwad/components/ct/response/model/CtPhotoInfo;)Ljava/lang/String;

    .line 160
    .line 161
    .line 162
    move-result-object v0

    .line 163
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 164
    .line 165
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 166
    .line 167
    .line 168
    move-result v1

    .line 169
    if-nez v1, :cond_6

    .line 170
    .line 171
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 172
    .line 173
    .line 174
    move-result v1

    .line 175
    if-nez v1, :cond_6

    .line 176
    .line 177
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/a;->awe:Landroid/widget/TextView;

    .line 178
    .line 179
    new-instance v3, Ljava/lang/StringBuilder;

    .line 180
    .line 181
    const-string v4, "\u53d1\u5e03\u4e8e "

    .line 182
    .line 183
    invoke-direct {v3, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 184
    .line 185
    .line 186
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 187
    .line 188
    .line 189
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 190
    .line 191
    .line 192
    move-result-object v0

    .line 193
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 194
    .line 195
    .line 196
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awe:Landroid/widget/TextView;

    .line 197
    .line 198
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 199
    .line 200
    .line 201
    goto :goto_4

    .line 202
    :cond_6
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awe:Landroid/widget/TextView;

    .line 203
    .line 204
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 205
    .line 206
    .line 207
    :goto_4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awb:Landroid/widget/LinearLayout;

    .line 208
    .line 209
    invoke-virtual {v0, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 210
    .line 211
    .line 212
    return-void
.end method

.method public final onClick(Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/a/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/a/a;->handleAdClick()V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 14
    .line 15
    iget-boolean p1, p1, Lcom/kwad/components/ct/detail/c;->alU:Z

    .line 16
    .line 17
    if-eqz p1, :cond_1

    .line 18
    .line 19
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/a/a;->zW()V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/a/a;->zX()V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_root_container:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 13
    .line 14
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_author_name_container:I

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    check-cast v0, Landroid/widget/LinearLayout;

    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awb:Landroid/widget/LinearLayout;

    .line 23
    .line 24
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_author_name:I

    .line 25
    .line 26
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, Landroid/widget/TextView;

    .line 31
    .line 32
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awc:Landroid/widget/TextView;

    .line 33
    .line 34
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_play_times:I

    .line 35
    .line 36
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    check-cast v0, Landroid/widget/TextView;

    .line 41
    .line 42
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awd:Landroid/widget/TextView;

    .line 43
    .line 44
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_author_area:I

    .line 45
    .line 46
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    check-cast v0, Landroid/widget/TextView;

    .line 51
    .line 52
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->awe:Landroid/widget/TextView;

    .line 53
    .line 54
    invoke-static {}, Lcom/kwad/components/ct/detail/a/b;->yg()Z

    .line 55
    .line 56
    .line 57
    move-result v0

    .line 58
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/a/a;->asc:Z

    .line 59
    .line 60
    return-void
.end method

.method public final onUnbind()V
    .locals 0

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
