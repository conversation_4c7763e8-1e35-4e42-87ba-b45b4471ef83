.class public final Lcom/kwad/components/ct/detail/ad/presenter/a/b;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private final adW:Lcom/kwad/components/core/webview/jshandler/bl$a;

.field private final air:Ljava/lang/Runnable;

.field private alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

.field private alX:Lcom/kwad/components/ct/detail/e/a;

.field private amN:Landroid/view/ViewGroup;

.field private amO:Z

.field private amP:Z

.field private amQ:Z

.field private final amR:I

.field private final amS:I

.field private final amT:I

.field private final amU:I

.field private final amV:Lcom/kwad/components/ct/detail/e/a$a;

.field private amW:Landroid/widget/RelativeLayout;

.field private amX:I

.field private final amY:Ljava/lang/Runnable;

.field private final amo:Lcom/kwad/components/core/j/a;

.field private amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

.field private eA:Landroid/animation/ValueAnimator;

.field private eB:Landroid/animation/ValueAnimator;

.field private eF:Landroid/widget/FrameLayout;

.field private es:Lcom/kwad/components/core/webview/a;

.field private et:Lcom/kwad/sdk/core/webview/b;

.field private eu:I

.field private ev:Lcom/kwad/components/core/webview/jshandler/ba;

.field private final ey:Lcom/kwad/components/core/webview/jshandler/al$b;

.field private final ez:Lcom/kwad/components/core/webview/jshandler/at$b;

.field private fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

.field private final mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

.field private final mWebCardCloseListener:Lcom/kwad/sdk/core/webview/d/a/b;

.field private time:J


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, -0x1

    .line 5
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eu:I

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amO:Z

    .line 9
    .line 10
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amP:Z

    .line 11
    .line 12
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amQ:Z

    .line 13
    .line 14
    const/16 v0, 0xc

    .line 15
    .line 16
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amR:I

    .line 17
    .line 18
    const/16 v0, 0x5d

    .line 19
    .line 20
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amS:I

    .line 21
    .line 22
    const/16 v0, 0x190

    .line 23
    .line 24
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amT:I

    .line 25
    .line 26
    const/16 v0, 0xf

    .line 27
    .line 28
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amU:I

    .line 29
    .line 30
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$1;

    .line 31
    .line 32
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 33
    .line 34
    .line 35
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amV:Lcom/kwad/components/ct/detail/e/a$a;

    .line 36
    .line 37
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;

    .line 38
    .line 39
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$7;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 40
    .line 41
    .line 42
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 43
    .line 44
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;

    .line 45
    .line 46
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$8;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 47
    .line 48
    .line 49
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amo:Lcom/kwad/components/core/j/a;

    .line 50
    .line 51
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$12;

    .line 52
    .line 53
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$12;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 54
    .line 55
    .line 56
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mWebCardCloseListener:Lcom/kwad/sdk/core/webview/d/a/b;

    .line 57
    .line 58
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$13;

    .line 59
    .line 60
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$13;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 61
    .line 62
    .line 63
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->ey:Lcom/kwad/components/core/webview/jshandler/al$b;

    .line 64
    .line 65
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$14;

    .line 66
    .line 67
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$14;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 68
    .line 69
    .line 70
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->adW:Lcom/kwad/components/core/webview/jshandler/bl$a;

    .line 71
    .line 72
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$2;

    .line 73
    .line 74
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 75
    .line 76
    .line 77
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->ez:Lcom/kwad/components/core/webview/jshandler/at$b;

    .line 78
    .line 79
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$3;

    .line 80
    .line 81
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$3;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 82
    .line 83
    .line 84
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->air:Ljava/lang/Runnable;

    .line 85
    .line 86
    new-instance v1, Lcom/kwad/components/core/t/s;

    .line 87
    .line 88
    invoke-direct {v1, v0}, Lcom/kwad/components/core/t/s;-><init>(Ljava/lang/Runnable;)V

    .line 89
    .line 90
    .line 91
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amY:Ljava/lang/Runnable;

    .line 92
    .line 93
    return-void
.end method

.method private static H(Landroid/view/View;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 6
    .line 7
    const/4 v1, -0x1

    .line 8
    iput v1, v0, Landroid/view/ViewGroup$MarginLayoutParams;->height:I

    .line 9
    .line 10
    iput v1, v0, Landroid/view/ViewGroup$MarginLayoutParams;->width:I

    .line 11
    .line 12
    const/4 v1, 0x0

    .line 13
    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method private I(Landroid/view/View;)V
    .locals 1

    .line 1
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$6;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$6;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p1, v0}, Lcom/kwad/sdk/c/a/a;->a(Landroid/view/View;Landroid/view/View$OnKeyListener;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;I)I
    .locals 0

    .line 1
    iput p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eu:I

    return p1
.end method

.method private a(Lcom/kwad/components/core/webview/a;)V
    .locals 5

    .line 5
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ac;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    new-instance v3, Lcom/kwad/components/ct/detail/ad/presenter/a/b$9;

    invoke-direct {v3, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$9;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    const/4 v4, 0x0

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/kwad/components/core/webview/jshandler/ac;-><init>(Lcom/kwad/sdk/core/webview/b;Lcom/kwad/components/core/e/d/c;Lcom/kwad/sdk/core/webview/d/a/a;B)V

    .line 6
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 7
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/z;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    new-instance v3, Lcom/kwad/components/ct/detail/ad/presenter/a/b$10;

    invoke-direct {v3, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$10;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    invoke-direct {v0, v1, v2, v3}, Lcom/kwad/components/core/webview/jshandler/z;-><init>(Lcom/kwad/sdk/core/webview/b;Lcom/kwad/components/core/e/d/c;Lcom/kwad/sdk/core/webview/d/a/a;)V

    .line 8
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 9
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ag;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/ag;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 10
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/aj;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/aj;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 11
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ae;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/ae;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 12
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/am;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/kwad/components/core/webview/jshandler/am;-><init>(Lcom/kwad/sdk/core/webview/b;Lcom/kwad/components/core/webview/jshandler/am$b;)V

    .line 13
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 14
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/at;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->ez:Lcom/kwad/components/core/webview/jshandler/at$b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 15
    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/b;->cR(Lcom/kwad/sdk/core/response/model/AdTemplate;)Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lcom/kwad/components/core/webview/jshandler/at;-><init>(Lcom/kwad/components/core/webview/jshandler/at$b;Ljava/lang/String;)V

    .line 16
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 17
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/ba;

    invoke-direct {v0}, Lcom/kwad/components/core/webview/jshandler/ba;-><init>()V

    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->ev:Lcom/kwad/components/core/webview/jshandler/ba;

    .line 18
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 19
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/bd;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    invoke-direct {v0, v1, v2}, Lcom/kwad/components/core/webview/jshandler/bd;-><init>(Lcom/kwad/sdk/core/webview/b;Lcom/kwad/components/core/e/d/c;)V

    .line 20
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 21
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/al;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->ey:Lcom/kwad/components/core/webview/jshandler/al$b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/al;-><init>(Lcom/kwad/components/core/webview/jshandler/al$b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 22
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/an;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/an;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    .line 23
    new-instance v1, Lcom/kwad/components/core/webview/jshandler/o;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v1, v2}, Lcom/kwad/components/core/webview/jshandler/o;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v1}, Lcom/kwad/components/core/webview/a;->b(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 24
    new-instance v1, Lcom/kwad/components/core/webview/jshandler/n;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    invoke-direct {v1, v2}, Lcom/kwad/components/core/webview/jshandler/n;-><init>(Lcom/kwad/sdk/core/webview/b;)V

    invoke-virtual {p1, v1}, Lcom/kwad/components/core/webview/a;->b(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 25
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/a/b$11;

    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$11;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    invoke-virtual {v0, v1}, Lcom/kwad/components/core/webview/jshandler/an;->a(Lcom/kwad/components/core/webview/jshandler/an$a;)V

    .line 26
    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 27
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/bl;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->adW:Lcom/kwad/components/core/webview/jshandler/bl$a;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/bl;-><init>(Lcom/kwad/components/core/webview/jshandler/bl$a;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 28
    new-instance v0, Lcom/kwad/components/core/webview/tachikoma/b/f;

    invoke-direct {v0}, Lcom/kwad/components/core/webview/tachikoma/b/f;-><init>()V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    .line 29
    new-instance v0, Lcom/kwad/components/core/webview/jshandler/aa;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mWebCardCloseListener:Lcom/kwad/sdk/core/webview/d/a/b;

    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/jshandler/aa;-><init>(Lcom/kwad/sdk/core/webview/d/a/b;)V

    invoke-virtual {p1, v0}, Lcom/kwad/components/core/webview/a;->a(Lcom/kwad/sdk/core/webview/c/a;)V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Landroid/view/View;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->I(Landroid/view/View;)V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Z
    .locals 0

    .line 3
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amQ:Z

    return p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)Z
    .locals 0

    const/4 p1, 0x0

    .line 4
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amO:Z

    return p1
.end method

.method private aC()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    .line 2
    .line 3
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 4
    .line 5
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/core/webview/b;->setAdTemplate(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    .line 11
    .line 12
    const/4 v1, 0x0

    .line 13
    iput v1, v0, Lcom/kwad/sdk/core/webview/b;->mScreenOrientation:I

    .line 14
    .line 15
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 16
    .line 17
    iput-object v1, v0, Lcom/kwad/sdk/core/webview/b;->bIX:Lcom/kwad/sdk/widget/i;

    .line 18
    .line 19
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 20
    .line 21
    iput-object v1, v0, Lcom/kwad/sdk/core/webview/b;->SI:Landroid/view/ViewGroup;

    .line 22
    .line 23
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 24
    .line 25
    iput-object v1, v0, Lcom/kwad/sdk/core/webview/b;->Sc:Landroid/webkit/WebView;

    .line 26
    .line 27
    return-void
.end method

.method private aE()V
    .locals 3
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "SetJavaScriptEnabled",
            "AddJavascriptInterface",
            "JavascriptInterface"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aF()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/core/webview/a;

    .line 5
    .line 6
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 7
    .line 8
    invoke-direct {v0, v1}, Lcom/kwad/components/core/webview/a;-><init>(Landroid/webkit/WebView;)V

    .line 9
    .line 10
    .line 11
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->es:Lcom/kwad/components/core/webview/a;

    .line 12
    .line 13
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->a(Lcom/kwad/components/core/webview/a;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 17
    .line 18
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->es:Lcom/kwad/components/core/webview/a;

    .line 19
    .line 20
    const-string v2, "KwaiAd"

    .line 21
    .line 22
    invoke-virtual {v0, v1, v2}, Landroid/webkit/WebView;->addJavascriptInterface(Ljava/lang/Object;Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method private aF()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->es:Lcom/kwad/components/core/webview/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/kwad/components/core/webview/a;->destroy()V

    .line 6
    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->es:Lcom/kwad/components/core/webview/a;

    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method private aH()V
    .locals 3

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aL()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amN:Landroid/view/ViewGroup;

    .line 5
    .line 6
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 7
    .line 8
    iget v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amX:I

    .line 9
    .line 10
    invoke-static {v0, v1, v2}, Lcom/kwad/components/core/t/r;->a(Landroid/view/View;Landroid/view/View;I)Landroid/animation/ValueAnimator;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eA:Landroid/animation/ValueAnimator;

    .line 15
    .line 16
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;

    .line 17
    .line 18
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$4;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0, v1}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eA:Landroid/animation/ValueAnimator;

    .line 25
    .line 26
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method private aL()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eA:Landroid/animation/ValueAnimator;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->removeAllListeners()V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eA:Landroid/animation/ValueAnimator;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 11
    .line 12
    .line 13
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eB:Landroid/animation/ValueAnimator;

    .line 14
    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {v0}, Landroid/animation/Animator;->removeAllListeners()V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eB:Landroid/animation/ValueAnimator;

    .line 21
    .line 22
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 23
    .line 24
    .line 25
    :cond_1
    return-void
.end method

.method private aM()V
    .locals 3

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eu:I

    .line 2
    .line 3
    const/4 v1, -0x1

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    const-string v0, "timeout"

    .line 7
    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/4 v1, 0x1

    .line 10
    if-eq v0, v1, :cond_1

    .line 11
    .line 12
    const-string v0, "h5error"

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_1
    const-string v0, "others"

    .line 16
    .line 17
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    .line 18
    .line 19
    const-string v2, "show webCard fail, reason: "

    .line 20
    .line 21
    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 25
    .line 26
    .line 27
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    const-string v1, "ActionBarWebCard"

    .line 32
    .line 33
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->w(Ljava/lang/String;Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 37
    .line 38
    invoke-static {v0}, Lcom/kwad/sdk/core/adlog/c;->cr(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->bq(Z)V

    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Z
    .locals 0

    .line 2
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amO:Z

    return p0
.end method

.method private bn(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    invoke-virtual {v0, p1, v1}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->h(ZI)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private bp(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amQ:Z

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/e/a;->pause()V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    const/4 p1, 0x1

    .line 14
    invoke-virtual {v0, p1}, Lcom/kwad/components/ct/detail/e/a;->bB(Z)V

    .line 15
    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method private bq(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 2
    .line 3
    const/16 v1, 0x32

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-static {v0, v1, v2}, Lcom/kwad/sdk/utils/ca;->a(Landroid/view/View;IZ)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amP:Z

    .line 14
    .line 15
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aL()V

    .line 16
    .line 17
    .line 18
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 19
    .line 20
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amY:Ljava/lang/Runnable;

    .line 21
    .line 22
    invoke-virtual {p1, v0}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 23
    .line 24
    .line 25
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 26
    .line 27
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amN:Landroid/view/ViewGroup;

    .line 28
    .line 29
    iget v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amX:I

    .line 30
    .line 31
    invoke-static {p1, v0, v1}, Lcom/kwad/components/core/t/r;->a(Landroid/view/View;Landroid/view/View;I)Landroid/animation/ValueAnimator;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eB:Landroid/animation/ValueAnimator;

    .line 36
    .line 37
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$5;

    .line 38
    .line 39
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b$5;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p1, v0}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 43
    .line 44
    .line 45
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eB:Landroid/animation/ValueAnimator;

    .line 46
    .line 47
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->start()V

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)Z
    .locals 0

    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amP:Z

    return p1
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->ya()V

    return-void
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)V
    .locals 0

    const/4 p1, 0x1

    .line 2
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->bp(Z)V

    return-void
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/sdk/core/webview/KSApiWebView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    return-object p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/ad/presenter/a/b;Z)V
    .locals 0

    const/4 p1, 0x0

    .line 2
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->bn(Z)V

    return-void
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aF()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic h(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aL()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic i(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Landroid/widget/FrameLayout;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 2
    .line 3
    return-object p0
.end method

.method private initView()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundColor(I)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 14
    .line 15
    invoke-virtual {v0}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {v0, v1}, Landroid/graphics/drawable/Drawable;->setAlpha(I)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static synthetic j(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Landroid/view/ViewGroup;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amN:Landroid/view/ViewGroup;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic k(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amX:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic l(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Ljava/lang/Runnable;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amY:Ljava/lang/Runnable;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic m(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->xZ()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic n(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->xI()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic o(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic p(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic q(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic r(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic s(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic t(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic u(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->yd()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic v(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->yc()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic w(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic x(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->time:J

    .line 2
    .line 3
    return-wide v0
.end method

.method private xI()V
    .locals 4

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    const-string v1, "initWebCard mWebCardContainerWidth:"

    .line 4
    .line 5
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amX:I

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const-string v1, "ActionBarWebCard"

    .line 18
    .line 19
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 23
    .line 24
    iget v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amX:I

    .line 25
    .line 26
    neg-int v1, v1

    .line 27
    int-to-float v1, v1

    .line 28
    invoke-virtual {v0, v1}, Landroid/view/View;->setTranslationX(F)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 32
    .line 33
    const/4 v1, 0x0

    .line 34
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 35
    .line 36
    .line 37
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aE()V

    .line 38
    .line 39
    .line 40
    const/4 v0, -0x1

    .line 41
    iput v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eu:I

    .line 42
    .line 43
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 44
    .line 45
    .line 46
    move-result-wide v2

    .line 47
    iput-wide v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->time:J

    .line 48
    .line 49
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 50
    .line 51
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 55
    .line 56
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 57
    .line 58
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/b;->cR(Lcom/kwad/sdk/core/response/model/AdTemplate;)Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    invoke-virtual {v0, v1}, Landroid/webkit/WebView;->loadUrl(Ljava/lang/String;)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method private xZ()V
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const/high16 v1, 0x41400000    # 12.0f

    .line 8
    .line 9
    invoke-static {v0, v1}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 14
    .line 15
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/high16 v2, 0x42ba0000    # 93.0f

    .line 20
    .line 21
    invoke-static {v1, v2}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 26
    .line 27
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    const/high16 v3, 0x43c80000    # 400.0f

    .line 32
    .line 33
    invoke-static {v2, v3}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    iget-object v3, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 38
    .line 39
    invoke-virtual {v3}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    const/high16 v4, 0x41700000    # 15.0f

    .line 44
    .line 45
    invoke-static {v3, v4}, Lcom/kwad/sdk/c/a/a;->a(Landroid/content/Context;F)I

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    iget-object v4, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 50
    .line 51
    invoke-virtual {v4}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 52
    .line 53
    .line 54
    move-result-object v4

    .line 55
    check-cast v4, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 56
    .line 57
    iput v0, v4, Landroid/view/ViewGroup$MarginLayoutParams;->leftMargin:I

    .line 58
    .line 59
    iput v1, v4, Landroid/view/ViewGroup$MarginLayoutParams;->rightMargin:I

    .line 60
    .line 61
    iput v2, v4, Landroid/view/ViewGroup$MarginLayoutParams;->height:I

    .line 62
    .line 63
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 64
    .line 65
    invoke-virtual {v1, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 66
    .line 67
    .line 68
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 69
    .line 70
    const/4 v2, 0x0

    .line 71
    invoke-virtual {v1, v2, v2, v2, v3}, Landroid/view/View;->setPadding(IIII)V

    .line 72
    .line 73
    .line 74
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 75
    .line 76
    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    .line 77
    .line 78
    .line 79
    move-result v1

    .line 80
    add-int/2addr v1, v0

    .line 81
    iput v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amX:I

    .line 82
    .line 83
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amW:Landroid/widget/RelativeLayout;

    .line 84
    .line 85
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    check-cast v0, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 90
    .line 91
    const/4 v1, -0x2

    .line 92
    iput v1, v0, Landroid/view/ViewGroup$MarginLayoutParams;->height:I

    .line 93
    .line 94
    const/4 v1, -0x1

    .line 95
    iput v1, v0, Landroid/view/ViewGroup$MarginLayoutParams;->width:I

    .line 96
    .line 97
    invoke-virtual {v0, v2, v2, v2, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 98
    .line 99
    .line 100
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amW:Landroid/widget/RelativeLayout;

    .line 101
    .line 102
    invoke-virtual {v1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 103
    .line 104
    .line 105
    return-void
.end method

.method public static synthetic y(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/core/webview/jshandler/ba;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->ev:Lcom/kwad/components/core/webview/jshandler/ba;

    .line 2
    .line 3
    return-object p0
.end method

.method private ya()V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amO:Z

    .line 2
    .line 3
    if-nez v0, :cond_2

    .line 4
    .line 5
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amP:Z

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x1

    .line 11
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amO:Z

    .line 12
    .line 13
    const-string v1, "ActionBarWebCard"

    .line 14
    .line 15
    const-string v2, "showWebActionBar"

    .line 16
    .line 17
    invoke-static {v1, v2}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    iget v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eu:I

    .line 21
    .line 22
    if-ne v1, v0, :cond_1

    .line 23
    .line 24
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aH()V

    .line 25
    .line 26
    .line 27
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->yb()V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aM()V

    .line 32
    .line 33
    .line 34
    :cond_2
    :goto_0
    return-void
.end method

.method private yb()V
    .locals 6

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/d/b;->a(Lcom/kwad/components/ct/detail/c;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/b;->cT(Lcom/kwad/sdk/core/response/model/AdTemplate;)J

    .line 12
    .line 13
    .line 14
    move-result-wide v0

    .line 15
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 16
    .line 17
    iget-object v3, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amY:Ljava/lang/Runnable;

    .line 18
    .line 19
    const-wide/16 v4, 0x0

    .line 20
    .line 21
    cmp-long v4, v0, v4

    .line 22
    .line 23
    if-lez v4, :cond_0

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    const-wide/16 v0, 0x1388

    .line 27
    .line 28
    :goto_0
    invoke-virtual {v2, v3, v0, v1}, Landroid/view/View;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 29
    .line 30
    .line 31
    :cond_1
    return-void
.end method

.method private yc()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 2
    .line 3
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amY:Ljava/lang/Runnable;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 9
    .line 10
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->H(Landroid/view/View;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/View;->setPadding(IIII)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amW:Landroid/widget/RelativeLayout;

    .line 20
    .line 21
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->H(Landroid/view/View;)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 25
    .line 26
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->H(Landroid/view/View;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method private yd()V
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->bq(Z)V

    .line 3
    .line 4
    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 13
    .line 14
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 15
    .line 16
    .line 17
    const/4 v1, 0x0

    .line 18
    invoke-direct {p0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->bp(Z)V

    .line 19
    .line 20
    .line 21
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->bn(Z)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public static synthetic z(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 7
    .line 8
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 9
    .line 10
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 13
    .line 14
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/b;->cU(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-eqz v0, :cond_3

    .line 19
    .line 20
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 21
    .line 22
    if-nez v0, :cond_0

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 26
    .line 27
    const/4 v1, 0x0

    .line 28
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 32
    .line 33
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 34
    .line 35
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 36
    .line 37
    if-eqz v0, :cond_1

    .line 38
    .line 39
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amV:Lcom/kwad/components/ct/detail/e/a$a;

    .line 40
    .line 41
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->a(Lcom/kwad/components/ct/detail/e/a$a;)V

    .line 42
    .line 43
    .line 44
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 45
    .line 46
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 47
    .line 48
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 49
    .line 50
    new-instance v0, Lcom/kwad/sdk/core/webview/b;

    .line 51
    .line 52
    invoke-direct {v0}, Lcom/kwad/sdk/core/webview/b;-><init>()V

    .line 53
    .line 54
    .line 55
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->et:Lcom/kwad/sdk/core/webview/b;

    .line 56
    .line 57
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->initView()V

    .line 58
    .line 59
    .line 60
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aC()V

    .line 61
    .line 62
    .line 63
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 64
    .line 65
    if-eqz v0, :cond_2

    .line 66
    .line 67
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 68
    .line 69
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 70
    .line 71
    .line 72
    :cond_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 73
    .line 74
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 75
    .line 76
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amo:Lcom/kwad/components/core/j/a;

    .line 77
    .line 78
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 79
    .line 80
    .line 81
    return-void

    .line 82
    :cond_3
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 83
    .line 84
    const/16 v1, 0x8

    .line 85
    .line 86
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 87
    .line 88
    .line 89
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_root_container:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 13
    .line 14
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_content_container:I

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    check-cast v0, Landroid/view/ViewGroup;

    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amN:Landroid/view/ViewGroup;

    .line 23
    .line 24
    sget v0, Lcom/kwad/sdk/R$id;->ksad_web_card_container:I

    .line 25
    .line 26
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, Landroid/widget/FrameLayout;

    .line 31
    .line 32
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->eF:Landroid/widget/FrameLayout;

    .line 33
    .line 34
    sget v0, Lcom/kwad/sdk/R$id;->ksad_actionbar_web_card:I

    .line 35
    .line 36
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    check-cast v0, Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 41
    .line 42
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 43
    .line 44
    sget v0, Lcom/kwad/sdk/R$id;->ksad_video_bottom_container:I

    .line 45
    .line 46
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    check-cast v0, Landroid/widget/RelativeLayout;

    .line 51
    .line 52
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amW:Landroid/widget/RelativeLayout;

    .line 53
    .line 54
    return-void
.end method

.method public final onDestroy()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onDestroy()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/kwad/sdk/core/webview/KSApiWebView;->release()V

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/sdk/core/response/b/b;->cU(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-eqz v0, :cond_3

    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 13
    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 22
    .line 23
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 24
    .line 25
    .line 26
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 27
    .line 28
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 29
    .line 30
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amo:Lcom/kwad/components/core/j/a;

    .line 31
    .line 32
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 36
    .line 37
    invoke-static {v0}, Lcom/kwad/sdk/c/a/a;->Y(Landroid/view/View;)V

    .line 38
    .line 39
    .line 40
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amY:Ljava/lang/Runnable;

    .line 41
    .line 42
    if-eqz v0, :cond_2

    .line 43
    .line 44
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->amr:Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 45
    .line 46
    invoke-virtual {v1, v0}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 47
    .line 48
    .line 49
    :cond_2
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->aL()V

    .line 50
    .line 51
    .line 52
    :cond_3
    :goto_0
    return-void
.end method
