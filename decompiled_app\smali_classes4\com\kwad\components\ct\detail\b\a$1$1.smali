.class final Lcom/kwad/components/ct/detail/b/a$1$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/glide/request/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/b/a$1;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/kwad/sdk/glide/request/h<",
        "Landroid/graphics/drawable/Drawable;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic avd:Ljava/lang/String;

.field final synthetic ave:Lcom/kwad/components/ct/detail/b/a$1;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/a$1;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/a$1$1;->ave:Lcom/kwad/components/ct/detail/b/a$1;

    .line 2
    .line 3
    iput-object p2, p0, Lcom/kwad/components/ct/detail/b/a$1$1;->avd:Ljava/lang/String;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Lcom/kwad/sdk/glide/load/engine/GlideException;)Z
    .locals 2
    .param p1    # Lcom/kwad/sdk/glide/load/engine/GlideException;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/kwad/sdk/glide/load/engine/GlideException;",
            ")Z"
        }
    .end annotation

    .line 2
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object p1

    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$1$1;->ave:Lcom/kwad/components/ct/detail/b/a$1;

    iget-object v0, v0, Lcom/kwad/components/ct/detail/b/a$1;->avc:Lcom/kwad/components/ct/detail/b/a;

    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->f(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;

    move-result-object v0

    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a$1$1;->avd:Ljava/lang/String;

    invoke-virtual {p1, v0, v1}, Lcom/kwad/components/ct/e/b;->a(Lcom/kwad/components/ct/response/model/CtAdTemplate;Ljava/lang/String;)V

    const/4 p1, 0x0

    return p1
.end method

.method public final bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;Lcom/kwad/sdk/glide/request/a/j;Lcom/kwad/sdk/glide/load/DataSource;Z)Z
    .locals 0

    .line 1
    const/4 p1, 0x0

    return p1
.end method
