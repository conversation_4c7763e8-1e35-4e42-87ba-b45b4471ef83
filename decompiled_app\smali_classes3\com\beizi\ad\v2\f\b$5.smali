.class Lcom/beizi/ad/v2/f/b$5;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/f/b;->y()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/f/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/f/b$5;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$5;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-virtual {p1}, Lcom/beizi/ad/v2/f/b;->c()V

    .line 4
    .line 5
    .line 6
    return-void
.end method
