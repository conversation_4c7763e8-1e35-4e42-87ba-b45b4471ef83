.class public final synthetic Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/home/<USER>

.field public final synthetic b:I

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    iput p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iput p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    return-void
.end method


# virtual methods
.method public final invoke(Lja<PERSON>/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget v2, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    check-cast p1, Landroidx/compose/runtime/Composer;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    invoke-static {v0, v1, v2, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p1

    return-object p1
.end method
