.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ltop/cycdm/cycapp/BaseVM;
.source "SourceFile"


# annotations
.annotation build Landroidx/compose/runtime/internal/StabilityInferred;
    parameters = 0x0
.end annotation

.annotation build Ldagger/hilt/android/lifecycle/HiltViewModel;
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ltop/cycdm/cycapp/BaseVM<",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0008\u0007\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001BA\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0016J\u000f\u0010\u0018\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0016J\u000f\u0010\u0019\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0016J\u000f\u0010\u001a\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0016J\u000f\u0010\u001b\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0016J\u000f\u0010\u001c\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0016J\u000f\u0010\u001d\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u0016J\u000f\u0010\u001e\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ \u0010\"\u001a\u00020!*\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030 H\u0096@\u00a2\u0006\u0004\u0008\"\u0010#J\r\u0010$\u001a\u00020\u0014\u00a2\u0006\u0004\u0008$\u0010\u0016J\r\u0010\"\u001a\u00020!\u00a2\u0006\u0004\u0008\"\u0010%J\u0015\u0010(\u001a\u00020\u00142\u0006\u0010\'\u001a\u00020&\u00a2\u0006\u0004\u0008(\u0010)J%\u0010/\u001a\u00020\u00142\u0006\u0010+\u001a\u00020*2\u0006\u0010-\u001a\u00020,2\u0006\u0010.\u001a\u00020*\u00a2\u0006\u0004\u0008/\u00100J\r\u00101\u001a\u00020\u0014\u00a2\u0006\u0004\u00081\u0010\u0016J\r\u00102\u001a\u00020\u0014\u00a2\u0006\u0004\u00082\u0010\u0016R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0007\u00103R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u00104R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u00105R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u00106R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000f\u00107R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u00108R,\u0010=\u001a\u001a\u0012\u0004\u0012\u00020*\u0012\u0010\u0012\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020<0;0:098\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R \u0010@\u001a\u000e\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020?098\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010>\u00a8\u0006A"
    }
    d2 = {
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Ltop/cycdm/cycapp/BaseVM;",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Landroidx/lifecycle/SavedStateHandle;",
        "savedStateHandle",
        "Lg8/b;",
        "appRep",
        "Lg8/d;",
        "indexRep",
        "Lg8/f;",
        "rankRep",
        "Lg8/i;",
        "videoRep",
        "Ltop/cycdm/cycapp/UserData;",
        "userData",
        "Lr7/k;",
        "videoPagingSourceFactory",
        "<init>",
        "(Landroidx/lifecycle/SavedStateHandle;Lg8/b;Lg8/d;Lg8/f;Lg8/i;Ltop/cycdm/cycapp/UserData;Lr7/k;)V",
        "Lkotlinx/coroutines/w1;",
        "initNav",
        "()Lkotlinx/coroutines/w1;",
        "initBanner",
        "initAd",
        "initVideo",
        "initVersion",
        "initNotice",
        "initWeekly",
        "initRank",
        "createInitialState",
        "()Ltop/cycdm/cycapp/ui/home/<USER>",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/t;",
        "initData",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "init",
        "()V",
        "Ltop/cycdm/model/NavInfoInner;",
        "navInfo",
        "loadNavInfoVideoList",
        "(Ltop/cycdm/model/NavInfoInner;)Lkotlinx/coroutines/w1;",
        "",
        "typeId",
        "Ltop/cycdm/model/NavType;",
        "type",
        "i",
        "updateQuery",
        "(ILtop/cycdm/model/NavType;I)Lkotlinx/coroutines/w1;",
        "shownAD",
        "shownNotice",
        "Lg8/b;",
        "Lg8/d;",
        "Lg8/f;",
        "Lg8/i;",
        "Ltop/cycdm/cycapp/UserData;",
        "Lr7/k;",
        "",
        "Lkotlinx/coroutines/flow/d;",
        "Landroidx/paging/PagingData;",
        "Ltop/cycdm/model/c0;",
        "_videoFlow",
        "Ljava/util/Map;",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "_query",
        "app_adRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final $stable:I = 0x8


# instance fields
.field private final _query:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ltop/cycdm/cycapp/ui/home/<USER>",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final _videoFlow:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lkotlinx/coroutines/flow/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final appRep:Lg8/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final indexRep:Lg8/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final rankRep:Lg8/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private userData:Ltop/cycdm/cycapp/UserData;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final videoPagingSourceFactory:Lr7/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final videoRep:Lg8/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroidx/lifecycle/SavedStateHandle;Lg8/b;Lg8/d;Lg8/f;Lg8/i;Ltop/cycdm/cycapp/UserData;Lr7/k;)V
    .locals 0
    .param p1    # Landroidx/lifecycle/SavedStateHandle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lg8/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lg8/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lg8/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lg8/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Ltop/cycdm/cycapp/UserData;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lr7/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Ljavax/inject/Inject;
    .end annotation

    .line 1
    invoke-direct {p0, p1}, Ltop/cycdm/cycapp/BaseVM;-><init>(Landroidx/lifecycle/SavedStateHandle;)V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/b;

    .line 5
    .line 6
    iput-object p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>/d;

    .line 7
    .line 8
    iput-object p4, p0, Ltop/cycdm/cycapp/ui/home/<USER>/f;

    .line 9
    .line 10
    iput-object p5, p0, Ltop/cycdm/cycapp/ui/home/<USER>/i;

    .line 11
    .line 12
    iput-object p6, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/UserData;

    .line 13
    .line 14
    iput-object p7, p0, Ltop/cycdm/cycapp/ui/home/<USER>/k;

    .line 15
    .line 16
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 17
    .line 18
    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 19
    .line 20
    .line 21
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 22
    .line 23
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 24
    .line 25
    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 26
    .line 27
    .line 28
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 29
    .line 30
    return-void
.end method

.method public static final synthetic access$getAppRep$p(Ltop/cycdm/cycapp/ui/home/<USER>/b;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getIndexRep$p(Ltop/cycdm/cycapp/ui/home/<USER>/d;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getRankRep$p(Ltop/cycdm/cycapp/ui/home/<USER>/f;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getUserData$p(Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/UserData;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/UserData;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getVideoPagingSourceFactory$p(Ltop/cycdm/cycapp/ui/home/<USER>/k;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/k;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$getVideoRep$p(Ltop/cycdm/cycapp/ui/home/<USER>/i;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$get_query$p(Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$get_videoFlow$p(Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic access$initBanner(Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;
    .locals 0

    .line 1
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic access$initNav(Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;
    .locals 0

    .line 1
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic access$initRank(Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;
    .locals 0

    .line 1
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic access$initVideo(Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;
    .locals 0

    .line 1
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic access$initWeekly(Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;
    .locals 0

    .line 1
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final initAd()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method private final initBanner()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method private final initNav()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method private final initNotice()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method private final initRank()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method private final initVersion()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method private final initVideo()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method private final initWeekly()Lkotlinx/coroutines/w1;
    .locals 4

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method


# virtual methods
.method public bridge synthetic createInitialState()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    move-result-object v0

    return-object v0
.end method

.method public createInitialState()Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 19
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 2
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    const v17, 0xffff

    const/16 v18, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    const/4 v15, 0x0

    const/16 v16, 0x0

    invoke-direct/range {v0 .. v18}, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/Map;Ljava/util/Map;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;Ltop/cycdm/model/z;Ltop/cycdm/model/s;Ltop/cycdm/model/p;Ltop/cycdm/cycapp/ui/weekly/DayOfWeek;Ljava/util/List;ILkotlin/jvm/internal/n;)V

    return-object v0
.end method

.method public final init()Lkotlinx/coroutines/w1;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public initData(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lorg/orbitmvi/orbit/syntax/simple/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 2
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    return-object p1
.end method

.method public final initData()V
    .locals 0

    .line 3
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 4
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    .line 5
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/w1;

    return-void
.end method

.method public final loadNavInfoVideoList(Ltop/cycdm/model/NavInfoInner;)Lkotlinx/coroutines/w1;
    .locals 3
    .param p1    # Ltop/cycdm/model/NavInfoInner;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p1, p0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 p1, 0x1

    .line 8
    const/4 v2, 0x0

    .line 9
    invoke-static {p0, v2, v0, p1, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method

.method public final shownAD()Lkotlinx/coroutines/w1;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public final shownNotice()Lkotlinx/coroutines/w1;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-static {p0, v3, v0, v2, v1}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public final updateQuery(ILtop/cycdm/model/NavType;I)Lkotlinx/coroutines/w1;
    .locals 6
    .param p2    # Ltop/cycdm/model/NavType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v5, 0x0

    .line 4
    move-object v2, p0

    .line 5
    move v1, p1

    .line 6
    move-object v3, p2

    .line 7
    move v4, p3

    .line 8
    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;ILkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    const/4 p1, 0x1

    .line 12
    const/4 p2, 0x0

    .line 13
    const/4 p3, 0x0

    .line 14
    invoke-static {p0, p3, v0, p1, p2}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->c(Lorg/orbitmvi/orbit/b;ZLkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    return-object p1
.end method
