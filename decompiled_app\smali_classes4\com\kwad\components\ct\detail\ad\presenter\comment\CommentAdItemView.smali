.class public Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;
.super Landroid/widget/RelativeLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;
    }
.end annotation


# instance fields
.field private anb:Landroid/widget/TextView;

.field private anc:Landroid/widget/ImageView;

.field private and:Landroid/widget/ImageView;

.field private ane:Landroid/widget/RelativeLayout;

.field private anf:Landroid/widget/TextView;

.field private ang:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/kwad/components/ct/detail/photo/comment/f;",
            ">;"
        }
    .end annotation
.end field

.field private anh:Lcom/kwad/components/ct/detail/photo/comment/h;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    .line 2
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->ang:Ljava/util/List;

    .line 3
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->initView()V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0

    .line 4
    invoke-direct {p0, p1, p2}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 5
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->ang:Ljava/util/List;

    .line 6
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->initView()V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0

    .line 7
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 8
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->ang:Ljava/util/List;

    .line 9
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->initView()V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Landroid/widget/TextView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anf:Landroid/widget/TextView;

    return-object p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;II)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->r(II)V

    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Lcom/kwad/components/ct/detail/photo/comment/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 2
    .line 3
    return-object p0
.end method

.method private initView()V
    .locals 3

    .line 1
    const-string v0, "CommentAdItemView"

    .line 2
    .line 3
    const-string v1, "initView"

    .line 4
    .line 5
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    sget v1, Lcom/kwad/sdk/R$layout;->ksad_photo_comment_ad_item:I

    .line 17
    .line 18
    const/4 v2, 0x1

    .line 19
    invoke-virtual {v0, v1, p0, v2}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    .line 20
    .line 21
    .line 22
    invoke-static {}, Lcom/kwad/components/ct/f/d;->Jt()Lcom/kwad/components/ct/f/d;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    const-class v1, Lcom/kwad/components/ct/detail/photo/comment/i;

    .line 27
    .line 28
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/f/d;->a(Ljava/lang/Class;)Lcom/kwad/components/ct/f/a;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    check-cast v0, Lcom/kwad/components/ct/detail/photo/comment/i;

    .line 33
    .line 34
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/photo/comment/i;->zn()Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 39
    .line 40
    sget v0, Lcom/kwad/sdk/R$id;->ksad_photo_comment_item_avatar:I

    .line 41
    .line 42
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    check-cast v0, Landroid/widget/ImageView;

    .line 47
    .line 48
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anc:Landroid/widget/ImageView;

    .line 49
    .line 50
    sget v0, Lcom/kwad/sdk/R$id;->ksad_photo_comment_item_flag_img:I

    .line 51
    .line 52
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    check-cast v0, Landroid/widget/ImageView;

    .line 57
    .line 58
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->and:Landroid/widget/ImageView;

    .line 59
    .line 60
    sget v0, Lcom/kwad/sdk/R$id;->ksad_photo_comment_item_name:I

    .line 61
    .line 62
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    check-cast v0, Landroid/widget/TextView;

    .line 67
    .line 68
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anb:Landroid/widget/TextView;

    .line 69
    .line 70
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anh:Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 71
    .line 72
    iget-object v1, v1, Lcom/kwad/components/ct/detail/photo/comment/h;->apK:Ljava/lang/String;

    .line 73
    .line 74
    invoke-static {v0, v1}, Lcom/kwad/components/ct/f/g;->a(Landroid/widget/TextView;Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    sget v0, Lcom/kwad/sdk/R$id;->ksad_photo_comment_item_content_frame:I

    .line 78
    .line 79
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    check-cast v0, Landroid/widget/RelativeLayout;

    .line 84
    .line 85
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->ane:Landroid/widget/RelativeLayout;

    .line 86
    .line 87
    sget v1, Lcom/kwad/sdk/R$id;->ksad_photo_comment_item_comment:I

    .line 88
    .line 89
    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    check-cast v0, Landroid/widget/TextView;

    .line 94
    .line 95
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anf:Landroid/widget/TextView;

    .line 96
    .line 97
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anc:Landroid/widget/ImageView;

    .line 98
    .line 99
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$1;

    .line 100
    .line 101
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)V

    .line 102
    .line 103
    .line 104
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 105
    .line 106
    .line 107
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anb:Landroid/widget/TextView;

    .line 108
    .line 109
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$2;

    .line 110
    .line 111
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)V

    .line 112
    .line 113
    .line 114
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 115
    .line 116
    .line 117
    return-void
.end method

.method private r(II)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->ang:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lcom/kwad/components/ct/detail/photo/comment/f;

    .line 18
    .line 19
    invoke-interface {v1, p1, p2}, Lcom/kwad/components/ct/detail/photo/comment/f;->r(II)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method private s(Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anf:Landroid/widget/TextView;

    .line 2
    .line 3
    new-instance v1, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;

    .line 4
    .line 5
    invoke-direct {v1, p0, p1, p2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final a(Lcom/kwad/components/ct/response/model/CtAdTemplate;Ljava/util/List;)V
    .locals 0
    .param p2    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/kwad/components/ct/response/model/CtAdTemplate;",
            "Ljava/util/List<",
            "Lcom/kwad/components/ct/detail/photo/comment/f;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    return-void

    :cond_0
    if-eqz p2, :cond_1

    .line 3
    iput-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->ang:Ljava/util/List;

    .line 4
    :cond_1
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/e;->bf(Lcom/kwad/sdk/core/response/model/AdTemplate;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->setAuthorIcon(Ljava/lang/String;)V

    .line 5
    invoke-static {p1}, Lcom/kwad/components/ct/response/a/a;->aL(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->setName(Ljava/lang/String;)V

    .line 6
    invoke-static {p1}, Lcom/kwad/components/ct/response/a/a;->aQ(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Ljava/lang/String;

    move-result-object p2

    .line 7
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    move-result-object p1

    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/a;->aI(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    move-result-object p1

    .line 8
    invoke-direct {p0, p2, p1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->s(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->and:Landroid/widget/ImageView;

    const/4 p2, 0x0

    invoke-virtual {p1, p2}, Landroid/widget/ImageView;->setVisibility(I)V

    return-void
.end method

.method public setAuthorIcon(Ljava/lang/String;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Lcom/kwad/sdk/glide/c;->bU(Landroid/content/Context;)Lcom/kwad/sdk/glide/g;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0, p1}, Lcom/kwad/sdk/glide/g;->hh(Ljava/lang/String;)Lcom/kwad/sdk/glide/f;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    sget v1, Lcom/kwad/sdk/R$drawable;->ksad_photo_default_author_icon:I

    .line 22
    .line 23
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {p1, v0}, Lcom/kwad/sdk/glide/request/b;->d(Landroid/graphics/drawable/Drawable;)Lcom/kwad/sdk/glide/request/b;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    check-cast p1, Lcom/kwad/sdk/glide/f;

    .line 32
    .line 33
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    sget v1, Lcom/kwad/sdk/R$drawable;->ksad_photo_default_author_icon:I

    .line 42
    .line 43
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {p1, v0}, Lcom/kwad/sdk/glide/request/b;->f(Landroid/graphics/drawable/Drawable;)Lcom/kwad/sdk/glide/request/b;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    check-cast p1, Lcom/kwad/sdk/glide/f;

    .line 52
    .line 53
    new-instance v0, Lcom/kwad/components/ct/widget/b/a;

    .line 54
    .line 55
    invoke-direct {v0}, Lcom/kwad/components/ct/widget/b/a;-><init>()V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p1, v0}, Lcom/kwad/sdk/glide/request/b;->a(Lcom/kwad/sdk/glide/load/i;)Lcom/kwad/sdk/glide/request/b;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    check-cast p1, Lcom/kwad/sdk/glide/f;

    .line 63
    .line 64
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anc:Landroid/widget/ImageView;

    .line 65
    .line 66
    invoke-virtual {p1, v0}, Lcom/kwad/sdk/glide/f;->b(Landroid/widget/ImageView;)Lcom/kwad/sdk/glide/request/a/k;

    .line 67
    .line 68
    .line 69
    return-void
.end method

.method public setName(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->anb:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
