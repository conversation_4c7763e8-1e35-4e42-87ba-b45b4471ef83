.class public final Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/email/EmailVM;

.field public final synthetic b:Lorg/orbitmvi/orbit/syntax/simple/b;


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->b:Lorg/orbitmvi/orbit/syntax/simple/b;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Ljava/lang/String;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->f(Ljava/lang/String;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Ljava/lang/String;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 2

    .line 1
    invoke-virtual {p1}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ltop/cycdm/cycapp/ui/email/x;

    .line 6
    .line 7
    new-instance v0, Ltop/cycdm/cycapp/utils/h$c;

    .line 8
    .line 9
    invoke-direct {v0, p0}, Ltop/cycdm/cycapp/utils/h$c;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    const/4 p0, 0x0

    .line 13
    const/4 v1, 0x2

    .line 14
    invoke-static {p1, v0, p0, v1, p0}, Ltop/cycdm/cycapp/ui/email/x;->b(Ltop/cycdm/cycapp/ui/email/x;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/email/x;

    .line 15
    .line 16
    .line 17
    move-result-object p0

    .line 18
    return-object p0
.end method


# virtual methods
.method public final c(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7

    .line 1
    instance-of v0, p2, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;

    .line 7
    .line 8
    iget v1, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;-><init>(Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x3

    .line 34
    const/4 v4, 0x2

    .line 35
    const/4 v5, 0x1

    .line 36
    const/4 v6, 0x0

    .line 37
    if-eqz v2, :cond_4

    .line 38
    .line 39
    if-eq v2, v5, :cond_3

    .line 40
    .line 41
    if-eq v2, v4, :cond_2

    .line 42
    .line 43
    if-ne v2, v3, :cond_1

    .line 44
    .line 45
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    goto :goto_4

    .line 49
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_2
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    goto :goto_2

    .line 61
    :cond_3
    iget-object p1, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->L$0:Ljava/lang/Object;

    .line 62
    .line 63
    check-cast p1, Ljava/lang/String;

    .line 64
    .line 65
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_4
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    iget-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 73
    .line 74
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->b:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 75
    .line 76
    iput-object p1, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->L$0:Ljava/lang/Object;

    .line 77
    .line 78
    iput v5, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->label:I

    .line 79
    .line 80
    const-string v5, "\u53d1\u9001\u6210\u529f"

    .line 81
    .line 82
    invoke-static {p2, v2, v5, v0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    move-result-object p2

    .line 86
    if-ne p2, v1, :cond_5

    .line 87
    .line 88
    goto :goto_3

    .line 89
    :cond_5
    :goto_1
    iget-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->b:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 90
    .line 91
    new-instance v2, Ltop/cycdm/cycapp/ui/email/b0;

    .line 92
    .line 93
    invoke-direct {v2, p1}, Ltop/cycdm/cycapp/ui/email/b0;-><init>(Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    iput-object v6, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->L$0:Ljava/lang/Object;

    .line 97
    .line 98
    iput v4, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->label:I

    .line 99
    .line 100
    invoke-static {p2, v2, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    if-ne p1, v1, :cond_6

    .line 105
    .line 106
    goto :goto_3

    .line 107
    :cond_6
    :goto_2
    new-instance p1, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$2;

    .line 108
    .line 109
    iget-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->b:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 110
    .line 111
    invoke-direct {p1, p2, v6}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$2;-><init>(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    .line 112
    .line 113
    .line 114
    new-instance p2, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$3;

    .line 115
    .line 116
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->b:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 117
    .line 118
    invoke-direct {p2, v2, v6}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$3;-><init>(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    .line 119
    .line 120
    .line 121
    new-instance v2, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$4;

    .line 122
    .line 123
    iget-object v4, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->b:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 124
    .line 125
    invoke-direct {v2, v4, v6}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$4;-><init>(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    .line 126
    .line 127
    .line 128
    iput v3, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3$emit$1;->label:I

    .line 129
    .line 130
    const/16 v3, 0x3c

    .line 131
    .line 132
    invoke-static {v3, p1, p2, v2, v0}, Ltop/cycdm/cycapp/utils/LifecycleExtensionsKt;->a(ILkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    if-ne p1, v1, :cond_7

    .line 137
    .line 138
    :goto_3
    return-object v1

    .line 139
    :cond_7
    :goto_4
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 140
    .line 141
    return-object p1
.end method

.method public bridge synthetic emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    .line 2
    .line 3
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->c(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
