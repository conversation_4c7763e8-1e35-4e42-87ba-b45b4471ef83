.class public interface abstract Lcom/sigmob/sdk/downloader/core/breakpoint/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/sigmob/sdk/downloader/core/breakpoint/g;


# virtual methods
.method public abstract a(ILcom/sigmob/sdk/downloader/core/cause/a;Ljava/lang/Exception;)V
.end method

.method public abstract a(Lcom/sigmob/sdk/downloader/core/breakpoint/c;IJ)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract d(I)V
.end method

.method public abstract e(I)Lcom/sigmob/sdk/downloader/core/breakpoint/c;
.end method

.method public abstract f(I)Z
.end method

.method public abstract g(I)Z
.end method
