.class public final synthetic Ltop/cycdm/cycapp/ui/email/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Ltop/cycdm/cycapp/ui/email/EmailVM;

.field public final synthetic b:Landroidx/compose/runtime/MutableState;


# direct methods
.method public synthetic constructor <init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Landroidx/compose/runtime/MutableState;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/r;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/r;->b:Landroidx/compose/runtime/MutableState;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/r;->a:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/r;->b:Landroidx/compose/runtime/MutableState;

    invoke-static {v0, v1}, Ltop/cycdm/cycapp/ui/email/EmailScreenKt;->h(Ltop/cycdm/cycapp/ui/email/EmailVM;Landroidx/compose/runtime/MutableState;)Lkotlin/t;

    move-result-object v0

    return-object v0
.end method
