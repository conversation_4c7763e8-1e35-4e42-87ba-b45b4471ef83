.class public final Ltop/cycdm/cycapp/ui/history/a$b;
.super Ltop/cycdm/cycapp/ui/history/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltop/cycdm/cycapp/ui/history/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Ltop/cycdm/model/j;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Ltop/cycdm/model/j;)V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, v0}, Ltop/cycdm/cycapp/ui/history/a;-><init>(Lkotlin/jvm/internal/n;)V

    .line 3
    .line 4
    .line 5
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/a$b;->a:Ltop/cycdm/model/j;

    .line 6
    .line 7
    return-void
.end method


# virtual methods
.method public final a()Ltop/cycdm/model/j;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/a$b;->a:Ltop/cycdm/model/j;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Ltop/cycdm/cycapp/ui/history/a$b;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Ltop/cycdm/cycapp/ui/history/a$b;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/a$b;->a:Ltop/cycdm/model/j;

    iget-object p1, p1, Ltop/cycdm/cycapp/ui/history/a$b;->a:Ltop/cycdm/model/j;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    return v2

    :cond_2
    return v0
.end method

.method public hashCode()I
    .locals 1

    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/a$b;->a:Ltop/cycdm/model/j;

    invoke-virtual {v0}, Ltop/cycdm/model/j;->hashCode()I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "DeleteHistory(history="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/a$b;->a:Ltop/cycdm/model/j;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
