.class public abstract Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ltop/cycdm/cycapp/ui/home/<USER>
    }
.end annotation


# direct methods
.method public static final A(Landroidx/compose/runtime/MutableIntState;Landroidx/compose/ui/unit/IntSize;)Lkotlin/t;
    .locals 4

    .line 1
    invoke-virtual {p1}, Landroidx/compose/ui/unit/IntSize;->unbox-impl()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const-wide v2, 0xffffffffL

    .line 6
    .line 7
    .line 8
    .line 9
    .line 10
    and-long/2addr v0, v2

    .line 11
    long-to-int p1, v0

    .line 12
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;I)V

    .line 13
    .line 14
    .line 15
    sget-object p0, <PERSON><PERSON><PERSON>/t;->a:<PERSON><PERSON><PERSON>/t;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final B(Landroidx/compose/runtime/MutableFloatState;Landroidx/compose/ui/unit/Density;)Landroidx/compose/ui/unit/IntOffset;
    .locals 4

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/MutableFloatState;->getFloatValue()F

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    invoke-static {p0}, Lu5/c;->d(F)I

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    const/4 p1, 0x0

    .line 10
    int-to-long v0, p1

    .line 11
    const/16 p1, 0x20

    .line 12
    .line 13
    shl-long/2addr v0, p1

    .line 14
    int-to-long p0, p0

    .line 15
    const-wide v2, 0xffffffffL

    .line 16
    .line 17
    .line 18
    .line 19
    .line 20
    and-long/2addr p0, v2

    .line 21
    or-long/2addr p0, v0

    .line 22
    invoke-static {p0, p1}, Landroidx/compose/ui/unit/IntOffset;->constructor-impl(J)J

    .line 23
    .line 24
    .line 25
    move-result-wide p0

    .line 26
    invoke-static {p0, p1}, Landroidx/compose/ui/unit/IntOffset;->box-impl(J)Landroidx/compose/ui/unit/IntOffset;

    .line 27
    .line 28
    .line 29
    move-result-object p0

    .line 30
    return-object p0
.end method

.method public static final C(Landroidx/compose/runtime/MutableIntState;Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/ui/layout/Measurable;Landroidx/compose/ui/unit/Constraints;)Landroidx/compose/ui/layout/MeasureResult;
    .locals 9

    .line 1
    invoke-virtual {p3}, Landroidx/compose/ui/unit/Constraints;->unbox-impl()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-interface {p2, v0, v1}, Landroidx/compose/ui/layout/Measurable;->measure-BRTryo0(J)Landroidx/compose/ui/layout/Placeable;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-virtual {p3}, Landroidx/compose/ui/unit/Constraints;->unbox-impl()J

    .line 10
    .line 11
    .line 12
    move-result-wide v0

    .line 13
    invoke-static {v0, v1}, Landroidx/compose/ui/unit/Constraints;->getMaxWidth-impl(J)I

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    invoke-virtual {p3}, Landroidx/compose/ui/unit/Constraints;->unbox-impl()J

    .line 18
    .line 19
    .line 20
    move-result-wide v0

    .line 21
    invoke-static {v0, v1}, Landroidx/compose/ui/unit/Constraints;->getMaxHeight-impl(J)I

    .line 22
    .line 23
    .line 24
    move-result v4

    .line 25
    new-instance v6, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 26
    .line 27
    invoke-direct {v6, p2, p1, p0}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/ui/layout/Placeable;Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/runtime/MutableIntState;)V

    .line 28
    .line 29
    .line 30
    const/4 v7, 0x4

    .line 31
    const/4 v8, 0x0

    .line 32
    const/4 v5, 0x0

    .line 33
    move-object v2, p1

    .line 34
    invoke-static/range {v2 .. v8}, Landroidx/compose/ui/layout/MeasureScope;->layout$default(Landroidx/compose/ui/layout/MeasureScope;IILjava/util/Map;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroidx/compose/ui/layout/MeasureResult;

    .line 35
    .line 36
    .line 37
    move-result-object p0

    .line 38
    return-object p0
.end method

.method public static final D(Landroidx/compose/ui/layout/Placeable;Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/runtime/MutableIntState;Landroidx/compose/ui/layout/Placeable$PlacementScope;)Lkotlin/t;
    .locals 7

    .line 1
    invoke-static {p2}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;)I

    .line 2
    .line 3
    .line 4
    move-result p2

    .line 5
    const/16 v0, 0x10

    .line 6
    .line 7
    int-to-float v0, v0

    .line 8
    invoke-static {v0}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-interface {p1, v0}, Landroidx/compose/ui/unit/Density;->roundToPx-0680j_4(F)I

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    add-int v3, p2, p1

    .line 17
    .line 18
    const/4 v5, 0x4

    .line 19
    const/4 v6, 0x0

    .line 20
    const/4 v2, 0x0

    .line 21
    const/4 v4, 0x0

    .line 22
    move-object v1, p0

    .line 23
    move-object v0, p3

    .line 24
    invoke-static/range {v0 .. v6}, Landroidx/compose/ui/layout/Placeable$PlacementScope;->place$default(Landroidx/compose/ui/layout/Placeable$PlacementScope;Landroidx/compose/ui/layout/Placeable;IIFILjava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 28
    .line 29
    return-object p0
.end method

.method public static final E(Landroidx/compose/runtime/MutableFloatState;Landroidx/compose/ui/unit/Density;)Landroidx/compose/ui/unit/IntOffset;
    .locals 4

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/MutableFloatState;->getFloatValue()F

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    invoke-static {p0}, Lu5/c;->d(F)I

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    const/4 p1, 0x0

    .line 10
    int-to-long v0, p1

    .line 11
    const/16 p1, 0x20

    .line 12
    .line 13
    shl-long/2addr v0, p1

    .line 14
    int-to-long p0, p0

    .line 15
    const-wide v2, 0xffffffffL

    .line 16
    .line 17
    .line 18
    .line 19
    .line 20
    and-long/2addr p0, v2

    .line 21
    or-long/2addr p0, v0

    .line 22
    invoke-static {p0, p1}, Landroidx/compose/ui/unit/IntOffset;->constructor-impl(J)J

    .line 23
    .line 24
    .line 25
    move-result-wide p0

    .line 26
    invoke-static {p0, p1}, Landroidx/compose/ui/unit/IntOffset;->box-impl(J)Landroidx/compose/ui/unit/IntOffset;

    .line 27
    .line 28
    .line 29
    move-result-object p0

    .line 30
    return-object p0
.end method

.method public static final F(Ltop/cycdm/model/c0;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/model/c0;->f()I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static final G(Ltop/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final H(Ltop/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final I()Landroidx/compose/runtime/MutableIntState;
    .locals 1

    .line 1
    const/16 v0, 0xc8

    .line 2
    .line 3
    invoke-static {v0}, Landroidx/compose/runtime/SnapshotIntStateKt;->mutableIntStateOf(I)Landroidx/compose/runtime/MutableIntState;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public static final J(Landroidx/compose/runtime/MutableIntState;)I
    .locals 0

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/IntState;->getIntValue()I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final K(Landroidx/compose/runtime/MutableIntState;I)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Landroidx/compose/runtime/MutableIntState;->setIntValue(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L(Ljava/lang/String;ZLkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/String;ZLkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic M(Landroidx/compose/runtime/MutableIntState;)I
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static synthetic a(Ltop/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Ltop/cycdm/model/m;ILtop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/LazyListScope;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;ILtop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/LazyListScope;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroidx/compose/runtime/MutableIntState;Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/ui/layout/Measurable;Landroidx/compose/ui/unit/Constraints;)Landroidx/compose/ui/layout/MeasureResult;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/ui/layout/Measurable;Landroidx/compose/ui/unit/Constraints;)Landroidx/compose/ui/layout/MeasureResult;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Ltop/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Ltop/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Landroidx/compose/runtime/MutableFloatState;Landroidx/compose/ui/unit/Density;)Landroidx/compose/ui/unit/IntOffset;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;Landroidx/compose/ui/unit/Density;)Landroidx/compose/ui/unit/IntOffset;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Landroidx/compose/runtime/MutableIntState;Landroidx/compose/ui/unit/IntSize;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;Landroidx/compose/ui/unit/IntSize;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h()Landroidx/compose/runtime/MutableIntState;
    .locals 1

    .line 1
    invoke-static {}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic i(Landroidx/compose/runtime/MutableFloatState;Landroidx/compose/ui/unit/Density;)Landroidx/compose/ui/unit/IntOffset;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;Landroidx/compose/ui/unit/Density;)Landroidx/compose/ui/unit/IntOffset;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic j(Landroidx/compose/ui/layout/Placeable;Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/runtime/MutableIntState;Landroidx/compose/ui/layout/Placeable$PlacementScope;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/ui/layout/Placeable;Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/runtime/MutableIntState;Landroidx/compose/ui/layout/Placeable$PlacementScope;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic k(Ljava/lang/String;ZLkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/String;ZLkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic l(ILtop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m()Landroidx/compose/runtime/MutableFloatState;
    .locals 1

    .line 1
    invoke-static {}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic n(Ltop/cycdm/model/c0;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/c0;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic o(Landroidx/compose/foundation/lazy/grid/LazyGridState;)Z
    .locals 0

    .line 1
    invoke-static {p0}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/grid/LazyGridState;)Z

    move-result p0

    return p0
.end method

.method public static final p(Ljava/lang/String;ZLkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V
    .locals 30

    .line 1
    move/from16 v4, p4

    .line 2
    .line 3
    const v0, -0x534d494b

    .line 4
    .line 5
    .line 6
    move-object/from16 v1, p3

    .line 7
    .line 8
    invoke-interface {v1, v0}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    and-int/lit8 v2, p5, 0x1

    .line 13
    .line 14
    if-eqz v2, :cond_0

    .line 15
    .line 16
    or-int/lit8 v2, v4, 0x6

    .line 17
    .line 18
    move-object/from16 v5, p0

    .line 19
    .line 20
    goto :goto_1

    .line 21
    :cond_0
    and-int/lit8 v2, v4, 0x6

    .line 22
    .line 23
    move-object/from16 v5, p0

    .line 24
    .line 25
    if-nez v2, :cond_2

    .line 26
    .line 27
    invoke-interface {v1, v5}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    if-eqz v2, :cond_1

    .line 32
    .line 33
    const/4 v2, 0x4

    .line 34
    goto :goto_0

    .line 35
    :cond_1
    const/4 v2, 0x2

    .line 36
    :goto_0
    or-int/2addr v2, v4

    .line 37
    goto :goto_1

    .line 38
    :cond_2
    move v2, v4

    .line 39
    :goto_1
    and-int/lit8 v3, p5, 0x2

    .line 40
    .line 41
    const/16 v6, 0x10

    .line 42
    .line 43
    if-eqz v3, :cond_4

    .line 44
    .line 45
    or-int/lit8 v2, v2, 0x30

    .line 46
    .line 47
    :cond_3
    move/from16 v7, p1

    .line 48
    .line 49
    goto :goto_3

    .line 50
    :cond_4
    and-int/lit8 v7, v4, 0x30

    .line 51
    .line 52
    if-nez v7, :cond_3

    .line 53
    .line 54
    move/from16 v7, p1

    .line 55
    .line 56
    invoke-interface {v1, v7}, Landroidx/compose/runtime/Composer;->changed(Z)Z

    .line 57
    .line 58
    .line 59
    move-result v8

    .line 60
    if-eqz v8, :cond_5

    .line 61
    .line 62
    const/16 v8, 0x20

    .line 63
    .line 64
    goto :goto_2

    .line 65
    :cond_5
    move v8, v6

    .line 66
    :goto_2
    or-int/2addr v2, v8

    .line 67
    :goto_3
    and-int/lit8 v8, p5, 0x4

    .line 68
    .line 69
    if-eqz v8, :cond_6

    .line 70
    .line 71
    or-int/lit16 v2, v2, 0x180

    .line 72
    .line 73
    move-object/from16 v15, p2

    .line 74
    .line 75
    goto :goto_5

    .line 76
    :cond_6
    and-int/lit16 v8, v4, 0x180

    .line 77
    .line 78
    move-object/from16 v15, p2

    .line 79
    .line 80
    if-nez v8, :cond_8

    .line 81
    .line 82
    invoke-interface {v1, v15}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    move-result v8

    .line 86
    if-eqz v8, :cond_7

    .line 87
    .line 88
    const/16 v8, 0x100

    .line 89
    .line 90
    goto :goto_4

    .line 91
    :cond_7
    const/16 v8, 0x80

    .line 92
    .line 93
    :goto_4
    or-int/2addr v2, v8

    .line 94
    :cond_8
    :goto_5
    and-int/lit16 v8, v2, 0x93

    .line 95
    .line 96
    const/16 v9, 0x92

    .line 97
    .line 98
    if-ne v8, v9, :cond_a

    .line 99
    .line 100
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 101
    .line 102
    .line 103
    move-result v8

    .line 104
    if-nez v8, :cond_9

    .line 105
    .line 106
    goto :goto_6

    .line 107
    :cond_9
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 108
    .line 109
    .line 110
    move-object/from16 v26, v1

    .line 111
    .line 112
    move v2, v7

    .line 113
    goto/16 :goto_a

    .line 114
    .line 115
    :cond_a
    :goto_6
    const/4 v8, 0x0

    .line 116
    if-eqz v3, :cond_b

    .line 117
    .line 118
    move v3, v8

    .line 119
    goto :goto_7

    .line 120
    :cond_b
    move v3, v7

    .line 121
    :goto_7
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 122
    .line 123
    .line 124
    move-result v7

    .line 125
    if-eqz v7, :cond_c

    .line 126
    .line 127
    const/4 v7, -0x1

    .line 128
    const-string v9, "top.cycdm.cycapp.ui.home.TagItem (VideoList.kt:197)"

    .line 129
    .line 130
    invoke-static {v0, v2, v7, v9}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 131
    .line 132
    .line 133
    :cond_c
    if-eqz v3, :cond_d

    .line 134
    .line 135
    const v0, 0x4adfef5f    # 7337903.5f

    .line 136
    .line 137
    .line 138
    invoke-interface {v1, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 139
    .line 140
    .line 141
    invoke-static {v1, v8}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    invoke-virtual {v0}, Lw7/a;->p()J

    .line 146
    .line 147
    .line 148
    move-result-wide v9

    .line 149
    :goto_8
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 150
    .line 151
    .line 152
    move-wide/from16 v18, v9

    .line 153
    .line 154
    goto :goto_9

    .line 155
    :cond_d
    const v0, 0x4adff39e    # 7338447.0f

    .line 156
    .line 157
    .line 158
    invoke-interface {v1, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 159
    .line 160
    .line 161
    invoke-static {v1, v8}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 162
    .line 163
    .line 164
    move-result-object v0

    .line 165
    invoke-virtual {v0}, Lw7/a;->o()J

    .line 166
    .line 167
    .line 168
    move-result-wide v9

    .line 169
    goto :goto_8

    .line 170
    :goto_9
    invoke-static {v6}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 171
    .line 172
    .line 173
    move-result-wide v6

    .line 174
    const v0, 0x4ae00499    # 7340620.5f

    .line 175
    .line 176
    .line 177
    invoke-interface {v1, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 178
    .line 179
    .line 180
    sget-object v0, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 181
    .line 182
    if-eqz v3, :cond_e

    .line 183
    .line 184
    invoke-static {v1, v8}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 185
    .line 186
    .line 187
    move-result-object v8

    .line 188
    invoke-virtual {v8}, Lw7/a;->b()J

    .line 189
    .line 190
    .line 191
    move-result-wide v8

    .line 192
    const/16 v10, 0x1e

    .line 193
    .line 194
    int-to-float v10, v10

    .line 195
    invoke-static {v10}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 196
    .line 197
    .line 198
    move-result v10

    .line 199
    invoke-static {v10}, Landroidx/compose/foundation/shape/RoundedCornerShapeKt;->RoundedCornerShape-0680j_4(F)Landroidx/compose/foundation/shape/RoundedCornerShape;

    .line 200
    .line 201
    .line 202
    move-result-object v10

    .line 203
    invoke-static {v0, v8, v9, v10}, Landroidx/compose/foundation/BackgroundKt;->background-bw27NRU(Landroidx/compose/ui/Modifier;JLandroidx/compose/ui/graphics/Shape;)Landroidx/compose/ui/Modifier;

    .line 204
    .line 205
    .line 206
    move-result-object v0

    .line 207
    :cond_e
    move-object v9, v0

    .line 208
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 209
    .line 210
    .line 211
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 212
    .line 213
    .line 214
    move-result-object v0

    .line 215
    sget-object v8, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 216
    .line 217
    invoke-virtual {v8}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 218
    .line 219
    .line 220
    move-result-object v8

    .line 221
    if-ne v0, v8, :cond_f

    .line 222
    .line 223
    invoke-static {}, Landroidx/compose/foundation/interaction/InteractionSourceKt;->MutableInteractionSource()Landroidx/compose/foundation/interaction/MutableInteractionSource;

    .line 224
    .line 225
    .line 226
    move-result-object v0

    .line 227
    invoke-interface {v1, v0}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 228
    .line 229
    .line 230
    :cond_f
    move-object v10, v0

    .line 231
    check-cast v10, Landroidx/compose/foundation/interaction/MutableInteractionSource;

    .line 232
    .line 233
    const/16 v16, 0x1c

    .line 234
    .line 235
    const/16 v17, 0x0

    .line 236
    .line 237
    const/4 v11, 0x0

    .line 238
    const/4 v12, 0x0

    .line 239
    const/4 v13, 0x0

    .line 240
    const/4 v14, 0x0

    .line 241
    invoke-static/range {v9 .. v17}, Landroidx/compose/foundation/ClickableKt;->clickable-O2vRcR0$default(Landroidx/compose/ui/Modifier;Landroidx/compose/foundation/interaction/MutableInteractionSource;Landroidx/compose/foundation/Indication;ZLjava/lang/String;Landroidx/compose/ui/semantics/Role;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 242
    .line 243
    .line 244
    move-result-object v0

    .line 245
    const/16 v8, 0xc

    .line 246
    .line 247
    int-to-float v8, v8

    .line 248
    invoke-static {v8}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 249
    .line 250
    .line 251
    move-result v8

    .line 252
    const/4 v9, 0x6

    .line 253
    int-to-float v9, v9

    .line 254
    invoke-static {v9}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 255
    .line 256
    .line 257
    move-result v9

    .line 258
    invoke-static {v0, v8, v9}, Landroidx/compose/foundation/layout/PaddingKt;->padding-VpY3zN4(Landroidx/compose/ui/Modifier;FF)Landroidx/compose/ui/Modifier;

    .line 259
    .line 260
    .line 261
    move-result-object v0

    .line 262
    and-int/lit8 v2, v2, 0xe

    .line 263
    .line 264
    or-int/lit16 v2, v2, 0xc00

    .line 265
    .line 266
    const/16 v28, 0x0

    .line 267
    .line 268
    const v29, 0x1fff0

    .line 269
    .line 270
    .line 271
    const/4 v12, 0x0

    .line 272
    const-wide/16 v14, 0x0

    .line 273
    .line 274
    const/16 v16, 0x0

    .line 275
    .line 276
    move-wide v9, v6

    .line 277
    move-wide/from16 v7, v18

    .line 278
    .line 279
    const-wide/16 v18, 0x0

    .line 280
    .line 281
    const/16 v20, 0x0

    .line 282
    .line 283
    const/16 v21, 0x0

    .line 284
    .line 285
    const/16 v22, 0x0

    .line 286
    .line 287
    const/16 v23, 0x0

    .line 288
    .line 289
    const/16 v24, 0x0

    .line 290
    .line 291
    const/16 v25, 0x0

    .line 292
    .line 293
    move-object v6, v0

    .line 294
    move-object/from16 v26, v1

    .line 295
    .line 296
    move/from16 v27, v2

    .line 297
    .line 298
    invoke-static/range {v5 .. v29}, Landroidx/compose/material3/TextKt;->Text--4IGK_g(Ljava/lang/String;Landroidx/compose/ui/Modifier;JJLandroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontFamily;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/text/style/TextAlign;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/TextStyle;Landroidx/compose/runtime/Composer;III)V

    .line 299
    .line 300
    .line 301
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 302
    .line 303
    .line 304
    move-result v0

    .line 305
    if-eqz v0, :cond_10

    .line 306
    .line 307
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 308
    .line 309
    .line 310
    :cond_10
    move v2, v3

    .line 311
    :goto_a
    invoke-interface/range {v26 .. v26}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 312
    .line 313
    .line 314
    move-result-object v6

    .line 315
    if-eqz v6, :cond_11

    .line 316
    .line 317
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 318
    .line 319
    move-object/from16 v1, p0

    .line 320
    .line 321
    move-object/from16 v3, p2

    .line 322
    .line 323
    move/from16 v5, p5

    .line 324
    .line 325
    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/String;ZLkotlin/jvm/functions/Function0;II)V

    .line 326
    .line 327
    .line 328
    invoke-interface {v6, v0}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 329
    .line 330
    .line 331
    :cond_11
    return-void
.end method

.method public static final q(Ljava/lang/String;ZLkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    invoke-static {p3}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result v4

    move-object v0, p0

    move v1, p1

    move-object v2, p2

    move v5, p4

    move-object v3, p5

    invoke-static/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/String;ZLkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final r(ILtop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;Landroidx/compose/runtime/Composer;I)V
    .locals 19

    .line 1
    move/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v2, p2

    .line 6
    .line 7
    move/from16 v3, p4

    .line 8
    .line 9
    const v4, -0x4b1318ba

    .line 10
    .line 11
    .line 12
    move-object/from16 v5, p3

    .line 13
    .line 14
    invoke-interface {v5, v4}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 15
    .line 16
    .line 17
    move-result-object v15

    .line 18
    and-int/lit8 v5, v3, 0x6

    .line 19
    .line 20
    const/4 v13, 0x2

    .line 21
    const/4 v14, 0x4

    .line 22
    if-nez v5, :cond_1

    .line 23
    .line 24
    invoke-interface {v15, v0}, Landroidx/compose/runtime/Composer;->changed(I)Z

    .line 25
    .line 26
    .line 27
    move-result v5

    .line 28
    if-eqz v5, :cond_0

    .line 29
    .line 30
    move v5, v14

    .line 31
    goto :goto_0

    .line 32
    :cond_0
    move v5, v13

    .line 33
    :goto_0
    or-int/2addr v5, v3

    .line 34
    goto :goto_1

    .line 35
    :cond_1
    move v5, v3

    .line 36
    :goto_1
    and-int/lit8 v6, v3, 0x30

    .line 37
    .line 38
    if-nez v6, :cond_3

    .line 39
    .line 40
    invoke-interface {v15, v1}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    move-result v6

    .line 44
    if-eqz v6, :cond_2

    .line 45
    .line 46
    const/16 v6, 0x20

    .line 47
    .line 48
    goto :goto_2

    .line 49
    :cond_2
    const/16 v6, 0x10

    .line 50
    .line 51
    :goto_2
    or-int/2addr v5, v6

    .line 52
    :cond_3
    and-int/lit16 v6, v3, 0x180

    .line 53
    .line 54
    if-nez v6, :cond_5

    .line 55
    .line 56
    invoke-interface {v15, v2}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    move-result v6

    .line 60
    if-eqz v6, :cond_4

    .line 61
    .line 62
    const/16 v6, 0x100

    .line 63
    .line 64
    goto :goto_3

    .line 65
    :cond_4
    const/16 v6, 0x80

    .line 66
    .line 67
    :goto_3
    or-int/2addr v5, v6

    .line 68
    :cond_5
    and-int/lit16 v6, v5, 0x93

    .line 69
    .line 70
    const/16 v7, 0x92

    .line 71
    .line 72
    if-ne v6, v7, :cond_7

    .line 73
    .line 74
    invoke-interface {v15}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 75
    .line 76
    .line 77
    move-result v6

    .line 78
    if-nez v6, :cond_6

    .line 79
    .line 80
    goto :goto_4

    .line 81
    :cond_6
    invoke-interface {v15}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 82
    .line 83
    .line 84
    goto/16 :goto_8

    .line 85
    .line 86
    :cond_7
    :goto_4
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 87
    .line 88
    .line 89
    move-result v6

    .line 90
    if-eqz v6, :cond_8

    .line 91
    .line 92
    const/4 v6, -0x1

    .line 93
    const-string v7, "top.cycdm.cycapp.ui.home.TagList (VideoList.kt:177)"

    .line 94
    .line 95
    invoke-static {v4, v5, v6, v7}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 96
    .line 97
    .line 98
    :cond_8
    invoke-virtual {v2}, Ltop/cycdm/model/m;->b()Ltop/cycdm/model/NavType;

    .line 99
    .line 100
    .line 101
    move-result-object v4

    .line 102
    const v6, 0x70b323c8

    .line 103
    .line 104
    .line 105
    invoke-interface {v15, v6}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 106
    .line 107
    .line 108
    sget-object v6, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 109
    .line 110
    sget v7, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 111
    .line 112
    invoke-virtual {v6, v15, v7}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 113
    .line 114
    .line 115
    move-result-object v6

    .line 116
    if-eqz v6, :cond_14

    .line 117
    .line 118
    const/4 v7, 0x0

    .line 119
    invoke-static {v6, v15, v7}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 120
    .line 121
    .line 122
    move-result-object v8

    .line 123
    const v9, 0x671a9c9b

    .line 124
    .line 125
    .line 126
    invoke-interface {v15, v9}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 127
    .line 128
    .line 129
    instance-of v9, v6, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 130
    .line 131
    if-eqz v9, :cond_9

    .line 132
    .line 133
    move-object v9, v6

    .line 134
    check-cast v9, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 135
    .line 136
    invoke-interface {v9}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 137
    .line 138
    .line 139
    move-result-object v9

    .line 140
    goto :goto_5

    .line 141
    :cond_9
    sget-object v9, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 142
    .line 143
    :goto_5
    const v11, 0x9048

    .line 144
    .line 145
    .line 146
    const/4 v12, 0x0

    .line 147
    move v10, v5

    .line 148
    const-class v5, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 149
    .line 150
    move/from16 v16, v7

    .line 151
    .line 152
    const/4 v7, 0x0

    .line 153
    move-object/from16 v18, v15

    .line 154
    .line 155
    move v15, v10

    .line 156
    move-object/from16 v10, v18

    .line 157
    .line 158
    invoke-static/range {v5 .. v12}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 159
    .line 160
    .line 161
    move-result-object v5

    .line 162
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 163
    .line 164
    .line 165
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 166
    .line 167
    .line 168
    check-cast v5, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 169
    .line 170
    sget-object v6, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 171
    .line 172
    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    .line 173
    .line 174
    .line 175
    move-result v4

    .line 176
    aget v4, v6, v4

    .line 177
    .line 178
    const/4 v7, 0x1

    .line 179
    if-eq v4, v7, :cond_e

    .line 180
    .line 181
    if-eq v4, v13, :cond_d

    .line 182
    .line 183
    const/4 v6, 0x3

    .line 184
    if-eq v4, v6, :cond_c

    .line 185
    .line 186
    if-eq v4, v14, :cond_b

    .line 187
    .line 188
    const/4 v6, 0x5

    .line 189
    if-ne v4, v6, :cond_a

    .line 190
    .line 191
    invoke-virtual {v1}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 192
    .line 193
    .line 194
    move-result v4

    .line 195
    goto :goto_6

    .line 196
    :cond_a
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 197
    .line 198
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 199
    .line 200
    .line 201
    throw v0

    .line 202
    :cond_b
    invoke-virtual {v1}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 203
    .line 204
    .line 205
    move-result v4

    .line 206
    goto :goto_6

    .line 207
    :cond_c
    invoke-virtual {v1}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 208
    .line 209
    .line 210
    move-result v4

    .line 211
    goto :goto_6

    .line 212
    :cond_d
    invoke-virtual {v1}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 213
    .line 214
    .line 215
    move-result v4

    .line 216
    goto :goto_6

    .line 217
    :cond_e
    invoke-virtual {v1}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 218
    .line 219
    .line 220
    move-result v4

    .line 221
    :goto_6
    invoke-interface {v10, v2}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 222
    .line 223
    .line 224
    move-result v6

    .line 225
    invoke-interface {v10, v4}, Landroidx/compose/runtime/Composer;->changed(I)Z

    .line 226
    .line 227
    .line 228
    move-result v8

    .line 229
    or-int/2addr v6, v8

    .line 230
    invoke-interface {v10, v5}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 231
    .line 232
    .line 233
    move-result v8

    .line 234
    or-int/2addr v6, v8

    .line 235
    and-int/lit8 v8, v15, 0xe

    .line 236
    .line 237
    if-ne v8, v14, :cond_f

    .line 238
    .line 239
    goto :goto_7

    .line 240
    :cond_f
    move/from16 v7, v16

    .line 241
    .line 242
    :goto_7
    or-int/2addr v6, v7

    .line 243
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 244
    .line 245
    .line 246
    move-result-object v7

    .line 247
    if-nez v6, :cond_10

    .line 248
    .line 249
    sget-object v6, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 250
    .line 251
    invoke-virtual {v6}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 252
    .line 253
    .line 254
    move-result-object v6

    .line 255
    if-ne v7, v6, :cond_11

    .line 256
    .line 257
    :cond_10
    new-instance v7, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 258
    .line 259
    invoke-direct {v7, v2, v4, v5, v0}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;ILtop/cycdm/cycapp/ui/home/<USER>

    .line 260
    .line 261
    .line 262
    invoke-interface {v10, v7}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 263
    .line 264
    .line 265
    :cond_11
    move-object v14, v7

    .line 266
    check-cast v14, Lkotlin/jvm/functions/Function1;

    .line 267
    .line 268
    const/16 v16, 0x0

    .line 269
    .line 270
    const/16 v17, 0x1ff

    .line 271
    .line 272
    const/4 v5, 0x0

    .line 273
    const/4 v6, 0x0

    .line 274
    const/4 v7, 0x0

    .line 275
    const/4 v8, 0x0

    .line 276
    const/4 v9, 0x0

    .line 277
    move-object v15, v10

    .line 278
    const/4 v10, 0x0

    .line 279
    const/4 v11, 0x0

    .line 280
    const/4 v12, 0x0

    .line 281
    const/4 v13, 0x0

    .line 282
    invoke-static/range {v5 .. v17}, Landroidx/compose/foundation/lazy/LazyDslKt;->LazyRow(Landroidx/compose/ui/Modifier;Landroidx/compose/foundation/lazy/LazyListState;Landroidx/compose/foundation/layout/PaddingValues;ZLandroidx/compose/foundation/layout/Arrangement$Horizontal;Landroidx/compose/ui/Alignment$Vertical;Landroidx/compose/foundation/gestures/FlingBehavior;ZLandroidx/compose/foundation/OverscrollEffect;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/Composer;II)V

    .line 283
    .line 284
    .line 285
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 286
    .line 287
    .line 288
    move-result v4

    .line 289
    if-eqz v4, :cond_12

    .line 290
    .line 291
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 292
    .line 293
    .line 294
    :cond_12
    :goto_8
    invoke-interface {v15}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 295
    .line 296
    .line 297
    move-result-object v4

    .line 298
    if-eqz v4, :cond_13

    .line 299
    .line 300
    new-instance v5, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 301
    .line 302
    invoke-direct {v5, v0, v1, v2, v3}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;I)V

    .line 303
    .line 304
    .line 305
    invoke-interface {v4, v5}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 306
    .line 307
    .line 308
    :cond_13
    return-void

    .line 309
    :cond_14
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 310
    .line 311
    const-string v1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 312
    .line 313
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 314
    .line 315
    .line 316
    throw v0
.end method

.method public static final s(Ltop/cycdm/model/m;ILtop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/LazyListScope;)Lkotlin/t;
    .locals 8

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/model/m;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 6
    .line 7
    .line 8
    move-result v6

    .line 9
    new-instance v7, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 10
    .line 11
    invoke-direct {v7, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;)V

    .line 12
    .line 13
    .line 14
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 15
    .line 16
    move-object v5, p0

    .line 17
    move v2, p1

    .line 18
    move-object v3, p2

    .line 19
    move v4, p3

    .line 20
    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;ILtop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;)V

    .line 21
    .line 22
    .line 23
    const p0, -0x410876af

    .line 24
    .line 25
    .line 26
    const/4 p1, 0x1

    .line 27
    invoke-static {p0, p1, v0}, Landroidx/compose/runtime/internal/ComposableLambdaKt;->composableLambdaInstance(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/ComposableLambda;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    const/4 p1, 0x0

    .line 32
    invoke-interface {p4, v6, p1, v7, p0}, Landroidx/compose/foundation/lazy/LazyListScope;->items(ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function4;)V

    .line 33
    .line 34
    .line 35
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 36
    .line 37
    return-object p0
.end method

.method public static final t(ILtop/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p3, p3, 0x1

    invoke-static {p3}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p3

    invoke-static {p0, p1, p2, p4, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final u(Ltop/cycdm/model/NavInfoInner;Landroidx/compose/runtime/Composer;I)V
    .locals 37

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move/from16 v1, p2

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    const v3, 0x1d0acf5

    .line 7
    .line 8
    .line 9
    move-object/from16 v4, p1

    .line 10
    .line 11
    invoke-interface {v4, v3}, Landroidx/compose/runtime/Composer;->startRestartGroup(I)Landroidx/compose/runtime/Composer;

    .line 12
    .line 13
    .line 14
    move-result-object v8

    .line 15
    const/4 v12, 0x6

    .line 16
    and-int/lit8 v4, v1, 0x6

    .line 17
    .line 18
    const/4 v13, 0x2

    .line 19
    if-nez v4, :cond_1

    .line 20
    .line 21
    invoke-interface {v8, v0}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    move-result v4

    .line 25
    if-eqz v4, :cond_0

    .line 26
    .line 27
    const/4 v4, 0x4

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    move v4, v13

    .line 30
    :goto_0
    or-int/2addr v4, v1

    .line 31
    goto :goto_1

    .line 32
    :cond_1
    move v4, v1

    .line 33
    :goto_1
    and-int/lit8 v5, v4, 0x3

    .line 34
    .line 35
    if-ne v5, v13, :cond_3

    .line 36
    .line 37
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 38
    .line 39
    .line 40
    move-result v5

    .line 41
    if-nez v5, :cond_2

    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_2
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 45
    .line 46
    .line 47
    goto/16 :goto_9

    .line 48
    .line 49
    :cond_3
    :goto_2
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 50
    .line 51
    .line 52
    move-result v5

    .line 53
    if-eqz v5, :cond_4

    .line 54
    .line 55
    const/4 v5, -0x1

    .line 56
    const-string v6, "top.cycdm.cycapp.ui.home.VideoList (VideoList.kt:65)"

    .line 57
    .line 58
    invoke-static {v3, v4, v5, v6}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 59
    .line 60
    .line 61
    :cond_4
    const v3, 0x70b323c8

    .line 62
    .line 63
    .line 64
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 65
    .line 66
    .line 67
    sget-object v3, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 68
    .line 69
    sget v4, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 70
    .line 71
    invoke-virtual {v3, v8, v4}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 72
    .line 73
    .line 74
    move-result-object v5

    .line 75
    if-eqz v5, :cond_2b

    .line 76
    .line 77
    const/4 v3, 0x0

    .line 78
    invoke-static {v5, v8, v3}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 79
    .line 80
    .line 81
    move-result-object v7

    .line 82
    const v4, 0x671a9c9b

    .line 83
    .line 84
    .line 85
    invoke-interface {v8, v4}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 86
    .line 87
    .line 88
    instance-of v4, v5, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 89
    .line 90
    if-eqz v4, :cond_5

    .line 91
    .line 92
    move-object v4, v5

    .line 93
    check-cast v4, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 94
    .line 95
    invoke-interface {v4}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 96
    .line 97
    .line 98
    move-result-object v4

    .line 99
    goto :goto_3

    .line 100
    :cond_5
    sget-object v4, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 101
    .line 102
    :goto_3
    const v10, 0x9048

    .line 103
    .line 104
    .line 105
    const/4 v11, 0x0

    .line 106
    move-object/from16 v23, v8

    .line 107
    .line 108
    move-object v8, v4

    .line 109
    const-class v4, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 110
    .line 111
    const/4 v6, 0x0

    .line 112
    move-object/from16 v9, v23

    .line 113
    .line 114
    invoke-static/range {v4 .. v11}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 115
    .line 116
    .line 117
    move-result-object v4

    .line 118
    move-object v8, v9

    .line 119
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 120
    .line 121
    .line 122
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 123
    .line 124
    .line 125
    check-cast v4, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 126
    .line 127
    const/4 v11, 0x0

    .line 128
    const/4 v14, 0x1

    .line 129
    invoke-static {v4, v11, v8, v3, v14}, Lorg/orbitmvi/orbit/compose/ContainerHostExtensionsKt;->c(Lorg/orbitmvi/orbit/b;Landroidx/lifecycle/Lifecycle$State;Landroidx/compose/runtime/Composer;II)Landroidx/compose/runtime/State;

    .line 130
    .line 131
    .line 132
    move-result-object v5

    .line 133
    invoke-virtual {v0}, Ltop/cycdm/model/NavInfoInner;->b()I

    .line 134
    .line 135
    .line 136
    move-result v15

    .line 137
    invoke-static {v5}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/State;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 138
    .line 139
    .line 140
    move-result-object v6

    .line 141
    invoke-virtual {v6}, Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 142
    .line 143
    .line 144
    move-result-object v6

    .line 145
    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 146
    .line 147
    .line 148
    move-result-object v7

    .line 149
    invoke-interface {v6, v7}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 150
    .line 151
    .line 152
    move-result-object v6

    .line 153
    check-cast v6, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 154
    .line 155
    if-nez v6, :cond_7

    .line 156
    .line 157
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 158
    .line 159
    .line 160
    move-result v2

    .line 161
    if-eqz v2, :cond_6

    .line 162
    .line 163
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 164
    .line 165
    .line 166
    :cond_6
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 167
    .line 168
    .line 169
    move-result-object v2

    .line 170
    if-eqz v2, :cond_2a

    .line 171
    .line 172
    new-instance v3, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 173
    .line 174
    invoke-direct {v3, v0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;I)V

    .line 175
    .line 176
    .line 177
    invoke-interface {v2, v3}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 178
    .line 179
    .line 180
    return-void

    .line 181
    :cond_7
    filled-new-array {v6}, [Ljava/lang/Object;

    .line 182
    .line 183
    .line 184
    move-result-object v7

    .line 185
    invoke-interface {v8, v4}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 186
    .line 187
    .line 188
    move-result v9

    .line 189
    invoke-interface {v8, v0}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 190
    .line 191
    .line 192
    move-result v10

    .line 193
    or-int/2addr v9, v10

    .line 194
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 195
    .line 196
    .line 197
    move-result-object v10

    .line 198
    if-nez v9, :cond_8

    .line 199
    .line 200
    sget-object v9, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 201
    .line 202
    invoke-virtual {v9}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 203
    .line 204
    .line 205
    move-result-object v9

    .line 206
    if-ne v10, v9, :cond_9

    .line 207
    .line 208
    :cond_8
    new-instance v10, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 209
    .line 210
    invoke-direct {v10, v4, v0, v11}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;Lkotlin/coroutines/e;)V

    .line 211
    .line 212
    .line 213
    invoke-interface {v8, v10}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 214
    .line 215
    .line 216
    :cond_9
    check-cast v10, Lkotlin/jvm/functions/Function2;

    .line 217
    .line 218
    invoke-static {v7, v10, v8, v3}, Ltop/cycdm/cycapp/ui/common/ExtensionKt;->y([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;Landroidx/compose/runtime/Composer;I)V

    .line 219
    .line 220
    .line 221
    invoke-static {v5}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/State;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 222
    .line 223
    .line 224
    move-result-object v4

    .line 225
    invoke-virtual {v4}, Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 226
    .line 227
    .line 228
    move-result-object v4

    .line 229
    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 230
    .line 231
    .line 232
    move-result-object v5

    .line 233
    invoke-interface {v4, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 234
    .line 235
    .line 236
    move-result-object v4

    .line 237
    check-cast v4, Lkotlinx/coroutines/flow/d;

    .line 238
    .line 239
    if-nez v4, :cond_b

    .line 240
    .line 241
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 242
    .line 243
    .line 244
    move-result v2

    .line 245
    if-eqz v2, :cond_a

    .line 246
    .line 247
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 248
    .line 249
    .line 250
    :cond_a
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 251
    .line 252
    .line 253
    move-result-object v2

    .line 254
    if-eqz v2, :cond_2a

    .line 255
    .line 256
    new-instance v3, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 257
    .line 258
    invoke-direct {v3, v0, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;I)V

    .line 259
    .line 260
    .line 261
    invoke-interface {v2, v3}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 262
    .line 263
    .line 264
    return-void

    .line 265
    :cond_b
    invoke-static {v4, v11, v8, v3, v14}, Landroidx/paging/compose/LazyPagingItemsKt;->collectAsLazyPagingItems(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/i;Landroidx/compose/runtime/Composer;II)Landroidx/paging/compose/LazyPagingItems;

    .line 266
    .line 267
    .line 268
    move-result-object v5

    .line 269
    invoke-static {}, Ltop/cycdm/cycapp/RouterKt;->i()Landroidx/compose/runtime/ProvidableCompositionLocal;

    .line 270
    .line 271
    .line 272
    move-result-object v4

    .line 273
    invoke-interface {v8, v4}, Landroidx/compose/runtime/Composer;->consume(Landroidx/compose/runtime/CompositionLocal;)Ljava/lang/Object;

    .line 274
    .line 275
    .line 276
    move-result-object v4

    .line 277
    check-cast v4, Landroidx/navigation/NavHostController;

    .line 278
    .line 279
    invoke-static {}, Landroidx/compose/ui/platform/CompositionLocalsKt;->getLocalDensity()Landroidx/compose/runtime/ProvidableCompositionLocal;

    .line 280
    .line 281
    .line 282
    move-result-object v7

    .line 283
    invoke-interface {v8, v7}, Landroidx/compose/runtime/Composer;->consume(Landroidx/compose/runtime/CompositionLocal;)Ljava/lang/Object;

    .line 284
    .line 285
    .line 286
    move-result-object v7

    .line 287
    check-cast v7, Landroidx/compose/ui/unit/Density;

    .line 288
    .line 289
    const/16 v9, 0x1a

    .line 290
    .line 291
    int-to-float v9, v9

    .line 292
    invoke-static {v9}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 293
    .line 294
    .line 295
    move-result v9

    .line 296
    invoke-interface {v7, v9}, Landroidx/compose/ui/unit/Density;->toPx-0680j_4(F)F

    .line 297
    .line 298
    .line 299
    move-result v7

    .line 300
    move-object v9, v4

    .line 301
    new-array v4, v3, [Ljava/lang/Object;

    .line 302
    .line 303
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 304
    .line 305
    .line 306
    move-result-object v10

    .line 307
    sget-object v16, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 308
    .line 309
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 310
    .line 311
    .line 312
    move-result-object v12

    .line 313
    if-ne v10, v12, :cond_c

    .line 314
    .line 315
    new-instance v10, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 316
    .line 317
    invoke-direct {v10}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 318
    .line 319
    .line 320
    invoke-interface {v8, v10}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 321
    .line 322
    .line 323
    :cond_c
    check-cast v10, Lkotlin/jvm/functions/Function0;

    .line 324
    .line 325
    move-object v12, v9

    .line 326
    const/16 v9, 0xc00

    .line 327
    .line 328
    move/from16 v17, v7

    .line 329
    .line 330
    move-object v7, v10

    .line 331
    const/4 v10, 0x6

    .line 332
    move-object/from16 v18, v5

    .line 333
    .line 334
    const/4 v5, 0x0

    .line 335
    move-object/from16 v19, v6

    .line 336
    .line 337
    const/4 v6, 0x0

    .line 338
    move-object/from16 v28, v12

    .line 339
    .line 340
    move/from16 v14, v17

    .line 341
    .line 342
    move-object/from16 v27, v18

    .line 343
    .line 344
    move-object/from16 v12, v19

    .line 345
    .line 346
    invoke-static/range {v4 .. v10}, Landroidx/compose/runtime/saveable/RememberSaveableKt;->rememberSaveable([Ljava/lang/Object;Landroidx/compose/runtime/saveable/Saver;Ljava/lang/String;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)Ljava/lang/Object;

    .line 347
    .line 348
    .line 349
    move-result-object v4

    .line 350
    check-cast v4, Landroidx/compose/runtime/MutableIntState;

    .line 351
    .line 352
    move-object v5, v4

    .line 353
    new-array v4, v3, [Ljava/lang/Object;

    .line 354
    .line 355
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 356
    .line 357
    .line 358
    move-result-object v6

    .line 359
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 360
    .line 361
    .line 362
    move-result-object v7

    .line 363
    if-ne v6, v7, :cond_d

    .line 364
    .line 365
    new-instance v6, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 366
    .line 367
    invoke-direct {v6}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 368
    .line 369
    .line 370
    invoke-interface {v8, v6}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 371
    .line 372
    .line 373
    :cond_d
    move-object v7, v6

    .line 374
    check-cast v7, Lkotlin/jvm/functions/Function0;

    .line 375
    .line 376
    const/16 v9, 0xc00

    .line 377
    .line 378
    const/4 v10, 0x6

    .line 379
    move-object v6, v5

    .line 380
    const/4 v5, 0x0

    .line 381
    move-object/from16 v18, v6

    .line 382
    .line 383
    const/4 v6, 0x0

    .line 384
    move-object/from16 v11, v18

    .line 385
    .line 386
    invoke-static/range {v4 .. v10}, Landroidx/compose/runtime/saveable/RememberSaveableKt;->rememberSaveable([Ljava/lang/Object;Landroidx/compose/runtime/saveable/Saver;Ljava/lang/String;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;II)Ljava/lang/Object;

    .line 387
    .line 388
    .line 389
    move-result-object v4

    .line 390
    check-cast v4, Landroidx/compose/runtime/MutableFloatState;

    .line 391
    .line 392
    invoke-static {v3, v3, v8, v3, v2}, Landroidx/compose/foundation/lazy/grid/LazyGridStateKt;->rememberLazyGridState(IILandroidx/compose/runtime/Composer;II)Landroidx/compose/foundation/lazy/grid/LazyGridState;

    .line 393
    .line 394
    .line 395
    move-result-object v5

    .line 396
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 397
    .line 398
    .line 399
    move-result-object v6

    .line 400
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 401
    .line 402
    .line 403
    move-result-object v7

    .line 404
    if-ne v6, v7, :cond_e

    .line 405
    .line 406
    new-instance v6, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 407
    .line 408
    invoke-direct {v6, v14, v4, v11}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;Landroidx/compose/runtime/MutableIntState;)V

    .line 409
    .line 410
    .line 411
    invoke-interface {v8, v6}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 412
    .line 413
    .line 414
    :cond_e
    check-cast v6, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 415
    .line 416
    invoke-interface {v8, v5}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 417
    .line 418
    .line 419
    move-result v7

    .line 420
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 421
    .line 422
    .line 423
    move-result-object v9

    .line 424
    if-nez v7, :cond_f

    .line 425
    .line 426
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 427
    .line 428
    .line 429
    move-result-object v7

    .line 430
    if-ne v9, v7, :cond_10

    .line 431
    .line 432
    :cond_f
    new-instance v9, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 433
    .line 434
    invoke-direct {v9, v5}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/grid/LazyGridState;)V

    .line 435
    .line 436
    .line 437
    invoke-interface {v8, v9}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 438
    .line 439
    .line 440
    :cond_10
    check-cast v9, Lkotlin/jvm/functions/Function0;

    .line 441
    .line 442
    invoke-static {v9, v8, v3}, Ltop/cycdm/cycapp/ui/common/ExtensionKt;->Y(Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/Composer;I)Landroidx/compose/runtime/State;

    .line 443
    .line 444
    .line 445
    move-result-object v29

    .line 446
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 447
    .line 448
    .line 449
    move-result-object v7

    .line 450
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 451
    .line 452
    .line 453
    move-result-object v9

    .line 454
    if-ne v7, v9, :cond_11

    .line 455
    .line 456
    sget-object v7, Lkotlin/coroutines/EmptyCoroutineContext;->INSTANCE:Lkotlin/coroutines/EmptyCoroutineContext;

    .line 457
    .line 458
    invoke-static {v7, v8}, Landroidx/compose/runtime/EffectsKt;->createCompositionCoroutineScope(Lkotlin/coroutines/i;Landroidx/compose/runtime/Composer;)Lkotlinx/coroutines/o0;

    .line 459
    .line 460
    .line 461
    move-result-object v7

    .line 462
    invoke-interface {v8, v7}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 463
    .line 464
    .line 465
    :cond_11
    check-cast v7, Lkotlinx/coroutines/o0;

    .line 466
    .line 467
    invoke-static {v8, v3}, Ltop/cycdm/cycapp/utils/f;->d(Landroidx/compose/runtime/Composer;I)Z

    .line 468
    .line 469
    .line 470
    move-result v9

    .line 471
    sget-object v10, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 472
    .line 473
    const/4 v14, 0x0

    .line 474
    invoke-static {v10, v6, v14, v13, v14}, Landroidx/compose/ui/input/nestedscroll/NestedScrollModifierKt;->nestedScroll$default(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/input/nestedscroll/NestedScrollConnection;Landroidx/compose/ui/input/nestedscroll/NestedScrollDispatcher;ILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 475
    .line 476
    .line 477
    move-result-object v6

    .line 478
    invoke-static {v3}, Landroidx/compose/foundation/shape/RoundedCornerShapeKt;->RoundedCornerShape(I)Landroidx/compose/foundation/shape/RoundedCornerShape;

    .line 479
    .line 480
    .line 481
    move-result-object v14

    .line 482
    invoke-static {v6, v14}, Landroidx/compose/ui/draw/ClipKt;->clip(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/graphics/Shape;)Landroidx/compose/ui/Modifier;

    .line 483
    .line 484
    .line 485
    move-result-object v6

    .line 486
    sget-object v14, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 487
    .line 488
    invoke-virtual {v14}, Landroidx/compose/ui/Alignment$Companion;->getTopStart()Landroidx/compose/ui/Alignment;

    .line 489
    .line 490
    .line 491
    move-result-object v13

    .line 492
    invoke-static {v13, v3}, Landroidx/compose/foundation/layout/BoxKt;->maybeCachedBoxMeasurePolicy(Landroidx/compose/ui/Alignment;Z)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 493
    .line 494
    .line 495
    move-result-object v13

    .line 496
    invoke-static {v8, v3}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 497
    .line 498
    .line 499
    move-result v20

    .line 500
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 501
    .line 502
    .line 503
    move-result-object v2

    .line 504
    invoke-static {v8, v6}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 505
    .line 506
    .line 507
    move-result-object v6

    .line 508
    sget-object v22, Landroidx/compose/ui/node/ComposeUiNode;->Companion:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 509
    .line 510
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 511
    .line 512
    .line 513
    move-result-object v3

    .line 514
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 515
    .line 516
    .line 517
    move-result-object v24

    .line 518
    if-nez v24, :cond_12

    .line 519
    .line 520
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 521
    .line 522
    .line 523
    :cond_12
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 524
    .line 525
    .line 526
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 527
    .line 528
    .line 529
    move-result v24

    .line 530
    if-eqz v24, :cond_13

    .line 531
    .line 532
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 533
    .line 534
    .line 535
    goto :goto_4

    .line 536
    :cond_13
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 537
    .line 538
    .line 539
    :goto_4
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 540
    .line 541
    .line 542
    move-result-object v3

    .line 543
    move-object/from16 v24, v5

    .line 544
    .line 545
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 546
    .line 547
    .line 548
    move-result-object v5

    .line 549
    invoke-static {v3, v13, v5}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 550
    .line 551
    .line 552
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 553
    .line 554
    .line 555
    move-result-object v5

    .line 556
    invoke-static {v3, v2, v5}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 557
    .line 558
    .line 559
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 560
    .line 561
    .line 562
    move-result-object v2

    .line 563
    invoke-interface {v3}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 564
    .line 565
    .line 566
    move-result v5

    .line 567
    if-nez v5, :cond_14

    .line 568
    .line 569
    invoke-interface {v3}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 570
    .line 571
    .line 572
    move-result-object v5

    .line 573
    invoke-static/range {v20 .. v20}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 574
    .line 575
    .line 576
    move-result-object v13

    .line 577
    invoke-static {v5, v13}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 578
    .line 579
    .line 580
    move-result v5

    .line 581
    if-nez v5, :cond_15

    .line 582
    .line 583
    :cond_14
    invoke-static/range {v20 .. v20}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 584
    .line 585
    .line 586
    move-result-object v5

    .line 587
    invoke-interface {v3, v5}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 588
    .line 589
    .line 590
    invoke-static/range {v20 .. v20}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 591
    .line 592
    .line 593
    move-result-object v5

    .line 594
    invoke-interface {v3, v5, v2}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 595
    .line 596
    .line 597
    :cond_15
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 598
    .line 599
    .line 600
    move-result-object v2

    .line 601
    invoke-static {v3, v6, v2}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 602
    .line 603
    .line 604
    sget-object v2, Landroidx/compose/foundation/layout/BoxScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 605
    .line 606
    const/4 v3, 0x0

    .line 607
    const/4 v5, 0x0

    .line 608
    const/4 v6, 0x1

    .line 609
    invoke-static {v10, v3, v6, v5}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxWidth$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 610
    .line 611
    .line 612
    move-result-object v10

    .line 613
    const/4 v6, 0x0

    .line 614
    const/4 v13, 0x3

    .line 615
    invoke-static {v10, v5, v6, v13, v5}, Landroidx/compose/foundation/layout/SizeKt;->wrapContentHeight$default(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/Alignment$Vertical;ZILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 616
    .line 617
    .line 618
    move-result-object v10

    .line 619
    const/16 v6, 0x10

    .line 620
    .line 621
    int-to-float v6, v6

    .line 622
    invoke-static {v6}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 623
    .line 624
    .line 625
    move-result v13

    .line 626
    move/from16 v20, v6

    .line 627
    .line 628
    const/4 v6, 0x2

    .line 629
    invoke-static {v10, v13, v3, v6, v5}, Landroidx/compose/foundation/layout/PaddingKt;->padding-VpY3zN4$default(Landroidx/compose/ui/Modifier;FFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 630
    .line 631
    .line 632
    move-result-object v30

    .line 633
    const/16 v5, 0xa

    .line 634
    .line 635
    int-to-float v5, v5

    .line 636
    invoke-static {v5}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 637
    .line 638
    .line 639
    move-result v32

    .line 640
    invoke-static/range {v20 .. v20}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 641
    .line 642
    .line 643
    move-result v34

    .line 644
    const/16 v35, 0x5

    .line 645
    .line 646
    const/16 v36, 0x0

    .line 647
    .line 648
    const/16 v31, 0x0

    .line 649
    .line 650
    const/16 v33, 0x0

    .line 651
    .line 652
    invoke-static/range {v30 .. v36}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 653
    .line 654
    .line 655
    move-result-object v5

    .line 656
    invoke-interface {v8, v11}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 657
    .line 658
    .line 659
    move-result v6

    .line 660
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 661
    .line 662
    .line 663
    move-result-object v10

    .line 664
    if-nez v6, :cond_16

    .line 665
    .line 666
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 667
    .line 668
    .line 669
    move-result-object v6

    .line 670
    if-ne v10, v6, :cond_17

    .line 671
    .line 672
    :cond_16
    new-instance v10, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 673
    .line 674
    invoke-direct {v10, v11}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;)V

    .line 675
    .line 676
    .line 677
    invoke-interface {v8, v10}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 678
    .line 679
    .line 680
    :cond_17
    check-cast v10, Lkotlin/jvm/functions/Function1;

    .line 681
    .line 682
    invoke-static {v5, v10}, Landroidx/compose/ui/layout/OnRemeasuredModifierKt;->onSizeChanged(Landroidx/compose/ui/Modifier;Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/Modifier;

    .line 683
    .line 684
    .line 685
    move-result-object v5

    .line 686
    invoke-interface {v8, v4}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 687
    .line 688
    .line 689
    move-result v6

    .line 690
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 691
    .line 692
    .line 693
    move-result-object v10

    .line 694
    if-nez v6, :cond_18

    .line 695
    .line 696
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 697
    .line 698
    .line 699
    move-result-object v6

    .line 700
    if-ne v10, v6, :cond_19

    .line 701
    .line 702
    :cond_18
    new-instance v10, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 703
    .line 704
    invoke-direct {v10, v4}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;)V

    .line 705
    .line 706
    .line 707
    invoke-interface {v8, v10}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 708
    .line 709
    .line 710
    :cond_19
    check-cast v10, Lkotlin/jvm/functions/Function1;

    .line 711
    .line 712
    invoke-static {v5, v10}, Landroidx/compose/foundation/layout/OffsetKt;->offset(Landroidx/compose/ui/Modifier;Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/Modifier;

    .line 713
    .line 714
    .line 715
    move-result-object v5

    .line 716
    sget-object v6, Landroidx/compose/foundation/layout/Arrangement;->INSTANCE:Landroidx/compose/foundation/layout/Arrangement;

    .line 717
    .line 718
    const/16 v10, 0x14

    .line 719
    .line 720
    int-to-float v10, v10

    .line 721
    invoke-static {v10}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 722
    .line 723
    .line 724
    move-result v10

    .line 725
    invoke-virtual {v6, v10}, Landroidx/compose/foundation/layout/Arrangement;->spacedBy-0680j_4(F)Landroidx/compose/foundation/layout/Arrangement$HorizontalOrVertical;

    .line 726
    .line 727
    .line 728
    move-result-object v6

    .line 729
    invoke-virtual {v14}, Landroidx/compose/ui/Alignment$Companion;->getStart()Landroidx/compose/ui/Alignment$Horizontal;

    .line 730
    .line 731
    .line 732
    move-result-object v10

    .line 733
    const/4 v13, 0x6

    .line 734
    invoke-static {v6, v10, v8, v13}, Landroidx/compose/foundation/layout/ColumnKt;->columnMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/ui/Alignment$Horizontal;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 735
    .line 736
    .line 737
    move-result-object v6

    .line 738
    const/4 v10, 0x0

    .line 739
    invoke-static {v8, v10}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 740
    .line 741
    .line 742
    move-result v14

    .line 743
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 744
    .line 745
    .line 746
    move-result-object v10

    .line 747
    invoke-static {v8, v5}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 748
    .line 749
    .line 750
    move-result-object v5

    .line 751
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 752
    .line 753
    .line 754
    move-result-object v13

    .line 755
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 756
    .line 757
    .line 758
    move-result-object v16

    .line 759
    if-nez v16, :cond_1a

    .line 760
    .line 761
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 762
    .line 763
    .line 764
    :cond_1a
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 765
    .line 766
    .line 767
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 768
    .line 769
    .line 770
    move-result v16

    .line 771
    if-eqz v16, :cond_1b

    .line 772
    .line 773
    invoke-interface {v8, v13}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 774
    .line 775
    .line 776
    goto :goto_5

    .line 777
    :cond_1b
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 778
    .line 779
    .line 780
    :goto_5
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 781
    .line 782
    .line 783
    move-result-object v13

    .line 784
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 785
    .line 786
    .line 787
    move-result-object v3

    .line 788
    invoke-static {v13, v6, v3}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 789
    .line 790
    .line 791
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 792
    .line 793
    .line 794
    move-result-object v3

    .line 795
    invoke-static {v13, v10, v3}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 796
    .line 797
    .line 798
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 799
    .line 800
    .line 801
    move-result-object v3

    .line 802
    invoke-interface {v13}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 803
    .line 804
    .line 805
    move-result v6

    .line 806
    if-nez v6, :cond_1c

    .line 807
    .line 808
    invoke-interface {v13}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 809
    .line 810
    .line 811
    move-result-object v6

    .line 812
    invoke-static {v14}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 813
    .line 814
    .line 815
    move-result-object v10

    .line 816
    invoke-static {v6, v10}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 817
    .line 818
    .line 819
    move-result v6

    .line 820
    if-nez v6, :cond_1d

    .line 821
    .line 822
    :cond_1c
    invoke-static {v14}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 823
    .line 824
    .line 825
    move-result-object v6

    .line 826
    invoke-interface {v13, v6}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 827
    .line 828
    .line 829
    invoke-static {v14}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 830
    .line 831
    .line 832
    move-result-object v6

    .line 833
    invoke-interface {v13, v6, v3}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 834
    .line 835
    .line 836
    :cond_1d
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 837
    .line 838
    .line 839
    move-result-object v3

    .line 840
    invoke-static {v13, v5, v3}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 841
    .line 842
    .line 843
    sget-object v3, Landroidx/compose/foundation/layout/ColumnScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/ColumnScopeInstance;

    .line 844
    .line 845
    const v3, 0x66cf377a

    .line 846
    .line 847
    .line 848
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 849
    .line 850
    .line 851
    invoke-virtual {v0}, Ltop/cycdm/model/NavInfoInner;->a()Ltop/cycdm/model/NavInfoInner$NavExtend;

    .line 852
    .line 853
    .line 854
    move-result-object v3

    .line 855
    invoke-virtual {v3}, Ltop/cycdm/model/NavInfoInner$NavExtend;->c()Ljava/util/List;

    .line 856
    .line 857
    .line 858
    move-result-object v3

    .line 859
    invoke-static {v3}, Lkotlin/collections/f0;->M0(Ljava/lang/Iterable;)Ljava/util/List;

    .line 860
    .line 861
    .line 862
    move-result-object v3

    .line 863
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 864
    .line 865
    .line 866
    move-result-object v3

    .line 867
    :goto_6
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 868
    .line 869
    .line 870
    move-result v5

    .line 871
    if-eqz v5, :cond_22

    .line 872
    .line 873
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 874
    .line 875
    .line 876
    move-result-object v5

    .line 877
    check-cast v5, Ltop/cycdm/model/m;

    .line 878
    .line 879
    sget-object v6, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 880
    .line 881
    sget-object v10, Landroidx/compose/foundation/layout/Arrangement;->INSTANCE:Landroidx/compose/foundation/layout/Arrangement;

    .line 882
    .line 883
    invoke-virtual {v10}, Landroidx/compose/foundation/layout/Arrangement;->getTop()Landroidx/compose/foundation/layout/Arrangement$Vertical;

    .line 884
    .line 885
    .line 886
    move-result-object v10

    .line 887
    sget-object v13, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 888
    .line 889
    invoke-virtual {v13}, Landroidx/compose/ui/Alignment$Companion;->getStart()Landroidx/compose/ui/Alignment$Horizontal;

    .line 890
    .line 891
    .line 892
    move-result-object v13

    .line 893
    const/4 v14, 0x0

    .line 894
    invoke-static {v10, v13, v8, v14}, Landroidx/compose/foundation/layout/ColumnKt;->columnMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/ui/Alignment$Horizontal;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 895
    .line 896
    .line 897
    move-result-object v10

    .line 898
    invoke-static {v8, v14}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 899
    .line 900
    .line 901
    move-result v13

    .line 902
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 903
    .line 904
    .line 905
    move-result-object v14

    .line 906
    invoke-static {v8, v6}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 907
    .line 908
    .line 909
    move-result-object v6

    .line 910
    sget-object v22, Landroidx/compose/ui/node/ComposeUiNode;->Companion:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 911
    .line 912
    move-object/from16 v25, v3

    .line 913
    .line 914
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 915
    .line 916
    .line 917
    move-result-object v3

    .line 918
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 919
    .line 920
    .line 921
    move-result-object v26

    .line 922
    if-nez v26, :cond_1e

    .line 923
    .line 924
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 925
    .line 926
    .line 927
    :cond_1e
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 928
    .line 929
    .line 930
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 931
    .line 932
    .line 933
    move-result v26

    .line 934
    if-eqz v26, :cond_1f

    .line 935
    .line 936
    invoke-interface {v8, v3}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 937
    .line 938
    .line 939
    goto :goto_7

    .line 940
    :cond_1f
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 941
    .line 942
    .line 943
    :goto_7
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 944
    .line 945
    .line 946
    move-result-object v3

    .line 947
    move-object/from16 v26, v7

    .line 948
    .line 949
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 950
    .line 951
    .line 952
    move-result-object v7

    .line 953
    invoke-static {v3, v10, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 954
    .line 955
    .line 956
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 957
    .line 958
    .line 959
    move-result-object v7

    .line 960
    invoke-static {v3, v14, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 961
    .line 962
    .line 963
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 964
    .line 965
    .line 966
    move-result-object v7

    .line 967
    invoke-interface {v3}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 968
    .line 969
    .line 970
    move-result v10

    .line 971
    if-nez v10, :cond_20

    .line 972
    .line 973
    invoke-interface {v3}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 974
    .line 975
    .line 976
    move-result-object v10

    .line 977
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 978
    .line 979
    .line 980
    move-result-object v14

    .line 981
    invoke-static {v10, v14}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 982
    .line 983
    .line 984
    move-result v10

    .line 985
    if-nez v10, :cond_21

    .line 986
    .line 987
    :cond_20
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 988
    .line 989
    .line 990
    move-result-object v10

    .line 991
    invoke-interface {v3, v10}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 992
    .line 993
    .line 994
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 995
    .line 996
    .line 997
    move-result-object v10

    .line 998
    invoke-interface {v3, v10, v7}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 999
    .line 1000
    .line 1001
    :cond_21
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 1002
    .line 1003
    .line 1004
    move-result-object v7

    .line 1005
    invoke-static {v3, v6, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 1006
    .line 1007
    .line 1008
    sget-object v3, Landroidx/compose/foundation/layout/ColumnScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/ColumnScopeInstance;

    .line 1009
    .line 1010
    const/4 v14, 0x0

    .line 1011
    invoke-static {v15, v12, v5, v8, v14}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/m;Landroidx/compose/runtime/Composer;I)V

    .line 1012
    .line 1013
    .line 1014
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 1015
    .line 1016
    .line 1017
    move-object/from16 v3, v25

    .line 1018
    .line 1019
    move-object/from16 v7, v26

    .line 1020
    .line 1021
    goto/16 :goto_6

    .line 1022
    .line 1023
    :cond_22
    move-object/from16 v26, v7

    .line 1024
    .line 1025
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 1026
    .line 1027
    .line 1028
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 1029
    .line 1030
    .line 1031
    new-instance v3, Landroidx/compose/foundation/lazy/grid/GridCells$Fixed;

    .line 1032
    .line 1033
    if-eqz v9, :cond_23

    .line 1034
    .line 1035
    const/4 v12, 0x6

    .line 1036
    goto :goto_8

    .line 1037
    :cond_23
    const/4 v12, 0x3

    .line 1038
    :goto_8
    invoke-direct {v3, v12}, Landroidx/compose/foundation/lazy/grid/GridCells$Fixed;-><init>(I)V

    .line 1039
    .line 1040
    .line 1041
    invoke-static/range {v20 .. v20}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 1042
    .line 1043
    .line 1044
    move-result v5

    .line 1045
    const/4 v6, 0x2

    .line 1046
    const/4 v7, 0x0

    .line 1047
    const/4 v14, 0x0

    .line 1048
    invoke-static {v5, v7, v6, v14}, Landroidx/compose/foundation/layout/PaddingKt;->PaddingValues-YgX7TsA$default(FFILjava/lang/Object;)Landroidx/compose/foundation/layout/PaddingValues;

    .line 1049
    .line 1050
    .line 1051
    move-result-object v9

    .line 1052
    sget-object v5, Landroidx/compose/foundation/layout/Arrangement;->INSTANCE:Landroidx/compose/foundation/layout/Arrangement;

    .line 1053
    .line 1054
    invoke-static/range {v20 .. v20}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 1055
    .line 1056
    .line 1057
    move-result v6

    .line 1058
    invoke-virtual {v5, v6}, Landroidx/compose/foundation/layout/Arrangement;->spacedBy-0680j_4(F)Landroidx/compose/foundation/layout/Arrangement$HorizontalOrVertical;

    .line 1059
    .line 1060
    .line 1061
    move-result-object v12

    .line 1062
    invoke-static/range {v20 .. v20}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 1063
    .line 1064
    .line 1065
    move-result v6

    .line 1066
    invoke-virtual {v5, v6}, Landroidx/compose/foundation/layout/Arrangement;->spacedBy-0680j_4(F)Landroidx/compose/foundation/layout/Arrangement$HorizontalOrVertical;

    .line 1067
    .line 1068
    .line 1069
    move-result-object v5

    .line 1070
    sget-object v6, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 1071
    .line 1072
    const/4 v10, 0x1

    .line 1073
    invoke-static {v6, v7, v10, v14}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxSize$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 1074
    .line 1075
    .line 1076
    move-result-object v30

    .line 1077
    invoke-static/range {v20 .. v20}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 1078
    .line 1079
    .line 1080
    move-result v32

    .line 1081
    const/16 v35, 0xd

    .line 1082
    .line 1083
    const/16 v36, 0x0

    .line 1084
    .line 1085
    const/16 v31, 0x0

    .line 1086
    .line 1087
    const/16 v33, 0x0

    .line 1088
    .line 1089
    const/16 v34, 0x0

    .line 1090
    .line 1091
    invoke-static/range {v30 .. v36}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 1092
    .line 1093
    .line 1094
    move-result-object v7

    .line 1095
    invoke-interface {v8, v11}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 1096
    .line 1097
    .line 1098
    move-result v10

    .line 1099
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 1100
    .line 1101
    .line 1102
    move-result-object v13

    .line 1103
    if-nez v10, :cond_24

    .line 1104
    .line 1105
    sget-object v10, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 1106
    .line 1107
    invoke-virtual {v10}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 1108
    .line 1109
    .line 1110
    move-result-object v10

    .line 1111
    if-ne v13, v10, :cond_25

    .line 1112
    .line 1113
    :cond_24
    new-instance v13, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 1114
    .line 1115
    invoke-direct {v13, v11}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;)V

    .line 1116
    .line 1117
    .line 1118
    invoke-interface {v8, v13}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 1119
    .line 1120
    .line 1121
    :cond_25
    check-cast v13, Lkotlin/jvm/functions/Function3;

    .line 1122
    .line 1123
    invoke-static {v7, v13}, Landroidx/compose/ui/layout/LayoutModifierKt;->layout(Landroidx/compose/ui/Modifier;Lkotlin/jvm/functions/Function3;)Landroidx/compose/ui/Modifier;

    .line 1124
    .line 1125
    .line 1126
    move-result-object v7

    .line 1127
    invoke-interface {v8, v4}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 1128
    .line 1129
    .line 1130
    move-result v10

    .line 1131
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 1132
    .line 1133
    .line 1134
    move-result-object v11

    .line 1135
    if-nez v10, :cond_26

    .line 1136
    .line 1137
    sget-object v10, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 1138
    .line 1139
    invoke-virtual {v10}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 1140
    .line 1141
    .line 1142
    move-result-object v10

    .line 1143
    if-ne v11, v10, :cond_27

    .line 1144
    .line 1145
    :cond_26
    new-instance v11, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 1146
    .line 1147
    invoke-direct {v11, v4}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;)V

    .line 1148
    .line 1149
    .line 1150
    invoke-interface {v8, v11}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 1151
    .line 1152
    .line 1153
    :cond_27
    check-cast v11, Lkotlin/jvm/functions/Function1;

    .line 1154
    .line 1155
    invoke-static {v7, v11}, Landroidx/compose/foundation/layout/OffsetKt;->offset(Landroidx/compose/ui/Modifier;Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/Modifier;

    .line 1156
    .line 1157
    .line 1158
    move-result-object v4

    .line 1159
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 1160
    .line 1161
    .line 1162
    move-result-object v7

    .line 1163
    sget-object v10, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 1164
    .line 1165
    invoke-virtual {v10}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 1166
    .line 1167
    .line 1168
    move-result-object v10

    .line 1169
    if-ne v7, v10, :cond_28

    .line 1170
    .line 1171
    new-instance v7, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 1172
    .line 1173
    invoke-direct {v7}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 1174
    .line 1175
    .line 1176
    invoke-interface {v8, v7}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 1177
    .line 1178
    .line 1179
    :cond_28
    check-cast v7, Lkotlin/jvm/functions/Function1;

    .line 1180
    .line 1181
    move-object/from16 v10, v27

    .line 1182
    .line 1183
    invoke-static {v10, v7}, Landroidx/paging/compose/LazyFoundationExtensionsKt;->itemKey(Landroidx/paging/compose/LazyPagingItems;Lkotlin/jvm/functions/Function1;)Lkotlin/jvm/functions/Function1;

    .line 1184
    .line 1185
    .line 1186
    move-result-object v7

    .line 1187
    new-instance v11, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 1188
    .line 1189
    move-object/from16 v13, v28

    .line 1190
    .line 1191
    invoke-direct {v11, v13}, Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;)V

    .line 1192
    .line 1193
    .line 1194
    const v13, 0x454f1e3f

    .line 1195
    .line 1196
    .line 1197
    const/16 v14, 0x36

    .line 1198
    .line 1199
    const/4 v15, 0x1

    .line 1200
    invoke-static {v13, v15, v11, v8, v14}, Landroidx/compose/runtime/internal/ComposableLambdaKt;->rememberComposableLambda(IZLjava/lang/Object;Landroidx/compose/runtime/Composer;I)Landroidx/compose/runtime/internal/ComposableLambda;

    .line 1201
    .line 1202
    .line 1203
    move-result-object v22

    .line 1204
    sget v11, Landroidx/paging/compose/LazyPagingItems;->$stable:I

    .line 1205
    .line 1206
    const/16 v21, 0x3

    .line 1207
    .line 1208
    shl-int/lit8 v11, v11, 0x3

    .line 1209
    .line 1210
    const/high16 v13, 0x6c30000

    .line 1211
    .line 1212
    or-int/2addr v11, v13

    .line 1213
    const/high16 v25, 0x6c00000

    .line 1214
    .line 1215
    move-object/from16 v13, v26

    .line 1216
    .line 1217
    const v26, 0x1fe40

    .line 1218
    .line 1219
    .line 1220
    move-object/from16 v18, v10

    .line 1221
    .line 1222
    const/4 v10, 0x0

    .line 1223
    move-object/from16 v16, v13

    .line 1224
    .line 1225
    const/4 v13, 0x0

    .line 1226
    move/from16 v17, v14

    .line 1227
    .line 1228
    const/4 v14, 0x0

    .line 1229
    move/from16 v19, v15

    .line 1230
    .line 1231
    const/4 v15, 0x0

    .line 1232
    move-object/from16 v20, v16

    .line 1233
    .line 1234
    const/16 v16, 0x0

    .line 1235
    .line 1236
    move/from16 v21, v17

    .line 1237
    .line 1238
    const/16 v17, 0x0

    .line 1239
    .line 1240
    move-object/from16 v27, v18

    .line 1241
    .line 1242
    const/16 v18, 0x0

    .line 1243
    .line 1244
    move/from16 v23, v19

    .line 1245
    .line 1246
    const/16 v19, 0x0

    .line 1247
    .line 1248
    move-object/from16 v28, v20

    .line 1249
    .line 1250
    const/16 v20, 0x0

    .line 1251
    .line 1252
    move/from16 v30, v21

    .line 1253
    .line 1254
    const/16 v21, 0x0

    .line 1255
    .line 1256
    move-object v0, v6

    .line 1257
    move-object/from16 v23, v8

    .line 1258
    .line 1259
    move-object/from16 v8, v24

    .line 1260
    .line 1261
    move/from16 v1, v30

    .line 1262
    .line 1263
    move-object v6, v4

    .line 1264
    move/from16 v24, v11

    .line 1265
    .line 1266
    move-object v4, v3

    .line 1267
    move-object v11, v5

    .line 1268
    move-object/from16 v5, v27

    .line 1269
    .line 1270
    move-object/from16 v3, v28

    .line 1271
    .line 1272
    invoke-static/range {v4 .. v26}, Ltop/cycdm/cycapp/ui/common/StatusLazyLayoutKt;->T(Landroidx/compose/foundation/lazy/grid/GridCells;Landroidx/paging/compose/LazyPagingItems;Landroidx/compose/ui/Modifier;Lkotlin/jvm/functions/Function1;Landroidx/compose/foundation/lazy/grid/LazyGridState;Landroidx/compose/foundation/layout/PaddingValues;ZLandroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/foundation/layout/Arrangement$Horizontal;Landroidx/compose/foundation/layout/WindowInsets;Ljava/lang/String;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function3;Lkotlin/jvm/functions/Function3;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ZLkotlin/jvm/functions/Function3;Landroidx/compose/runtime/Composer;III)V

    .line 1273
    .line 1274
    .line 1275
    move-object v4, v8

    .line 1276
    move-object/from16 v8, v23

    .line 1277
    .line 1278
    sget-object v5, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 1279
    .line 1280
    invoke-virtual {v5}, Landroidx/compose/ui/Alignment$Companion;->getBottomEnd()Landroidx/compose/ui/Alignment;

    .line 1281
    .line 1282
    .line 1283
    move-result-object v5

    .line 1284
    invoke-interface {v2, v0, v5}, Landroidx/compose/foundation/layout/BoxScope;->align(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/Alignment;)Landroidx/compose/ui/Modifier;

    .line 1285
    .line 1286
    .line 1287
    move-result-object v5

    .line 1288
    invoke-static/range {v29 .. v29}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/State;)Z

    .line 1289
    .line 1290
    .line 1291
    move-result v0

    .line 1292
    const/4 v13, 0x7

    .line 1293
    const/4 v9, 0x0

    .line 1294
    const/4 v10, 0x0

    .line 1295
    const-wide/16 v11, 0x0

    .line 1296
    .line 1297
    invoke-static/range {v9 .. v14}, Landroidx/compose/animation/EnterExitTransitionKt;->scaleIn-L8ZKh-E$default(Landroidx/compose/animation/core/FiniteAnimationSpec;FJILjava/lang/Object;)Landroidx/compose/animation/EnterTransition;

    .line 1298
    .line 1299
    .line 1300
    move-result-object v6

    .line 1301
    invoke-static/range {v9 .. v14}, Landroidx/compose/animation/EnterExitTransitionKt;->scaleOut-L8ZKh-E$default(Landroidx/compose/animation/core/FiniteAnimationSpec;FJILjava/lang/Object;)Landroidx/compose/animation/ExitTransition;

    .line 1302
    .line 1303
    .line 1304
    move-result-object v7

    .line 1305
    new-instance v2, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 1306
    .line 1307
    invoke-direct {v2, v3, v4}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;Landroidx/compose/foundation/lazy/grid/LazyGridState;)V

    .line 1308
    .line 1309
    .line 1310
    const v3, 0x55c34bd7

    .line 1311
    .line 1312
    .line 1313
    const/4 v15, 0x1

    .line 1314
    invoke-static {v3, v15, v2, v8, v1}, Landroidx/compose/runtime/internal/ComposableLambdaKt;->rememberComposableLambda(IZLjava/lang/Object;Landroidx/compose/runtime/Composer;I)Landroidx/compose/runtime/internal/ComposableLambda;

    .line 1315
    .line 1316
    .line 1317
    move-result-object v9

    .line 1318
    const v11, 0x30d80

    .line 1319
    .line 1320
    .line 1321
    const/16 v12, 0x10

    .line 1322
    .line 1323
    const/4 v8, 0x0

    .line 1324
    move v4, v0

    .line 1325
    move-object/from16 v10, v23

    .line 1326
    .line 1327
    invoke-static/range {v4 .. v12}, Landroidx/compose/animation/AnimatedVisibilityKt;->AnimatedVisibility(ZLandroidx/compose/ui/Modifier;Landroidx/compose/animation/EnterTransition;Landroidx/compose/animation/ExitTransition;Ljava/lang/String;Lkotlin/jvm/functions/Function3;Landroidx/compose/runtime/Composer;II)V

    .line 1328
    .line 1329
    .line 1330
    move-object v8, v10

    .line 1331
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 1332
    .line 1333
    .line 1334
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 1335
    .line 1336
    .line 1337
    move-result v0

    .line 1338
    if-eqz v0, :cond_29

    .line 1339
    .line 1340
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 1341
    .line 1342
    .line 1343
    :cond_29
    :goto_9
    invoke-interface {v8}, Landroidx/compose/runtime/Composer;->endRestartGroup()Landroidx/compose/runtime/ScopeUpdateScope;

    .line 1344
    .line 1345
    .line 1346
    move-result-object v0

    .line 1347
    if-eqz v0, :cond_2a

    .line 1348
    .line 1349
    new-instance v1, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 1350
    .line 1351
    move-object/from16 v2, p0

    .line 1352
    .line 1353
    move/from16 v3, p2

    .line 1354
    .line 1355
    invoke-direct {v1, v2, v3}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;I)V

    .line 1356
    .line 1357
    .line 1358
    invoke-interface {v0, v1}, Landroidx/compose/runtime/ScopeUpdateScope;->updateScope(Lkotlin/jvm/functions/Function2;)V

    .line 1359
    .line 1360
    .line 1361
    :cond_2a
    return-void

    .line 1362
    :cond_2b
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 1363
    .line 1364
    const-string v1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 1365
    .line 1366
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 1367
    .line 1368
    .line 1369
    throw v0
.end method

.method public static final v(Landroidx/compose/runtime/State;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 0

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/State;->getValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 6
    .line 7
    return-object p0
.end method

.method public static final w(Ltop/cycdm/model/NavInfoInner;ILandroidx/compose/runtime/Composer;I)Lkotlin/t;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Landroidx/compose/runtime/RecomposeScopeImplKt;->updateChangedFlags(I)I

    move-result p1

    invoke-static {p0, p2, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;Landroidx/compose/runtime/Composer;I)V

    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    return-object p0
.end method

.method public static final x()Landroidx/compose/runtime/MutableFloatState;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {v0}, Landroidx/compose/runtime/PrimitiveSnapshotStateKt;->mutableFloatStateOf(F)Landroidx/compose/runtime/MutableFloatState;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    return-object v0
.end method

.method public static final y(Landroidx/compose/foundation/lazy/grid/LazyGridState;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/compose/foundation/lazy/grid/LazyGridState;->getFirstVisibleItemIndex()I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    if-lez p0, :cond_0

    .line 6
    .line 7
    const/4 p0, 0x1

    .line 8
    return p0

    .line 9
    :cond_0
    const/4 p0, 0x0

    .line 10
    return p0
.end method

.method public static final z(Landroidx/compose/runtime/State;)Z
    .locals 0

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/State;->getValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Ljava/lang/Boolean;

    .line 6
    .line 7
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    return p0
.end method
