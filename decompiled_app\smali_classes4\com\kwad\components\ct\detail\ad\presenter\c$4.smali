.class final Lcom/kwad/components/ct/detail/ad/presenter/c$4;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/core/webview/d/a/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/c;->a(Lcom/kwad/components/core/webview/a;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amv:Lcom/kwad/components/ct/detail/ad/presenter/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$4;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/kwad/sdk/core/webview/d/b/a;)V
    .locals 2
    .param p1    # Lcom/kwad/sdk/core/webview/d/b/a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$4;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/c;->D(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-object p1, p1, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 8
    .line 9
    instance-of p1, p1, Lcom/kwad/components/ct/detail/ad/a;

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    invoke-static {}, Lcom/kwad/components/core/t/d;->sM()Z

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    if-eqz p1, :cond_0

    .line 18
    .line 19
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$4;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 24
    .line 25
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->E(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/components/ct/detail/c;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 30
    .line 31
    const/4 v1, 0x1

    .line 32
    invoke-virtual {p1, v0, v1}, Lcom/kwad/components/ct/e/b;->b(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 33
    .line 34
    .line 35
    :cond_0
    return-void
.end method
