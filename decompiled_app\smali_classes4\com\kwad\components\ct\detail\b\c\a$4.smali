.class final Lcom/kwad/components/ct/detail/b/c/a$4;
.super Lcom/kwad/sdk/widget/swipe/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awE:Lcom/kwad/components/ct/detail/b/c/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$4;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/sdk/widget/swipe/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final j(F)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$4;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->i(Lcom/kwad/components/ct/detail/b/c/a;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    const/4 v0, 0x0

    .line 10
    cmpl-float p1, p1, v0

    .line 11
    .line 12
    if-eqz p1, :cond_0

    .line 13
    .line 14
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$4;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 15
    .line 16
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/c/a;->j(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/ct/detail/c;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 21
    .line 22
    invoke-static {p1, v0}, Lcom/kwad/components/ct/detail/b/c/a;->a(Lcom/kwad/components/ct/detail/b/c/a;Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 23
    .line 24
    .line 25
    :cond_0
    return-void
.end method
