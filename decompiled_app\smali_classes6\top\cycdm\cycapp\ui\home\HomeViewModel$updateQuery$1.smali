.class final Ltop/cycdm/cycapp/ui/home/<USER>
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;I)Lkotlinx/coroutines/w1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ltop/cycdm/cycapp/ui/home/<USER>
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "<PERSON><PERSON><PERSON>/jvm/functions/Function2<",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Ltop/cycdm/cycapp/ui/home/<USER>",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.home.HomeViewModel$updateQuery$1"
    f = "HomeViewModel.kt"
    i = {}
    l = {
        0xe4
    }
    m = "invokeSuspend"
    n = {}
    s = {}
.end annotation


# instance fields
.field final synthetic $i:I

.field final synthetic $type:Ltop/cycdm/model/NavType;

.field final synthetic $typeId:I

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/home/<USER>


# direct methods
.method public constructor <init>(ILtop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ltop/cycdm/cycapp/ui/home/<USER>",
            "Ltop/cycdm/model/NavType;",
            "I",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;

    iput p4, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Ltop/cycdm/cycapp/ui/home/<USER>/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>

    move-result-object p0

    return-object p0
.end method

.method private static final invokeSuspend$lambda$0(Ltop/cycdm/cycapp/ui/home/<USER>/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 20

    .line 1
    invoke-virtual/range {p1 .. p1}, Lorg/orbitmvi/orbit/syntax/simple/a;->a()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    move-object v1, v0

    .line 6
    check-cast v1, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 7
    .line 8
    invoke-static/range {p0 .. p0}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-static {v0}, Lkotlin/collections/s0;->v(Ljava/util/Map;)Ljava/util/Map;

    .line 13
    .line 14
    .line 15
    move-result-object v9

    .line 16
    const v18, 0xff7f

    .line 17
    .line 18
    .line 19
    const/16 v19, 0x0

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    const/4 v3, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    const/4 v5, 0x0

    .line 25
    const/4 v6, 0x0

    .line 26
    const/4 v7, 0x0

    .line 27
    const/4 v8, 0x0

    .line 28
    const/4 v10, 0x0

    .line 29
    const/4 v11, 0x0

    .line 30
    const/4 v12, 0x0

    .line 31
    const/4 v13, 0x0

    .line 32
    const/4 v14, 0x0

    .line 33
    const/4 v15, 0x0

    .line 34
    const/16 v16, 0x0

    .line 35
    .line 36
    const/16 v17, 0x0

    .line 37
    .line 38
    invoke-static/range {v1 .. v19}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/Map;Ljava/util/Map;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;Ltop/cycdm/model/z;Ltop/cycdm/model/s;Ltop/cycdm/model/p;Ltop/cycdm/cycapp/ui/weekly/DayOfWeek;Ljava/util/List;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    return-object v0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    iget-object v3, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;

    iget v4, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;ILkotlin/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/home/<USER>

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto/16 :goto_2

    .line 16
    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;

    .line 29
    .line 30
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 31
    .line 32
    invoke-virtual {p1}, Lorg/orbitmvi/orbit/syntax/simple/b;->b()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    check-cast v1, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 37
    .line 38
    invoke-virtual {v1}, Ltop/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    iget v3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 43
    .line 44
    invoke-static {v3}, Ln5/a;->e(I)Ljava/lang/Integer;

    .line 45
    .line 46
    .line 47
    move-result-object v3

    .line 48
    invoke-interface {v1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    check-cast v1, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 53
    .line 54
    if-nez v1, :cond_2

    .line 55
    .line 56
    new-instance v3, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 57
    .line 58
    const/16 v9, 0x1f

    .line 59
    .line 60
    const/4 v10, 0x0

    .line 61
    const/4 v4, 0x0

    .line 62
    const/4 v5, 0x0

    .line 63
    const/4 v6, 0x0

    .line 64
    const/4 v7, 0x0

    .line 65
    const/4 v8, 0x0

    .line 66
    invoke-direct/range {v3 .. v10}, Ltop/cycdm/cycapp/ui/home/<USER>/jvm/internal/n;)V

    .line 67
    .line 68
    .line 69
    move-object v4, v3

    .line 70
    goto :goto_0

    .line 71
    :cond_2
    move-object v4, v1

    .line 72
    :goto_0
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 73
    .line 74
    invoke-static {v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/util/Map;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    iget v3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 79
    .line 80
    invoke-static {v3}, Ln5/a;->e(I)Ljava/lang/Integer;

    .line 81
    .line 82
    .line 83
    move-result-object v3

    .line 84
    iget-object v5, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavType;

    .line 85
    .line 86
    sget-object v6, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 87
    .line 88
    invoke-virtual {v5}, Ljava/lang/Enum;->ordinal()I

    .line 89
    .line 90
    .line 91
    move-result v5

    .line 92
    aget v5, v6, v5

    .line 93
    .line 94
    if-eq v5, v2, :cond_7

    .line 95
    .line 96
    const/4 v6, 0x2

    .line 97
    if-eq v5, v6, :cond_6

    .line 98
    .line 99
    const/4 v6, 0x3

    .line 100
    if-eq v5, v6, :cond_5

    .line 101
    .line 102
    const/4 v6, 0x4

    .line 103
    if-eq v5, v6, :cond_4

    .line 104
    .line 105
    const/4 v6, 0x5

    .line 106
    if-ne v5, v6, :cond_3

    .line 107
    .line 108
    iget v9, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 109
    .line 110
    const/16 v10, 0xf

    .line 111
    .line 112
    const/4 v11, 0x0

    .line 113
    const/4 v5, 0x0

    .line 114
    const/4 v6, 0x0

    .line 115
    const/4 v7, 0x0

    .line 116
    const/4 v8, 0x0

    .line 117
    invoke-static/range {v4 .. v11}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 118
    .line 119
    .line 120
    move-result-object v4

    .line 121
    goto :goto_1

    .line 122
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 123
    .line 124
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 125
    .line 126
    .line 127
    throw p1

    .line 128
    :cond_4
    iget v8, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 129
    .line 130
    const/16 v10, 0x17

    .line 131
    .line 132
    const/4 v11, 0x0

    .line 133
    const/4 v5, 0x0

    .line 134
    const/4 v6, 0x0

    .line 135
    const/4 v7, 0x0

    .line 136
    const/4 v9, 0x0

    .line 137
    invoke-static/range {v4 .. v11}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 138
    .line 139
    .line 140
    move-result-object v4

    .line 141
    goto :goto_1

    .line 142
    :cond_5
    iget v7, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 143
    .line 144
    const/16 v10, 0x1b

    .line 145
    .line 146
    const/4 v11, 0x0

    .line 147
    const/4 v5, 0x0

    .line 148
    const/4 v6, 0x0

    .line 149
    const/4 v8, 0x0

    .line 150
    const/4 v9, 0x0

    .line 151
    invoke-static/range {v4 .. v11}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 152
    .line 153
    .line 154
    move-result-object v4

    .line 155
    goto :goto_1

    .line 156
    :cond_6
    iget v6, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 157
    .line 158
    const/16 v10, 0x1d

    .line 159
    .line 160
    const/4 v11, 0x0

    .line 161
    const/4 v5, 0x0

    .line 162
    const/4 v7, 0x0

    .line 163
    const/4 v8, 0x0

    .line 164
    const/4 v9, 0x0

    .line 165
    invoke-static/range {v4 .. v11}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 166
    .line 167
    .line 168
    move-result-object v4

    .line 169
    goto :goto_1

    .line 170
    :cond_7
    iget v5, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 171
    .line 172
    const/16 v10, 0x1e

    .line 173
    .line 174
    const/4 v11, 0x0

    .line 175
    const/4 v6, 0x0

    .line 176
    const/4 v7, 0x0

    .line 177
    const/4 v8, 0x0

    .line 178
    const/4 v9, 0x0

    .line 179
    invoke-static/range {v4 .. v11}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>

    .line 180
    .line 181
    .line 182
    move-result-object v4

    .line 183
    :goto_1
    invoke-interface {v1, v3, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 184
    .line 185
    .line 186
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 187
    .line 188
    new-instance v3, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 189
    .line 190
    invoke-direct {v3, v1}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 191
    .line 192
    .line 193
    iput v2, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 194
    .line 195
    invoke-static {p1, v3, p0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->e(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object p1

    .line 199
    if-ne p1, v0, :cond_8

    .line 200
    .line 201
    return-object v0

    .line 202
    :cond_8
    :goto_2
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 203
    .line 204
    return-object p1
.end method
