.class final Lcom/kwad/components/ct/detail/b/b$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/widget/swipe/HorizontalSwipeLayout$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avf:Lcom/kwad/components/ct/detail/b/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/b$2;->avf:Lcom/kwad/components/ct/detail/b/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final xG()V
    .locals 0

    return-void
.end method

.method public final xH()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b$2;->avf:Lcom/kwad/components/ct/detail/b/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b;->c(Lcom/kwad/components/ct/detail/b/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
