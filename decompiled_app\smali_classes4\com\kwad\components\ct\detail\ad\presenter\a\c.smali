.class public final Lcom/kwad/components/ct/detail/ad/presenter/a/c;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/ct/detail/b/a/a;

    .line 5
    .line 6
    invoke-direct {v0}, Lcom/kwad/components/ct/detail/b/a/a;-><init>()V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->d(Lcom/kwad/sdk/mvp/Presenter;)V

    .line 10
    .line 11
    .line 12
    new-instance v0, Lcom/kwad/components/ct/detail/b/a/b;

    .line 13
    .line 14
    invoke-direct {v0}, Lcom/kwad/components/ct/detail/b/a/b;-><init>()V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->d(Lcom/kwad/sdk/mvp/Presenter;)V

    .line 18
    .line 19
    .line 20
    new-instance v0, Lcom/kwad/components/ct/detail/b/a/c;

    .line 21
    .line 22
    invoke-direct {v0}, Lcom/kwad/components/ct/detail/b/a/c;-><init>()V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->d(Lcom/kwad/sdk/mvp/Presenter;)V

    .line 26
    .line 27
    .line 28
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/a;

    .line 29
    .line 30
    invoke-direct {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/a;-><init>()V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->d(Lcom/kwad/sdk/mvp/Presenter;)V

    .line 34
    .line 35
    .line 36
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 37
    .line 38
    invoke-direct {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;-><init>()V

    .line 39
    .line 40
    .line 41
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->d(Lcom/kwad/sdk/mvp/Presenter;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method
