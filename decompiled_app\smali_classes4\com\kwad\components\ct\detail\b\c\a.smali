.class public final Lcom/kwad/components/ct/detail/b/c/a;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# static fields
.field private static avg:Z = false


# instance fields
.field private Zh:I

.field private alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

.field private amo:Lcom/kwad/components/core/j/a;

.field private aof:Lcom/kwad/sdk/widget/swipe/a;

.field private apZ:Lcom/kwad/components/core/widget/a/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private ate:Lcom/kwad/sdk/widget/swipe/c;

.field private awA:J

.field private awB:I

.field awC:Z

.field awD:Z

.field private aww:Lcom/kwad/sdk/utils/bw;

.field private awx:Lcom/kwad/sdk/utils/bw;

.field private awy:Z

.field private awz:Z

.field private eQ:Lcom/kwad/sdk/core/i/c;

.field private ei:Lcom/kwad/sdk/widget/n;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mRootView:Landroid/view/View;

.field private mSceneImpl:Lcom/kwad/sdk/internal/api/SceneImpl;

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

.field private wX:Lcom/kwad/components/core/video/l;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awy:Z

    .line 6
    .line 7
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awz:Z

    .line 8
    .line 9
    new-instance v1, Lcom/kwad/components/ct/detail/b/c/a$1;

    .line 10
    .line 11
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/b/c/a$1;-><init>(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 12
    .line 13
    .line 14
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->ei:Lcom/kwad/sdk/widget/n;

    .line 15
    .line 16
    new-instance v1, Lcom/kwad/components/ct/detail/b/c/a$2;

    .line 17
    .line 18
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/b/c/a$2;-><init>(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 19
    .line 20
    .line 21
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 22
    .line 23
    new-instance v1, Lcom/kwad/components/ct/detail/b/c/a$3;

    .line 24
    .line 25
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/b/c/a$3;-><init>(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 26
    .line 27
    .line 28
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->amo:Lcom/kwad/components/core/j/a;

    .line 29
    .line 30
    new-instance v1, Lcom/kwad/components/ct/detail/b/c/a$4;

    .line 31
    .line 32
    invoke-direct {v1, p0}, Lcom/kwad/components/ct/detail/b/c/a$4;-><init>(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 33
    .line 34
    .line 35
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->aof:Lcom/kwad/sdk/widget/swipe/a;

    .line 36
    .line 37
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awC:Z

    .line 38
    .line 39
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awD:Z

    .line 40
    .line 41
    new-instance v0, Lcom/kwad/components/ct/detail/b/c/a$5;

    .line 42
    .line 43
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/c/a$5;-><init>(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 44
    .line 45
    .line 46
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 47
    .line 48
    return-void
.end method

.method private BQ()V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    iput v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awB:I

    .line 3
    .line 4
    const-wide/16 v1, 0x0

    .line 5
    .line 6
    iput-wide v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->awA:J

    .line 7
    .line 8
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awy:Z

    .line 9
    .line 10
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awz:Z

    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->wX:Lcom/kwad/components/core/video/l;

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-virtual {v0}, Lcom/kwad/components/core/video/l;->reset()V

    .line 17
    .line 18
    .line 19
    :cond_0
    return-void
.end method

.method private BR()V
    .locals 5

    .line 1
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awy:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    goto/16 :goto_1

    .line 6
    .line 7
    :cond_0
    const/4 v0, 0x1

    .line 8
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awy:Z

    .line 9
    .line 10
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 11
    .line 12
    if-nez v1, :cond_1

    .line 13
    .line 14
    goto :goto_1

    .line 15
    :cond_1
    invoke-virtual {v1}, Lcom/kwad/components/ct/detail/viewpager/e;->getPreItem()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 20
    .line 21
    invoke-virtual {v2}, Lcom/kwad/components/ct/detail/viewpager/e;->getCurrentItem()I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    iget-object v3, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 26
    .line 27
    invoke-virtual {v3}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->Cu()Z

    .line 28
    .line 29
    .line 30
    move-result v3

    .line 31
    const/4 v4, 0x3

    .line 32
    if-eqz v3, :cond_3

    .line 33
    .line 34
    if-le v2, v1, :cond_2

    .line 35
    .line 36
    move v4, v0

    .line 37
    goto :goto_0

    .line 38
    :cond_2
    if-ge v2, v1, :cond_4

    .line 39
    .line 40
    const/4 v4, 0x2

    .line 41
    goto :goto_0

    .line 42
    :cond_3
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 43
    .line 44
    invoke-virtual {v1, v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->setReportedItemImpression(Z)V

    .line 45
    .line 46
    .line 47
    :cond_4
    :goto_0
    sget-boolean v1, Lcom/kwad/components/ct/detail/b/c/a;->avg:Z

    .line 48
    .line 49
    if-eqz v1, :cond_5

    .line 50
    .line 51
    new-instance v1, Ljava/lang/StringBuilder;

    .line 52
    .line 53
    const-string v2, "position: "

    .line 54
    .line 55
    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    iget v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->Zh:I

    .line 59
    .line 60
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 61
    .line 62
    .line 63
    const-string v2, " reportItemImpression enterType="

    .line 64
    .line 65
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 69
    .line 70
    .line 71
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    const-string v2, "DetailLogPagePresenter"

    .line 76
    .line 77
    invoke-static {v2, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 78
    .line 79
    .line 80
    :cond_5
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 85
    .line 86
    invoke-static {}, Lcom/kwad/components/core/video/c;->tt()Lcom/kwad/components/core/video/c;

    .line 87
    .line 88
    .line 89
    move-result-object v3

    .line 90
    invoke-virtual {v3}, Lcom/kwad/components/core/video/c;->tw()I

    .line 91
    .line 92
    .line 93
    move-result v3

    .line 94
    invoke-virtual {v1, v2, v4, v3}, Lcom/kwad/components/ct/e/b;->a(Lcom/kwad/components/ct/response/model/CtAdTemplate;II)V

    .line 95
    .line 96
    .line 97
    const-class v1, Lcom/kwad/components/ec/api/a;

    .line 98
    .line 99
    invoke-static {v1}, Lcom/kwad/sdk/components/d;->g(Ljava/lang/Class;)Lcom/kwad/sdk/components/b;

    .line 100
    .line 101
    .line 102
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 103
    .line 104
    iget-object v1, v1, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 105
    .line 106
    iget v2, v1, Lcom/kwad/components/ct/home/<USER>

    .line 107
    .line 108
    add-int/2addr v2, v0

    .line 109
    iput v2, v1, Lcom/kwad/components/ct/home/<USER>

    .line 110
    .line 111
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awz:Z

    .line 112
    .line 113
    if-nez v0, :cond_6

    .line 114
    .line 115
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/a;->BS()Z

    .line 116
    .line 117
    .line 118
    move-result v0

    .line 119
    if-nez v0, :cond_6

    .line 120
    .line 121
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 122
    .line 123
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/b/c/a;->h(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 124
    .line 125
    .line 126
    :cond_6
    :goto_1
    return-void
.end method

.method private BS()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->ate:Lcom/kwad/sdk/widget/swipe/c;

    .line 2
    .line 3
    if-eqz v0, :cond_2

    .line 4
    .line 5
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->getSourceType()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_1

    .line 14
    .line 15
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->ate:Lcom/kwad/sdk/widget/swipe/c;

    .line 16
    .line 17
    invoke-virtual {v0}, Lcom/kwad/sdk/widget/swipe/c;->aqg()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_2

    .line 22
    .line 23
    :cond_1
    const/4 v0, 0x1

    .line 24
    return v0

    .line 25
    :cond_2
    const/4 v0, 0x0

    .line 26
    return v0
.end method

.method private BT()V
    .locals 6

    .line 1
    const-class v0, Lcom/kwad/components/ec/api/a;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/sdk/components/d;->g(Ljava/lang/Class;)Lcom/kwad/sdk/components/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/kwad/components/ec/api/a;

    .line 8
    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    iget-wide v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awA:J

    .line 13
    .line 14
    const-wide/16 v2, 0xbb8

    .line 15
    .line 16
    cmp-long v2, v0, v2

    .line 17
    .line 18
    const/4 v3, 0x1

    .line 19
    const-wide/16 v4, 0x1388

    .line 20
    .line 21
    if-ltz v2, :cond_1

    .line 22
    .line 23
    cmp-long v2, v0, v4

    .line 24
    .line 25
    if-gez v2, :cond_1

    .line 26
    .line 27
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awC:Z

    .line 28
    .line 29
    if-nez v0, :cond_2

    .line 30
    .line 31
    iput-boolean v3, p0, Lcom/kwad/components/ct/detail/b/c/a;->awC:Z

    .line 32
    .line 33
    return-void

    .line 34
    :cond_1
    cmp-long v0, v0, v4

    .line 35
    .line 36
    if-ltz v0, :cond_2

    .line 37
    .line 38
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awD:Z

    .line 39
    .line 40
    if-nez v0, :cond_2

    .line 41
    .line 42
    iput-boolean v3, p0, Lcom/kwad/components/ct/detail/b/c/a;->awD:Z

    .line 43
    .line 44
    :cond_2
    :goto_0
    return-void
.end method

.method public static synthetic BU()Z
    .locals 1

    .line 1
    sget-boolean v0, Lcom/kwad/components/ct/detail/b/c/a;->avg:Z

    .line 2
    .line 3
    return v0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c/a;J)J
    .locals 0

    .line 1
    iput-wide p1, p0, Lcom/kwad/components/ct/detail/b/c/a;->awA:J

    return-wide p1
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 2
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    return-object p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c/a;JJ)V
    .locals 0

    .line 3
    invoke-direct {p0, p1, p2, p3, p4}, Lcom/kwad/components/ct/detail/b/c/a;->m(JJ)V

    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/c/a;Lcom/kwad/components/ct/response/model/CtAdTemplate;)V
    .locals 0

    .line 4
    invoke-direct {p0, p1}, Lcom/kwad/components/ct/detail/b/c/a;->h(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/a;->BR()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->aww:Lcom/kwad/sdk/utils/bw;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/b/c/a;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->Zh:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/widget/a/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->apZ:Lcom/kwad/components/core/widget/a/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/core/i/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->eQ:Lcom/kwad/sdk/core/i/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awx:Lcom/kwad/sdk/utils/bw;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic h(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/a;->BQ()V

    return-void
.end method

.method private h(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V
    .locals 1
    .param p1    # Lcom/kwad/components/ct/response/model/CtAdTemplate;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awz:Z

    .line 3
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    iget-boolean v0, v0, Lcom/kwad/components/ct/detail/c;->alZ:Z

    if-eqz v0, :cond_0

    .line 4
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/kwad/components/ct/e/b;->aj(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    iget-boolean v0, v0, Lcom/kwad/components/ct/detail/c;->ama:Z

    if-eqz v0, :cond_1

    .line 6
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/kwad/components/ct/e/b;->ak(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 7
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    iget-boolean v0, v0, Lcom/kwad/components/ct/detail/c;->amb:Z

    if-eqz v0, :cond_2

    .line 8
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/kwad/components/ct/e/b;->al(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 9
    :cond_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    iget-boolean v0, v0, Lcom/kwad/components/ct/detail/c;->amc:Z

    if-eqz v0, :cond_3

    .line 10
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/kwad/components/ct/e/b;->am(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    .line 11
    :cond_3
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    iget-boolean v0, v0, Lcom/kwad/components/ct/detail/c;->amd:Z

    if-eqz v0, :cond_4

    .line 12
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/kwad/components/ct/e/b;->an(Lcom/kwad/components/ct/response/model/CtAdTemplate;)V

    :cond_4
    return-void
.end method

.method public static synthetic i(Lcom/kwad/components/ct/detail/b/c/a;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awz:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic j(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic k(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/a;->BT()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic l(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/video/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/c/a;->wX:Lcom/kwad/components/core/video/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic m(Lcom/kwad/components/ct/detail/b/c/a;)I
    .locals 2

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awB:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->awB:I

    return v0
.end method

.method private m(JJ)V
    .locals 12

    const-wide/16 v0, 0x0

    cmp-long v2, p1, v0

    if-nez v2, :cond_0

    goto :goto_1

    .line 2
    :cond_0
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 3
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 4
    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/e;->eP(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/sdk/core/response/model/AdInfo;

    move-result-object v2

    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/a;->M(Lcom/kwad/sdk/core/response/model/AdInfo;)I

    move-result v2

    mul-int/lit16 v2, v2, 0x3e8

    int-to-long v2, v2

    goto :goto_0

    .line 5
    :cond_1
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 6
    invoke-static {v2}, Lcom/kwad/components/ct/response/a/a;->ay(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Lcom/kwad/components/ct/response/model/CtPhotoInfo;

    move-result-object v2

    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/h;->f(Lcom/kwad/sdk/core/response/model/PhotoInfo;)Ljava/lang/Long;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    move-result-wide v2

    .line 7
    :goto_0
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    if-nez v4, :cond_2

    :goto_1
    return-void

    .line 8
    :cond_2
    sget-boolean v4, Lcom/kwad/components/ct/detail/b/c/a;->avg:Z

    if-eqz v4, :cond_3

    .line 9
    new-instance v4, Ljava/lang/StringBuilder;

    const-string v5, "position: "

    invoke-direct {v4, v5}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v5, p0, Lcom/kwad/components/ct/detail/b/c/a;->Zh:I

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v5, " reportPlayFinish videoDuration: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v2, " stayDuration: "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, p1, p2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v2, " playDuration "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-wide v2, p3

    invoke-virtual {v4, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const-string v5, "DetailLogPagePresenter"

    invoke-static {v5, v4}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_2

    :cond_3
    move-wide v2, p3

    .line 10
    :goto_2
    iget v4, p0, Lcom/kwad/components/ct/detail/b/c/a;->awB:I

    if-lez v4, :cond_4

    iget-wide v4, p0, Lcom/kwad/components/ct/detail/b/c/a;->awA:J

    cmp-long v0, v4, v0

    if-nez v0, :cond_4

    const/4 v0, 0x1

    :goto_3
    move v5, v0

    goto :goto_4

    :cond_4
    const/4 v0, 0x2

    goto :goto_3

    .line 11
    :goto_4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/e;->getPreItem()I

    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/e;->getCurrentItem()I

    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->wX:Lcom/kwad/components/core/video/l;

    invoke-virtual {v0}, Lcom/kwad/components/core/video/l;->tW()Lcom/kwad/components/core/video/l$a;

    move-result-object v0

    move-object v1, v0

    .line 14
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    move-result-object v0

    move-object v4, v1

    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->mSceneImpl:Lcom/kwad/sdk/internal/api/SceneImpl;

    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 15
    invoke-virtual {v4}, Lcom/kwad/components/core/video/l$a;->tZ()J

    move-result-wide v8

    invoke-virtual {v4}, Lcom/kwad/components/core/video/l$a;->tY()I

    move-result v10

    const/4 v11, 0x0

    move-wide v6, p1

    move-wide v3, p3

    .line 16
    invoke-virtual/range {v0 .. v11}, Lcom/kwad/components/ct/e/b;->a(Lcom/kwad/sdk/internal/api/SceneImpl;Lcom/kwad/components/ct/response/model/CtAdTemplate;JIJJII)V

    .line 17
    const-class v0, Lcom/kwad/components/ec/api/a;

    invoke-static {v0}, Lcom/kwad/sdk/components/d;->g(Ljava/lang/Class;)Lcom/kwad/sdk/components/b;

    return-void
.end method


# virtual methods
.method public final T()V
    .locals 4

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getRootView()Landroid/view/View;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->mRootView:Landroid/view/View;

    .line 9
    .line 10
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 11
    .line 12
    iget-object v2, v1, Lcom/kwad/components/ct/detail/c;->alJ:Lcom/kwad/components/ct/home/<USER>

    .line 13
    .line 14
    if-eqz v2, :cond_0

    .line 15
    .line 16
    iget-object v3, v2, Lcom/kwad/components/ct/home/<USER>/kwad/components/core/widget/a/b;

    .line 17
    .line 18
    iput-object v3, p0, Lcom/kwad/components/ct/detail/b/c/a;->apZ:Lcom/kwad/components/core/widget/a/b;

    .line 19
    .line 20
    iget-object v3, v2, Lcom/kwad/components/ct/home/<USER>/kwad/sdk/internal/api/SceneImpl;

    .line 21
    .line 22
    iput-object v3, p0, Lcom/kwad/components/ct/detail/b/c/a;->mSceneImpl:Lcom/kwad/sdk/internal/api/SceneImpl;

    .line 23
    .line 24
    iget-object v2, v2, Lcom/kwad/components/ct/home/<USER>/kwad/sdk/widget/swipe/c;

    .line 25
    .line 26
    iput-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->ate:Lcom/kwad/sdk/widget/swipe/c;

    .line 27
    .line 28
    :cond_0
    iget-object v2, v1, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 29
    .line 30
    iput-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 31
    .line 32
    iget v1, v1, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 33
    .line 34
    iput v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->Zh:I

    .line 35
    .line 36
    instance-of v1, v0, Lcom/kwad/sdk/core/view/AdBasePvFrameLayout;

    .line 37
    .line 38
    if-eqz v1, :cond_1

    .line 39
    .line 40
    check-cast v0, Lcom/kwad/sdk/core/view/AdBasePvFrameLayout;

    .line 41
    .line 42
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->ei:Lcom/kwad/sdk/widget/n;

    .line 43
    .line 44
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/core/view/AdBasePvFrameLayout;->setVisibleListener(Lcom/kwad/sdk/widget/n;)V

    .line 45
    .line 46
    .line 47
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 48
    .line 49
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 50
    .line 51
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 52
    .line 53
    new-instance v0, Lcom/kwad/sdk/utils/bw;

    .line 54
    .line 55
    invoke-direct {v0}, Lcom/kwad/sdk/utils/bw;-><init>()V

    .line 56
    .line 57
    .line 58
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->aww:Lcom/kwad/sdk/utils/bw;

    .line 59
    .line 60
    new-instance v0, Lcom/kwad/sdk/utils/bw;

    .line 61
    .line 62
    invoke-direct {v0}, Lcom/kwad/sdk/utils/bw;-><init>()V

    .line 63
    .line 64
    .line 65
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->awx:Lcom/kwad/sdk/utils/bw;

    .line 66
    .line 67
    new-instance v0, Lcom/kwad/components/core/video/l;

    .line 68
    .line 69
    invoke-direct {v0}, Lcom/kwad/components/core/video/l;-><init>()V

    .line 70
    .line 71
    .line 72
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->wX:Lcom/kwad/components/core/video/l;

    .line 73
    .line 74
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/c/a;->BQ()V

    .line 75
    .line 76
    .line 77
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 78
    .line 79
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 80
    .line 81
    const/4 v1, 0x0

    .line 82
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a;->amo:Lcom/kwad/components/core/j/a;

    .line 83
    .line 84
    invoke-interface {v0, v1, v2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 85
    .line 86
    .line 87
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 88
    .line 89
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 90
    .line 91
    if-eqz v0, :cond_2

    .line 92
    .line 93
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 94
    .line 95
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/e/a;->getMediaPlayerType()I

    .line 96
    .line 97
    .line 98
    move-result v0

    .line 99
    iput v0, v1, Lcom/kwad/sdk/core/response/model/AdTemplate;->mMediaPlayerType:I

    .line 100
    .line 101
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 102
    .line 103
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 104
    .line 105
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 106
    .line 107
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 108
    .line 109
    .line 110
    :cond_2
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 111
    .line 112
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alR:Ljava/util/List;

    .line 113
    .line 114
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->aof:Lcom/kwad/sdk/widget/swipe/a;

    .line 115
    .line 116
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 117
    .line 118
    .line 119
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 7
    .line 8
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->amo:Lcom/kwad/components/core/j/a;

    .line 9
    .line 10
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 14
    .line 15
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 22
    .line 23
    .line 24
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 25
    .line 26
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alR:Ljava/util/List;

    .line 27
    .line 28
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a;->aof:Lcom/kwad/sdk/widget/swipe/a;

    .line 29
    .line 30
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a;->mRootView:Landroid/view/View;

    .line 34
    .line 35
    instance-of v1, v0, Lcom/kwad/sdk/core/view/AdBasePvFrameLayout;

    .line 36
    .line 37
    if-eqz v1, :cond_1

    .line 38
    .line 39
    check-cast v0, Lcom/kwad/sdk/core/view/AdBasePvFrameLayout;

    .line 40
    .line 41
    const/4 v1, 0x0

    .line 42
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/core/view/AdBasePvFrameLayout;->setVisibleListener(Lcom/kwad/sdk/widget/n;)V

    .line 43
    .line 44
    .line 45
    :cond_1
    return-void
.end method
