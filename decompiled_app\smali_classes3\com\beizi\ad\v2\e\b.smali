.class public Lcom/beizi/ad/v2/e/b;
.super Lcom/beizi/ad/v2/a/b;
.source "SourceFile"


# static fields
.field public static C:Lcom/beizi/ad/v2/e/b;


# instance fields
.field private D:Lcom/beizi/ad/j;

.field private E:Z

.field private F:Ljava/lang/String;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 1
    sget-object v0, Lcom/beizi/ad/internal/k;->f:Lcom/beizi/ad/internal/k;

    .line 2
    .line 3
    invoke-direct {p0, p1, v0}, Lcom/beizi/ad/v2/a/b;-><init>(Landroid/content/Context;Lcom/beizi/ad/internal/k;)V

    .line 4
    .line 5
    .line 6
    const/4 p1, 0x0

    .line 7
    iput-boolean p1, p0, Lcom/beizi/ad/v2/e/b;->E:Z

    .line 8
    .line 9
    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/e/b;)Lcom/beizi/ad/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    return-object p0
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/e/b;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/v2/e/b;->F:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/e/b;Z)Z
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/beizi/ad/v2/e/b;->E:Z

    return p1
.end method

.method public static synthetic b(Lcom/beizi/ad/v2/e/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/e/b;->y()V

    return-void
.end method

.method private y()V
    .locals 8

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->N()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 11
    .line 12
    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->L()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 17
    .line 18
    invoke-virtual {v3}, Lcom/beizi/ad/internal/f/c;->M()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v3
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 22
    goto :goto_0

    .line 23
    :catch_0
    move-exception v0

    .line 24
    goto :goto_1

    .line 25
    :cond_0
    const/4 v2, 0x0

    .line 26
    move v0, v1

    .line 27
    move-object v3, v2

    .line 28
    :goto_0
    const-string v4, "enter BeiZi ad load"

    .line 29
    .line 30
    const-string v5, "BeiZisAd"

    .line 31
    .line 32
    const/4 v6, 0x1

    .line 33
    const/4 v7, 0x3

    .line 34
    if-nez v0, :cond_2

    .line 35
    .line 36
    :try_start_1
    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    if-eqz v0, :cond_1

    .line 41
    .line 42
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 43
    .line 44
    if-eqz v0, :cond_5

    .line 45
    .line 46
    invoke-interface {v0, v7}, Lcom/beizi/ad/j;->a(I)V

    .line 47
    .line 48
    .line 49
    return-void

    .line 50
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 51
    .line 52
    if-eqz v0, :cond_5

    .line 53
    .line 54
    iput-boolean v6, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    .line 55
    .line 56
    invoke-static {v5, v4}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 57
    .line 58
    .line 59
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 60
    .line 61
    invoke-interface {v0}, Lcom/beizi/ad/j;->a()V

    .line 62
    .line 63
    .line 64
    return-void

    .line 65
    :cond_2
    invoke-static {v3}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 66
    .line 67
    .line 68
    move-result v0

    .line 69
    if-eqz v0, :cond_3

    .line 70
    .line 71
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 72
    .line 73
    if-eqz v0, :cond_5

    .line 74
    .line 75
    invoke-interface {v0, v7}, Lcom/beizi/ad/j;->a(I)V

    .line 76
    .line 77
    .line 78
    return-void

    .line 79
    :cond_3
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 80
    .line 81
    if-eqz v0, :cond_4

    .line 82
    .line 83
    invoke-static {v5, v4}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 84
    .line 85
    .line 86
    iput-boolean v6, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    .line 87
    .line 88
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 89
    .line 90
    invoke-interface {v0}, Lcom/beizi/ad/j;->a()V

    .line 91
    .line 92
    .line 93
    :cond_4
    invoke-static {}, Lcom/beizi/ad/internal/h/u;->a()Lcom/beizi/ad/internal/h/u;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->a:Landroid/content/Context;

    .line 98
    .line 99
    new-instance v4, Lcom/beizi/ad/v2/e/b$1;

    .line 100
    .line 101
    invoke-direct {v4, p0}, Lcom/beizi/ad/v2/e/b$1;-><init>(Lcom/beizi/ad/v2/e/b;)V

    .line 102
    .line 103
    .line 104
    invoke-virtual {v0, v2, v3, v1, v4}, Lcom/beizi/ad/internal/h/u;->a(Landroid/content/Context;Ljava/lang/String;ZLcom/beizi/ad/internal/h/u$a;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    .line 105
    .line 106
    .line 107
    return-void

    .line 108
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 109
    .line 110
    .line 111
    :cond_5
    return-void
.end method


# virtual methods
.method public a(Landroid/content/Context;)V
    .locals 2

    .line 5
    const-string v0, "BeiZisAd"

    const-string v1, "showRewardVideo"

    invoke-static {v0, v1}, Lcom/beizi/ad/lance/a/m;->c(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    sput-object p0, Lcom/beizi/ad/v2/e/b;->C:Lcom/beizi/ad/v2/e/b;

    .line 7
    new-instance v0, Landroid/content/Intent;

    const-class v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;

    invoke-direct {v0, p1, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    .line 8
    invoke-virtual {p1, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    return-void
.end method

.method public a(Lcom/beizi/ad/internal/f/c;)V
    .locals 1

    if-nez p1, :cond_0

    .line 11
    iget-object p1, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    if-eqz p1, :cond_1

    const/4 v0, 0x3

    .line 12
    invoke-interface {p1, v0}, Lcom/beizi/ad/j;->a(I)V

    return-void

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->e()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/a/b;->b(Ljava/lang/String;)V

    .line 14
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->f()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/a/b;->c(Ljava/lang/String;)V

    .line 15
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->P()Z

    move-result p1

    invoke-virtual {p0, p1}, Lcom/beizi/ad/v2/a/b;->a(Z)V

    .line 16
    iget-object p1, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    if-eqz p1, :cond_1

    .line 17
    new-instance v0, Lcom/beizi/ad/v2/e/b$3;

    invoke-direct {v0, p0}, Lcom/beizi/ad/v2/e/b$3;-><init>(Lcom/beizi/ad/v2/e/b;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    return-void
.end method

.method public a(Lcom/beizi/ad/j;)V
    .locals 0

    .line 4
    iput-object p1, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    return-void
.end method

.method public a(Ljava/lang/String;I)V
    .locals 0

    .line 9
    iget-object p1, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    if-eqz p1, :cond_0

    const/4 p2, 0x0

    .line 10
    invoke-interface {p1, p2}, Lcom/beizi/ad/j;->a(Ljava/util/Map;)V

    :cond_0
    return-void
.end method

.method public b(I)V
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    .line 3
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    .line 4
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    if-eqz v0, :cond_1

    .line 5
    new-instance v1, Lcom/beizi/ad/v2/e/b$2;

    invoke-direct {v1, p0, p1}, Lcom/beizi/ad/v2/e/b$2;-><init>(Lcom/beizi/ad/v2/e/b;I)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public f(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, Lcom/beizi/ad/j;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public q()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/beizi/ad/v2/a/b;->q()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    sput-object v0, Lcom/beizi/ad/v2/e/b;->C:Lcom/beizi/ad/v2/e/b;

    .line 6
    .line 7
    return-void
.end method

.method public t()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/e/b;->E:Z

    .line 2
    .line 3
    return v0
.end method

.method public u()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->F:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public v()V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->t:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x1

    .line 6
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->u:Z

    .line 7
    .line 8
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/a/c;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    const-string v0, "BeiZisAd"

    .line 22
    .line 23
    const-string v1, "enter BeiZi ad show"

    .line 24
    .line 25
    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 29
    .line 30
    invoke-interface {v0}, Lcom/beizi/ad/j;->c()V

    .line 31
    .line 32
    .line 33
    :cond_1
    return-void
.end method

.method public w()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lcom/beizi/ad/j;->d()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public x()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/e/b;->D:Lcom/beizi/ad/j;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lcom/beizi/ad/j;->e()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method
