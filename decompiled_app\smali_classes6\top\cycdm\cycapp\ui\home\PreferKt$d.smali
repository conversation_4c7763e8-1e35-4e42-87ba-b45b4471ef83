.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function3;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/grid/LazyGridScope;Ltop/cycdm/model/IndexVideoInner;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ltop/cycdm/model/IndexVideoInner;


# direct methods
.method public constructor <init>(Ltop/cycdm/model/IndexVideoInner;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/IndexVideoInner;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Ltop/cycdm/model/IndexVideoInner;Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/IndexVideoInner;Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Ltop/cycdm/model/IndexVideoInner;Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;)Lkotlin/t;
    .locals 6

    .line 1
    invoke-virtual {p0}, Ltop/cycdm/model/IndexVideoInner;->b()Ltop/cycdm/model/IndexVideoInner$ReqType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Ltop/cycdm/model/IndexVideoInner$ReqType;->Video:Ltop/cycdm/model/IndexVideoInner$ReqType;

    .line 6
    .line 7
    if-ne v0, v1, :cond_0

    .line 8
    .line 9
    new-instance p2, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 10
    .line 11
    invoke-virtual {p0}, Ltop/cycdm/model/IndexVideoInner;->d()I

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    invoke-direct {p2, p0}, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 16
    .line 17
    .line 18
    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/BaseVM;->postSideEffectNotSuspend(Ljava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 19
    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    sget-object p1, Ltop/cycdm/cycapp/Pager;->PreferVideo:Ltop/cycdm/cycapp/Pager;

    .line 23
    .line 24
    invoke-virtual {p0}, Ltop/cycdm/model/IndexVideoInner;->a()I

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    const-string v1, "id"

    .line 33
    .line 34
    invoke-static {v1, v0}, Lkotlin/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    const-string v1, "title"

    .line 39
    .line 40
    invoke-virtual {p0}, Ltop/cycdm/model/IndexVideoInner;->c()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-static {v1, p0}, Lkotlin/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    filled-new-array {v0, p0}, [Lkotlin/Pair;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    invoke-static {p1, p0}, Ltop/cycdm/cycapp/ui/common/x0;->b(Ltop/cycdm/cycapp/Pager;[Lkotlin/Pair;)Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    const/4 v4, 0x6

    .line 57
    const/4 v5, 0x0

    .line 58
    const/4 v2, 0x0

    .line 59
    const/4 v3, 0x0

    .line 60
    move-object v0, p2

    .line 61
    invoke-static/range {v0 .. v5}, Landroidx/navigation/NavController;->navigate$default(Landroidx/navigation/NavController;Ljava/lang/String;Landroidx/navigation/NavOptions;Landroidx/navigation/Navigator$Extras;ILjava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    :goto_0
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 65
    .line 66
    return-object p0
.end method


# virtual methods
.method public final b(Landroidx/compose/foundation/lazy/grid/LazyGridItemScope;Landroidx/compose/runtime/Composer;I)V
    .locals 60

    .line 1
    move-object/from16 v5, p2

    .line 2
    .line 3
    move/from16 v0, p3

    .line 4
    .line 5
    and-int/lit8 v1, v0, 0x11

    .line 6
    .line 7
    const/16 v2, 0x10

    .line 8
    .line 9
    if-ne v1, v2, :cond_1

    .line 10
    .line 11
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-nez v1, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 19
    .line 20
    .line 21
    return-void

    .line 22
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    if-eqz v1, :cond_2

    .line 27
    .line 28
    const/4 v1, -0x1

    .line 29
    const-string v2, "top.cycdm.cycapp.ui.home.indexVideoList.<anonymous> (Prefer.kt:347)"

    .line 30
    .line 31
    const v3, -0x3442d67c    # -2.4793864E7f

    .line 32
    .line 33
    .line 34
    invoke-static {v3, v0, v1, v2}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 35
    .line 36
    .line 37
    :cond_2
    const v0, 0x70b323c8

    .line 38
    .line 39
    .line 40
    invoke-interface {v5, v0}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 41
    .line 42
    .line 43
    sget-object v0, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->INSTANCE:Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;

    .line 44
    .line 45
    sget v1, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->$stable:I

    .line 46
    .line 47
    invoke-virtual {v0, v5, v1}, Landroidx/lifecycle/viewmodel/compose/LocalViewModelStoreOwner;->getCurrent(Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelStoreOwner;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    if-eqz v1, :cond_10

    .line 52
    .line 53
    const/4 v8, 0x0

    .line 54
    invoke-static {v1, v5, v8}, Landroidx/hilt/navigation/compose/HiltViewModelKt;->createHiltViewModelFactory(Landroidx/lifecycle/ViewModelStoreOwner;Landroidx/compose/runtime/Composer;I)Landroidx/lifecycle/ViewModelProvider$Factory;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    const v0, 0x671a9c9b

    .line 59
    .line 60
    .line 61
    invoke-interface {v5, v0}, Landroidx/compose/runtime/Composer;->startReplaceableGroup(I)V

    .line 62
    .line 63
    .line 64
    instance-of v0, v1, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 65
    .line 66
    if-eqz v0, :cond_3

    .line 67
    .line 68
    move-object v0, v1

    .line 69
    check-cast v0, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;

    .line 70
    .line 71
    invoke-interface {v0}, Landroidx/lifecycle/HasDefaultViewModelProviderFactory;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    :goto_1
    move-object v4, v0

    .line 76
    goto :goto_2

    .line 77
    :cond_3
    sget-object v0, Landroidx/lifecycle/viewmodel/CreationExtras$Empty;->INSTANCE:Landroidx/lifecycle/viewmodel/CreationExtras$Empty;

    .line 78
    .line 79
    goto :goto_1

    .line 80
    :goto_2
    const v6, 0x9048

    .line 81
    .line 82
    .line 83
    const/4 v7, 0x0

    .line 84
    const-class v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 85
    .line 86
    const/4 v2, 0x0

    .line 87
    invoke-static/range {v0 .. v7}, Landroidx/lifecycle/viewmodel/compose/ViewModelKt;->viewModel(Ljava/lang/Class;Landroidx/lifecycle/ViewModelStoreOwner;Ljava/lang/String;Landroidx/lifecycle/ViewModelProvider$Factory;Landroidx/lifecycle/viewmodel/CreationExtras;Landroidx/compose/runtime/Composer;II)Landroidx/lifecycle/ViewModel;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 92
    .line 93
    .line 94
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->endReplaceableGroup()V

    .line 95
    .line 96
    .line 97
    check-cast v0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 98
    .line 99
    invoke-static {}, Ltop/cycdm/cycapp/RouterKt;->i()Landroidx/compose/runtime/ProvidableCompositionLocal;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    invoke-interface {v5, v1}, Landroidx/compose/runtime/Composer;->consume(Landroidx/compose/runtime/CompositionLocal;)Ljava/lang/Object;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    check-cast v1, Landroidx/navigation/NavHostController;

    .line 108
    .line 109
    sget-object v9, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 110
    .line 111
    const/16 v2, 0x8

    .line 112
    .line 113
    int-to-float v2, v2

    .line 114
    invoke-static {v2}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 115
    .line 116
    .line 117
    move-result v13

    .line 118
    const/4 v14, 0x7

    .line 119
    const/4 v15, 0x0

    .line 120
    const/4 v10, 0x0

    .line 121
    const/4 v11, 0x0

    .line 122
    const/4 v12, 0x0

    .line 123
    invoke-static/range {v9 .. v15}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 124
    .line 125
    .line 126
    move-result-object v2

    .line 127
    move-object/from16 v3, p0

    .line 128
    .line 129
    iget-object v4, v3, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/IndexVideoInner;

    .line 130
    .line 131
    sget-object v6, Landroidx/compose/foundation/layout/Arrangement;->INSTANCE:Landroidx/compose/foundation/layout/Arrangement;

    .line 132
    .line 133
    invoke-virtual {v6}, Landroidx/compose/foundation/layout/Arrangement;->getTop()Landroidx/compose/foundation/layout/Arrangement$Vertical;

    .line 134
    .line 135
    .line 136
    move-result-object v7

    .line 137
    sget-object v10, Landroidx/compose/ui/Alignment;->Companion:Landroidx/compose/ui/Alignment$Companion;

    .line 138
    .line 139
    invoke-virtual {v10}, Landroidx/compose/ui/Alignment$Companion;->getStart()Landroidx/compose/ui/Alignment$Horizontal;

    .line 140
    .line 141
    .line 142
    move-result-object v11

    .line 143
    invoke-static {v7, v11, v5, v8}, Landroidx/compose/foundation/layout/ColumnKt;->columnMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Vertical;Landroidx/compose/ui/Alignment$Horizontal;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 144
    .line 145
    .line 146
    move-result-object v7

    .line 147
    invoke-static {v5, v8}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 148
    .line 149
    .line 150
    move-result v11

    .line 151
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 152
    .line 153
    .line 154
    move-result-object v12

    .line 155
    invoke-static {v5, v2}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 156
    .line 157
    .line 158
    move-result-object v2

    .line 159
    sget-object v13, Landroidx/compose/ui/node/ComposeUiNode;->Companion:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 160
    .line 161
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 162
    .line 163
    .line 164
    move-result-object v14

    .line 165
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 166
    .line 167
    .line 168
    move-result-object v15

    .line 169
    if-nez v15, :cond_4

    .line 170
    .line 171
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 172
    .line 173
    .line 174
    :cond_4
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 175
    .line 176
    .line 177
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 178
    .line 179
    .line 180
    move-result v15

    .line 181
    if-eqz v15, :cond_5

    .line 182
    .line 183
    invoke-interface {v5, v14}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 184
    .line 185
    .line 186
    goto :goto_3

    .line 187
    :cond_5
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 188
    .line 189
    .line 190
    :goto_3
    invoke-static {v5}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 191
    .line 192
    .line 193
    move-result-object v14

    .line 194
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 195
    .line 196
    .line 197
    move-result-object v15

    .line 198
    invoke-static {v14, v7, v15}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 199
    .line 200
    .line 201
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 202
    .line 203
    .line 204
    move-result-object v7

    .line 205
    invoke-static {v14, v12, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 206
    .line 207
    .line 208
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 209
    .line 210
    .line 211
    move-result-object v7

    .line 212
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 213
    .line 214
    .line 215
    move-result v12

    .line 216
    if-nez v12, :cond_6

    .line 217
    .line 218
    invoke-interface {v14}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 219
    .line 220
    .line 221
    move-result-object v12

    .line 222
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 223
    .line 224
    .line 225
    move-result-object v15

    .line 226
    invoke-static {v12, v15}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 227
    .line 228
    .line 229
    move-result v12

    .line 230
    if-nez v12, :cond_7

    .line 231
    .line 232
    :cond_6
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 233
    .line 234
    .line 235
    move-result-object v12

    .line 236
    invoke-interface {v14, v12}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 237
    .line 238
    .line 239
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 240
    .line 241
    .line 242
    move-result-object v11

    .line 243
    invoke-interface {v14, v11, v7}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 244
    .line 245
    .line 246
    :cond_7
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 247
    .line 248
    .line 249
    move-result-object v7

    .line 250
    invoke-static {v14, v2, v7}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 251
    .line 252
    .line 253
    sget-object v2, Landroidx/compose/foundation/layout/ColumnScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/ColumnScopeInstance;

    .line 254
    .line 255
    const/4 v2, 0x0

    .line 256
    const/4 v7, 0x1

    .line 257
    const/4 v11, 0x0

    .line 258
    invoke-static {v9, v2, v7, v11}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxWidth$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 259
    .line 260
    .line 261
    move-result-object v12

    .line 262
    const/16 v14, 0x1c

    .line 263
    .line 264
    int-to-float v14, v14

    .line 265
    invoke-static {v14}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 266
    .line 267
    .line 268
    move-result v14

    .line 269
    invoke-static {v12, v14}, Landroidx/compose/foundation/layout/SizeKt;->height-3ABfNKs(Landroidx/compose/ui/Modifier;F)Landroidx/compose/ui/Modifier;

    .line 270
    .line 271
    .line 272
    move-result-object v12

    .line 273
    invoke-virtual {v10}, Landroidx/compose/ui/Alignment$Companion;->getCenterVertically()Landroidx/compose/ui/Alignment$Vertical;

    .line 274
    .line 275
    .line 276
    move-result-object v10

    .line 277
    invoke-virtual {v6}, Landroidx/compose/foundation/layout/Arrangement;->getStart()Landroidx/compose/foundation/layout/Arrangement$Horizontal;

    .line 278
    .line 279
    .line 280
    move-result-object v6

    .line 281
    const/16 v14, 0x30

    .line 282
    .line 283
    invoke-static {v6, v10, v5, v14}, Landroidx/compose/foundation/layout/RowKt;->rowMeasurePolicy(Landroidx/compose/foundation/layout/Arrangement$Horizontal;Landroidx/compose/ui/Alignment$Vertical;Landroidx/compose/runtime/Composer;I)Landroidx/compose/ui/layout/MeasurePolicy;

    .line 284
    .line 285
    .line 286
    move-result-object v6

    .line 287
    invoke-static {v5, v8}, Landroidx/compose/runtime/ComposablesKt;->getCurrentCompositeKeyHash(Landroidx/compose/runtime/Composer;I)I

    .line 288
    .line 289
    .line 290
    move-result v10

    .line 291
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getCurrentCompositionLocalMap()Landroidx/compose/runtime/CompositionLocalMap;

    .line 292
    .line 293
    .line 294
    move-result-object v14

    .line 295
    invoke-static {v5, v12}, Landroidx/compose/ui/ComposedModifierKt;->materializeModifier(Landroidx/compose/runtime/Composer;Landroidx/compose/ui/Modifier;)Landroidx/compose/ui/Modifier;

    .line 296
    .line 297
    .line 298
    move-result-object v12

    .line 299
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getConstructor()Lkotlin/jvm/functions/Function0;

    .line 300
    .line 301
    .line 302
    move-result-object v15

    .line 303
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getApplier()Landroidx/compose/runtime/Applier;

    .line 304
    .line 305
    .line 306
    move-result-object v16

    .line 307
    if-nez v16, :cond_8

    .line 308
    .line 309
    invoke-static {}, Landroidx/compose/runtime/ComposablesKt;->invalidApplier()V

    .line 310
    .line 311
    .line 312
    :cond_8
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->startReusableNode()V

    .line 313
    .line 314
    .line 315
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 316
    .line 317
    .line 318
    move-result v16

    .line 319
    if-eqz v16, :cond_9

    .line 320
    .line 321
    invoke-interface {v5, v15}, Landroidx/compose/runtime/Composer;->createNode(Lkotlin/jvm/functions/Function0;)V

    .line 322
    .line 323
    .line 324
    goto :goto_4

    .line 325
    :cond_9
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->useNode()V

    .line 326
    .line 327
    .line 328
    :goto_4
    invoke-static {v5}, Landroidx/compose/runtime/Updater;->constructor-impl(Landroidx/compose/runtime/Composer;)Landroidx/compose/runtime/Composer;

    .line 329
    .line 330
    .line 331
    move-result-object v15

    .line 332
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetMeasurePolicy()Lkotlin/jvm/functions/Function2;

    .line 333
    .line 334
    .line 335
    move-result-object v8

    .line 336
    invoke-static {v15, v6, v8}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 337
    .line 338
    .line 339
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetResolvedCompositionLocals()Lkotlin/jvm/functions/Function2;

    .line 340
    .line 341
    .line 342
    move-result-object v6

    .line 343
    invoke-static {v15, v14, v6}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 344
    .line 345
    .line 346
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetCompositeKeyHash()Lkotlin/jvm/functions/Function2;

    .line 347
    .line 348
    .line 349
    move-result-object v6

    .line 350
    invoke-interface {v15}, Landroidx/compose/runtime/Composer;->getInserting()Z

    .line 351
    .line 352
    .line 353
    move-result v8

    .line 354
    if-nez v8, :cond_a

    .line 355
    .line 356
    invoke-interface {v15}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 357
    .line 358
    .line 359
    move-result-object v8

    .line 360
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 361
    .line 362
    .line 363
    move-result-object v14

    .line 364
    invoke-static {v8, v14}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 365
    .line 366
    .line 367
    move-result v8

    .line 368
    if-nez v8, :cond_b

    .line 369
    .line 370
    :cond_a
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 371
    .line 372
    .line 373
    move-result-object v8

    .line 374
    invoke-interface {v15, v8}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 375
    .line 376
    .line 377
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 378
    .line 379
    .line 380
    move-result-object v8

    .line 381
    invoke-interface {v15, v8, v6}, Landroidx/compose/runtime/Composer;->apply(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 382
    .line 383
    .line 384
    :cond_b
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->getSetModifier()Lkotlin/jvm/functions/Function2;

    .line 385
    .line 386
    .line 387
    move-result-object v6

    .line 388
    invoke-static {v15, v12, v6}, Landroidx/compose/runtime/Updater;->set-impl(Landroidx/compose/runtime/Composer;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 389
    .line 390
    .line 391
    sget-object v25, Landroidx/compose/foundation/layout/RowScopeInstance;->INSTANCE:Landroidx/compose/foundation/layout/RowScopeInstance;

    .line 392
    .line 393
    const/4 v6, 0x4

    .line 394
    int-to-float v6, v6

    .line 395
    invoke-static {v6}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 396
    .line 397
    .line 398
    move-result v8

    .line 399
    invoke-static {v9, v8}, Landroidx/compose/foundation/layout/SizeKt;->width-3ABfNKs(Landroidx/compose/ui/Modifier;F)Landroidx/compose/ui/Modifier;

    .line 400
    .line 401
    .line 402
    move-result-object v8

    .line 403
    invoke-static {v8, v2, v7, v11}, Landroidx/compose/foundation/layout/SizeKt;->fillMaxHeight$default(Landroidx/compose/ui/Modifier;FILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 404
    .line 405
    .line 406
    move-result-object v7

    .line 407
    const/4 v8, 0x0

    .line 408
    invoke-static {v5, v8}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 409
    .line 410
    .line 411
    move-result-object v10

    .line 412
    invoke-virtual {v10}, Lw7/a;->p()J

    .line 413
    .line 414
    .line 415
    move-result-wide v12

    .line 416
    invoke-static {}, Landroidx/compose/foundation/shape/RoundedCornerShapeKt;->getCircleShape()Landroidx/compose/foundation/shape/RoundedCornerShape;

    .line 417
    .line 418
    .line 419
    move-result-object v10

    .line 420
    invoke-static {v7, v12, v13, v10}, Landroidx/compose/foundation/BackgroundKt;->background-bw27NRU(Landroidx/compose/ui/Modifier;JLandroidx/compose/ui/graphics/Shape;)Landroidx/compose/ui/Modifier;

    .line 421
    .line 422
    .line 423
    move-result-object v7

    .line 424
    invoke-static {v7, v5, v8}, Landroidx/compose/foundation/layout/SpacerKt;->Spacer(Landroidx/compose/ui/Modifier;Landroidx/compose/runtime/Composer;I)V

    .line 425
    .line 426
    .line 427
    move-object v7, v0

    .line 428
    invoke-virtual {v4}, Ltop/cycdm/model/IndexVideoInner;->c()Ljava/lang/String;

    .line 429
    .line 430
    .line 431
    move-result-object v0

    .line 432
    new-instance v20, Landroidx/compose/ui/text/TextStyle;

    .line 433
    .line 434
    invoke-static {v5, v8}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 435
    .line 436
    .line 437
    move-result-object v10

    .line 438
    invoke-virtual {v10}, Lw7/a;->o()J

    .line 439
    .line 440
    .line 441
    move-result-wide v27

    .line 442
    const/16 v10, 0x12

    .line 443
    .line 444
    invoke-static {v10}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 445
    .line 446
    .line 447
    move-result-wide v29

    .line 448
    sget-object v10, Landroidx/compose/ui/text/font/FontWeight;->Companion:Landroidx/compose/ui/text/font/FontWeight$Companion;

    .line 449
    .line 450
    invoke-virtual {v10}, Landroidx/compose/ui/text/font/FontWeight$Companion;->getW400()Landroidx/compose/ui/text/font/FontWeight;

    .line 451
    .line 452
    .line 453
    move-result-object v31

    .line 454
    const v56, 0xfffff8

    .line 455
    .line 456
    .line 457
    const/16 v57, 0x0

    .line 458
    .line 459
    const/16 v32, 0x0

    .line 460
    .line 461
    const/16 v33, 0x0

    .line 462
    .line 463
    const/16 v34, 0x0

    .line 464
    .line 465
    const/16 v35, 0x0

    .line 466
    .line 467
    const-wide/16 v36, 0x0

    .line 468
    .line 469
    const/16 v38, 0x0

    .line 470
    .line 471
    const/16 v39, 0x0

    .line 472
    .line 473
    const/16 v40, 0x0

    .line 474
    .line 475
    const-wide/16 v41, 0x0

    .line 476
    .line 477
    const/16 v43, 0x0

    .line 478
    .line 479
    const/16 v44, 0x0

    .line 480
    .line 481
    const/16 v45, 0x0

    .line 482
    .line 483
    const/16 v46, 0x0

    .line 484
    .line 485
    const/16 v47, 0x0

    .line 486
    .line 487
    const-wide/16 v48, 0x0

    .line 488
    .line 489
    const/16 v50, 0x0

    .line 490
    .line 491
    const/16 v51, 0x0

    .line 492
    .line 493
    const/16 v52, 0x0

    .line 494
    .line 495
    const/16 v53, 0x0

    .line 496
    .line 497
    const/16 v54, 0x0

    .line 498
    .line 499
    const/16 v55, 0x0

    .line 500
    .line 501
    move-object/from16 v26, v20

    .line 502
    .line 503
    invoke-direct/range {v26 .. v57}, Landroidx/compose/ui/text/TextStyle;-><init>(JJLandroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontSynthesis;Landroidx/compose/ui/text/font/FontFamily;Ljava/lang/String;JLandroidx/compose/ui/text/style/BaselineShift;Landroidx/compose/ui/text/style/TextGeometricTransform;Landroidx/compose/ui/text/intl/LocaleList;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/graphics/Shadow;Landroidx/compose/ui/graphics/drawscope/DrawStyle;IIJLandroidx/compose/ui/text/style/TextIndent;Landroidx/compose/ui/text/PlatformTextStyle;Landroidx/compose/ui/text/style/LineHeightStyle;IILandroidx/compose/ui/text/style/TextMotion;ILkotlin/jvm/internal/n;)V

    .line 504
    .line 505
    .line 506
    const/4 v10, 0x5

    .line 507
    int-to-float v10, v10

    .line 508
    invoke-static {v10}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 509
    .line 510
    .line 511
    move-result v10

    .line 512
    const/16 v14, 0xe

    .line 513
    .line 514
    const/4 v15, 0x0

    .line 515
    move-object v12, v11

    .line 516
    const/4 v11, 0x0

    .line 517
    move-object v13, v12

    .line 518
    const/4 v12, 0x0

    .line 519
    move-object/from16 v16, v13

    .line 520
    .line 521
    const/4 v13, 0x0

    .line 522
    invoke-static/range {v9 .. v15}, Landroidx/compose/foundation/layout/PaddingKt;->padding-qDBjuR0$default(Landroidx/compose/ui/Modifier;FFFFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 523
    .line 524
    .line 525
    move-result-object v10

    .line 526
    move-object/from16 v26, v9

    .line 527
    .line 528
    const/16 v23, 0x0

    .line 529
    .line 530
    const v24, 0xfffc

    .line 531
    .line 532
    .line 533
    move v9, v2

    .line 534
    const-wide/16 v2, 0x0

    .line 535
    .line 536
    move-object v11, v4

    .line 537
    const-wide/16 v4, 0x0

    .line 538
    .line 539
    move v12, v6

    .line 540
    const/4 v6, 0x0

    .line 541
    move-object v13, v7

    .line 542
    const/4 v7, 0x0

    .line 543
    move v14, v8

    .line 544
    const/4 v8, 0x0

    .line 545
    move-object/from16 v16, v1

    .line 546
    .line 547
    move v15, v9

    .line 548
    move-object v1, v10

    .line 549
    const-wide/16 v9, 0x0

    .line 550
    .line 551
    move-object/from16 v17, v11

    .line 552
    .line 553
    const/4 v11, 0x0

    .line 554
    move/from16 v18, v12

    .line 555
    .line 556
    const/4 v12, 0x0

    .line 557
    move-object/from16 v21, v13

    .line 558
    .line 559
    move/from16 v19, v14

    .line 560
    .line 561
    const-wide/16 v13, 0x0

    .line 562
    .line 563
    move/from16 v22, v15

    .line 564
    .line 565
    const/4 v15, 0x0

    .line 566
    move-object/from16 v27, v16

    .line 567
    .line 568
    const/16 v16, 0x0

    .line 569
    .line 570
    move-object/from16 v28, v17

    .line 571
    .line 572
    const/16 v17, 0x0

    .line 573
    .line 574
    move/from16 v29, v18

    .line 575
    .line 576
    const/16 v18, 0x0

    .line 577
    .line 578
    move/from16 v30, v19

    .line 579
    .line 580
    const/16 v19, 0x0

    .line 581
    .line 582
    move/from16 v31, v22

    .line 583
    .line 584
    const/16 v22, 0x30

    .line 585
    .line 586
    move-object/from16 v58, v21

    .line 587
    .line 588
    move-object/from16 v59, v27

    .line 589
    .line 590
    move-object/from16 v21, p2

    .line 591
    .line 592
    invoke-static/range {v0 .. v24}, Landroidx/compose/material3/TextKt;->Text--4IGK_g(Ljava/lang/String;Landroidx/compose/ui/Modifier;JJLandroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontFamily;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/text/style/TextAlign;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/TextStyle;Landroidx/compose/runtime/Composer;III)V

    .line 593
    .line 594
    .line 595
    move-object/from16 v10, v21

    .line 596
    .line 597
    const/4 v6, 0x2

    .line 598
    const/high16 v4, 0x3f800000    # 1.0f

    .line 599
    .line 600
    const/4 v5, 0x0

    .line 601
    move-object/from16 v2, v25

    .line 602
    .line 603
    move-object/from16 v3, v26

    .line 604
    .line 605
    invoke-static/range {v2 .. v7}, Landroidx/compose/foundation/layout/RowScope;->weight$default(Landroidx/compose/foundation/layout/RowScope;Landroidx/compose/ui/Modifier;FZILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 606
    .line 607
    .line 608
    move-result-object v0

    .line 609
    move-object v9, v3

    .line 610
    const/4 v14, 0x0

    .line 611
    invoke-static {v0, v10, v14}, Landroidx/compose/foundation/layout/SpacerKt;->Spacer(Landroidx/compose/ui/Modifier;Landroidx/compose/runtime/Composer;I)V

    .line 612
    .line 613
    .line 614
    invoke-virtual/range {v28 .. v28}, Ltop/cycdm/model/IndexVideoInner;->b()Ltop/cycdm/model/IndexVideoInner$ReqType;

    .line 615
    .line 616
    .line 617
    move-result-object v0

    .line 618
    sget-object v1, Ltop/cycdm/model/IndexVideoInner$ReqType;->None:Ltop/cycdm/model/IndexVideoInner$ReqType;

    .line 619
    .line 620
    if-eq v0, v1, :cond_e

    .line 621
    .line 622
    const v0, -0x4205238a

    .line 623
    .line 624
    .line 625
    invoke-interface {v10, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 626
    .line 627
    .line 628
    const/16 v0, 0xc

    .line 629
    .line 630
    int-to-float v0, v0

    .line 631
    invoke-static {v0}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 632
    .line 633
    .line 634
    move-result v0

    .line 635
    const/4 v1, 0x2

    .line 636
    const/4 v12, 0x0

    .line 637
    const/4 v15, 0x0

    .line 638
    invoke-static {v9, v0, v15, v1, v12}, Landroidx/compose/foundation/layout/OffsetKt;->offset-VpY3zN4$default(Landroidx/compose/ui/Modifier;FFILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 639
    .line 640
    .line 641
    move-result-object v1

    .line 642
    invoke-static/range {v29 .. v29}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 643
    .line 644
    .line 645
    move-result v0

    .line 646
    invoke-static/range {v29 .. v29}, Landroidx/compose/ui/unit/Dp;->constructor-impl(F)F

    .line 647
    .line 648
    .line 649
    move-result v2

    .line 650
    invoke-static {v0, v2}, Landroidx/compose/foundation/layout/PaddingKt;->PaddingValues-YgX7TsA(FF)Landroidx/compose/foundation/layout/PaddingValues;

    .line 651
    .line 652
    .line 653
    move-result-object v7

    .line 654
    move-object/from16 v11, v28

    .line 655
    .line 656
    invoke-interface {v10, v11}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 657
    .line 658
    .line 659
    move-result v0

    .line 660
    move-object/from16 v13, v58

    .line 661
    .line 662
    invoke-interface {v10, v13}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 663
    .line 664
    .line 665
    move-result v2

    .line 666
    or-int/2addr v0, v2

    .line 667
    move-object/from16 v2, v59

    .line 668
    .line 669
    invoke-interface {v10, v2}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 670
    .line 671
    .line 672
    move-result v3

    .line 673
    or-int/2addr v0, v3

    .line 674
    invoke-interface {v10}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 675
    .line 676
    .line 677
    move-result-object v3

    .line 678
    if-nez v0, :cond_c

    .line 679
    .line 680
    sget-object v0, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 681
    .line 682
    invoke-virtual {v0}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 683
    .line 684
    .line 685
    move-result-object v0

    .line 686
    if-ne v3, v0, :cond_d

    .line 687
    .line 688
    :cond_c
    new-instance v3, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 689
    .line 690
    invoke-direct {v3, v11, v13, v2}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/IndexVideoInner;Ltop/cycdm/cycapp/ui/home/<USER>/navigation/NavHostController;)V

    .line 691
    .line 692
    .line 693
    invoke-interface {v10, v3}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 694
    .line 695
    .line 696
    :cond_d
    move-object v0, v3

    .line 697
    check-cast v0, Lkotlin/jvm/functions/Function0;

    .line 698
    .line 699
    sget-object v2, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    .line 700
    .line 701
    invoke-virtual {v2}, Ltop/cycdm/cycapp/ui/home/<USER>/jvm/functions/Function3;

    .line 702
    .line 703
    .line 704
    move-result-object v9

    .line 705
    const v11, 0x30c00030

    .line 706
    .line 707
    .line 708
    const/16 v12, 0x17c

    .line 709
    .line 710
    const/4 v2, 0x0

    .line 711
    const/4 v3, 0x0

    .line 712
    const/4 v4, 0x0

    .line 713
    const/4 v5, 0x0

    .line 714
    const/4 v6, 0x0

    .line 715
    const/4 v8, 0x0

    .line 716
    invoke-static/range {v0 .. v12}, Landroidx/compose/material3/ButtonKt;->TextButton(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/Modifier;ZLandroidx/compose/ui/graphics/Shape;Landroidx/compose/material3/ButtonColors;Landroidx/compose/material3/ButtonElevation;Landroidx/compose/foundation/BorderStroke;Landroidx/compose/foundation/layout/PaddingValues;Landroidx/compose/foundation/interaction/MutableInteractionSource;Lkotlin/jvm/functions/Function3;Landroidx/compose/runtime/Composer;II)V

    .line 717
    .line 718
    .line 719
    move-object v5, v10

    .line 720
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 721
    .line 722
    .line 723
    goto :goto_5

    .line 724
    :cond_e
    move-object v5, v10

    .line 725
    const v0, -0x41f40a7c

    .line 726
    .line 727
    .line 728
    invoke-interface {v5, v0}, Landroidx/compose/runtime/Composer;->startReplaceGroup(I)V

    .line 729
    .line 730
    .line 731
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->endReplaceGroup()V

    .line 732
    .line 733
    .line 734
    :goto_5
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 735
    .line 736
    .line 737
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->endNode()V

    .line 738
    .line 739
    .line 740
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 741
    .line 742
    .line 743
    move-result v0

    .line 744
    if-eqz v0, :cond_f

    .line 745
    .line 746
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 747
    .line 748
    .line 749
    :cond_f
    return-void

    .line 750
    :cond_10
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 751
    .line 752
    const-string v1, "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"

    .line 753
    .line 754
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 755
    .line 756
    .line 757
    throw v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/lazy/grid/LazyGridItemScope;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/Composer;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/lazy/grid/LazyGridItemScope;Landroidx/compose/runtime/Composer;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 15
    .line 16
    return-object p1
.end method
