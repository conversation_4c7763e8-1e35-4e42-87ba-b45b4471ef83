.class final Lcom/kwad/components/ct/detail/b/d$5;
.super Lcom/kwad/sdk/core/network/l;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/b/d;->BC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/kwad/sdk/core/network/l<",
        "Lcom/kwad/components/ct/request/r;",
        "Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic Ex:Lcom/kwad/components/core/request/model/ImpInfo;

.field final synthetic avR:Lcom/kwad/components/ct/detail/b/d;

.field final synthetic avS:J


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/d;Lcom/kwad/components/core/request/model/ImpInfo;J)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/d$5;->avR:Lcom/kwad/components/ct/detail/b/d;

    .line 2
    .line 3
    iput-object p2, p0, Lcom/kwad/components/ct/detail/b/d$5;->Ex:Lcom/kwad/components/core/request/model/ImpInfo;

    .line 4
    .line 5
    iput-wide p3, p0, Lcom/kwad/components/ct/detail/b/d$5;->avS:J

    .line 6
    .line 7
    invoke-direct {p0}, Lcom/kwad/sdk/core/network/l;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method private BF()Lcom/kwad/components/ct/request/r;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    new-instance v0, Lcom/kwad/components/ct/request/r;

    .line 2
    .line 3
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d$5;->Ex:Lcom/kwad/components/core/request/model/ImpInfo;

    .line 4
    .line 5
    iget-wide v2, p0, Lcom/kwad/components/ct/detail/b/d$5;->avS:J

    .line 6
    .line 7
    invoke-direct {v0, v1, v2, v3}, Lcom/kwad/components/ct/request/r;-><init>(Lcom/kwad/components/core/request/model/ImpInfo;J)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method private static bs(Ljava/lang/String;)Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/json/JSONObject;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    new-instance p0, Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;

    .line 7
    .line 8
    invoke-direct {p0}, Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;-><init>()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0, v0}, Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;->parseJson(Lorg/json/JSONObject;)V

    .line 12
    .line 13
    .line 14
    return-object p0
.end method


# virtual methods
.method public final synthetic createRequest()Lcom/kwad/sdk/core/network/f;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/d$5;->BF()Lcom/kwad/components/ct/request/r;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final synthetic parseData(Ljava/lang/String;)Lcom/kwad/sdk/core/response/model/BaseResultData;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/d$5;->bs(Ljava/lang/String;)Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method
