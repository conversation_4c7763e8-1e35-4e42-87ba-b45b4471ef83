.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;
.super Lcom/kwad/sdk/core/download/a/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/sdk/core/download/a/a;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onDownloadFailed()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1, v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;ZZ)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->c(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/widget/TextView;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 14
    .line 15
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->d(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/a;->aI(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final onDownloadFinished()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-static {v0, v1, v2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;ZZ)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 9
    .line 10
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->c(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/widget/TextView;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 15
    .line 16
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/a;->cB(Lcom/kwad/sdk/core/response/model/AdTemplate;)Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final onIdle()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1, v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;ZZ)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->c(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/widget/TextView;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 14
    .line 15
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->d(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/a;->aI(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final onInstalled()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-static {v0, v1, v2}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;ZZ)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 9
    .line 10
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->c(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/widget/TextView;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 15
    .line 16
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->d(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Lcom/kwad/sdk/core/response/model/AdInfo;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/a;->ae(Lcom/kwad/sdk/core/response/model/AdInfo;)Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final onPaused(I)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, Lcom/kwad/sdk/core/download/a/a;->onPaused(I)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    invoke-static {p1, v0, v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;ZZ)V

    .line 8
    .line 9
    .line 10
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 11
    .line 12
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->c(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/widget/TextView;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-static {}, Lcom/kwad/sdk/core/response/b/a;->aaW()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final onProgressUpdate(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-static {v0, v1, v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;ZZ)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$2;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->c(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Landroid/widget/TextView;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/a;->fd(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method
