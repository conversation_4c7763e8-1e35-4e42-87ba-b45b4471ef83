.class final Lcom/kwad/components/ct/detail/b/a$3;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avc:Lcom/kwad/components/ct/detail/b/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/a$3;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlaying()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlaying()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$3;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a$3;->avc:Lcom/kwad/components/ct/detail/b/a;

    .line 17
    .line 18
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/a;->e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const/16 v1, 0x8

    .line 23
    .line 24
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 25
    .line 26
    .line 27
    :cond_0
    return-void
.end method
