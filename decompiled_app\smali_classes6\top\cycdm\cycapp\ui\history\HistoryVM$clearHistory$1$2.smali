.class public final Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lorg/orbitmvi/orbit/syntax/simple/b;


# direct methods
.method public constructor <init>(Lorg/orbitmvi/orbit/syntax/simple/b;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;->a:Lorg/orbitmvi/orbit/syntax/simple/b;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5

    .line 1
    instance-of v0, p2, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;

    .line 7
    .line 8
    iget v1, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_4

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    goto :goto_2

    .line 57
    :cond_3
    invoke-static {p2}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    iget-object p2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;->a:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 61
    .line 62
    new-instance v2, Ltop/cycdm/cycapp/ui/history/a$d;

    .line 63
    .line 64
    if-eqz p1, :cond_4

    .line 65
    .line 66
    const-string p1, "\u6e05\u7a7a\u6210\u529f"

    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_4
    const-string p1, "\u6e05\u7a7a\u5931\u8d25"

    .line 70
    .line 71
    :goto_1
    invoke-direct {v2, p1}, Ltop/cycdm/cycapp/ui/history/a$d;-><init>(Ljava/lang/String;)V

    .line 72
    .line 73
    .line 74
    iput v4, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;->label:I

    .line 75
    .line 76
    invoke-static {p2, v2, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->d(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    if-ne p1, v1, :cond_5

    .line 81
    .line 82
    goto :goto_3

    .line 83
    :cond_5
    :goto_2
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;->a:Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 84
    .line 85
    sget-object p2, Ltop/cycdm/cycapp/ui/history/a$c;->a:Ltop/cycdm/cycapp/ui/history/a$c;

    .line 86
    .line 87
    iput v3, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2$emit$1;->label:I

    .line 88
    .line 89
    invoke-static {p1, p2, v0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->d(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    if-ne p1, v1, :cond_6

    .line 94
    .line 95
    :goto_3
    return-object v1

    .line 96
    :cond_6
    :goto_4
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 97
    .line 98
    return-object p1
.end method

.method public bridge synthetic emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;->a(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method
