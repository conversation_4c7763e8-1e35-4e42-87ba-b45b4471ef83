.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/layout/RowScope;Landroidx/compose/foundation/pager/PagerState;Ljava/util/List;Landroidx/compose/runtime/Composer;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ljava/util/List;

.field public final synthetic b:Lkotlinx/coroutines/o0;

.field public final synthetic c:Landroidx/compose/foundation/pager/PagerState;


# direct methods
.method public constructor <init>(Ljava/util/List;Lkotlinx/coroutines/o0;Landroidx/compose/foundation/pager/PagerState;)V
    .locals 0

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/pager/PagerState;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lkotlinx/coroutines/o0;Landroidx/compose/foundation/pager/PagerState;I)Lkotlin/t;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;Landroidx/compose/foundation/pager/PagerState;I)Lkotlin/t;

    move-result-object p0

    return-object p0
.end method

.method private static final c(Lkotlinx/coroutines/o0;Landroidx/compose/foundation/pager/PagerState;I)Lkotlin/t;
    .locals 6

    .line 1
    new-instance v3, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    invoke-direct {v3, p1, p2, v0}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/pager/PagerState;ILkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    const/4 v4, 0x3

    .line 8
    const/4 v5, 0x0

    .line 9
    const/4 v1, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    move-object v0, p0

    .line 12
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/o0;Lkotlin/coroutines/i;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/w1;

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/t;->a:Lkotlin/t;

    .line 16
    .line 17
    return-object p0
.end method


# virtual methods
.method public final b(Landroidx/compose/runtime/Composer;I)V
    .locals 27

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move/from16 v2, p2

    .line 6
    .line 7
    and-int/lit8 v3, v2, 0x3

    .line 8
    .line 9
    const/4 v4, 0x2

    .line 10
    if-ne v3, v4, :cond_1

    .line 11
    .line 12
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 13
    .line 14
    .line 15
    move-result v3

    .line 16
    if-nez v3, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    if-eqz v3, :cond_2

    .line 28
    .line 29
    const/4 v3, -0x1

    .line 30
    const-string v4, "top.cycdm.cycapp.ui.home.Tabs.<anonymous> (Home.kt:252)"

    .line 31
    .line 32
    const v5, -0x4a1a46e8

    .line 33
    .line 34
    .line 35
    invoke-static {v5, v2, v3, v4}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 36
    .line 37
    .line 38
    :cond_2
    iget-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/util/List;

    .line 39
    .line 40
    iget-object v3, v0, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;

    .line 41
    .line 42
    move-object v4, v2

    .line 43
    iget-object v2, v0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/pager/PagerState;

    .line 44
    .line 45
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 46
    .line 47
    .line 48
    move-result-object v21

    .line 49
    const/4 v4, 0x0

    .line 50
    move v5, v4

    .line 51
    :goto_1
    invoke-interface/range {v21 .. v21}, Ljava/util/Iterator;->hasNext()Z

    .line 52
    .line 53
    .line 54
    move-result v6

    .line 55
    if-eqz v6, :cond_7

    .line 56
    .line 57
    invoke-interface/range {v21 .. v21}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v6

    .line 61
    add-int/lit8 v22, v5, 0x1

    .line 62
    .line 63
    if-gez v5, :cond_3

    .line 64
    .line 65
    invoke-static {}, Lkotlin/collections/w;->x()V

    .line 66
    .line 67
    .line 68
    :cond_3
    check-cast v6, Ljava/lang/String;

    .line 69
    .line 70
    sget-object v7, Landroidx/compose/ui/Modifier;->Companion:Landroidx/compose/ui/Modifier$Companion;

    .line 71
    .line 72
    const/4 v8, 0x0

    .line 73
    const/4 v9, 0x3

    .line 74
    invoke-static {v7, v8, v4, v9, v8}, Landroidx/compose/foundation/layout/SizeKt;->wrapContentWidth$default(Landroidx/compose/ui/Modifier;Landroidx/compose/ui/Alignment$Horizontal;ZILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 75
    .line 76
    .line 77
    move-result-object v10

    .line 78
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object v7

    .line 82
    sget-object v8, Landroidx/compose/runtime/Composer;->Companion:Landroidx/compose/runtime/Composer$Companion;

    .line 83
    .line 84
    invoke-virtual {v8}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    move-result-object v9

    .line 88
    if-ne v7, v9, :cond_4

    .line 89
    .line 90
    invoke-static {}, Landroidx/compose/foundation/interaction/InteractionSourceKt;->MutableInteractionSource()Landroidx/compose/foundation/interaction/MutableInteractionSource;

    .line 91
    .line 92
    .line 93
    move-result-object v7

    .line 94
    invoke-interface {v1, v7}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 95
    .line 96
    .line 97
    :cond_4
    move-object v11, v7

    .line 98
    check-cast v11, Landroidx/compose/foundation/interaction/MutableInteractionSource;

    .line 99
    .line 100
    invoke-interface {v1, v3}, Landroidx/compose/runtime/Composer;->changedInstance(Ljava/lang/Object;)Z

    .line 101
    .line 102
    .line 103
    move-result v7

    .line 104
    invoke-interface {v1, v2}, Landroidx/compose/runtime/Composer;->changed(Ljava/lang/Object;)Z

    .line 105
    .line 106
    .line 107
    move-result v9

    .line 108
    or-int/2addr v7, v9

    .line 109
    invoke-interface {v1, v5}, Landroidx/compose/runtime/Composer;->changed(I)Z

    .line 110
    .line 111
    .line 112
    move-result v9

    .line 113
    or-int/2addr v7, v9

    .line 114
    invoke-interface {v1}, Landroidx/compose/runtime/Composer;->rememberedValue()Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object v9

    .line 118
    if-nez v7, :cond_5

    .line 119
    .line 120
    invoke-virtual {v8}, Landroidx/compose/runtime/Composer$Companion;->getEmpty()Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    move-result-object v7

    .line 124
    if-ne v9, v7, :cond_6

    .line 125
    .line 126
    :cond_5
    new-instance v9, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 127
    .line 128
    invoke-direct {v9, v3, v2, v5}, Ltop/cycdm/cycapp/ui/home/<USER>/coroutines/o0;Landroidx/compose/foundation/pager/PagerState;I)V

    .line 129
    .line 130
    .line 131
    invoke-interface {v1, v9}, Landroidx/compose/runtime/Composer;->updateRememberedValue(Ljava/lang/Object;)V

    .line 132
    .line 133
    .line 134
    :cond_6
    move-object/from16 v16, v9

    .line 135
    .line 136
    check-cast v16, Lkotlin/jvm/functions/Function0;

    .line 137
    .line 138
    const/16 v17, 0x1c

    .line 139
    .line 140
    const/16 v18, 0x0

    .line 141
    .line 142
    const/4 v12, 0x0

    .line 143
    const/4 v13, 0x0

    .line 144
    const/4 v14, 0x0

    .line 145
    const/4 v15, 0x0

    .line 146
    invoke-static/range {v10 .. v18}, Landroidx/compose/foundation/ClickableKt;->clickable-O2vRcR0$default(Landroidx/compose/ui/Modifier;Landroidx/compose/foundation/interaction/MutableInteractionSource;Landroidx/compose/foundation/Indication;ZLjava/lang/String;Landroidx/compose/ui/semantics/Role;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/Modifier;

    .line 147
    .line 148
    .line 149
    move-result-object v7

    .line 150
    invoke-virtual {v2}, Landroidx/compose/foundation/pager/PagerState;->getPageCount()I

    .line 151
    .line 152
    .line 153
    move-result v8

    .line 154
    const/16 v9, 0x12

    .line 155
    .line 156
    invoke-static {v9}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 157
    .line 158
    .line 159
    move-result-wide v12

    .line 160
    const/16 v9, 0x14

    .line 161
    .line 162
    invoke-static {v9}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 163
    .line 164
    .line 165
    move-result-wide v10

    .line 166
    sget-object v9, Landroidx/compose/ui/text/font/FontWeight;->Companion:Landroidx/compose/ui/text/font/FontWeight$Companion;

    .line 167
    .line 168
    invoke-virtual {v9}, Landroidx/compose/ui/text/font/FontWeight$Companion;->getMedium()Landroidx/compose/ui/text/font/FontWeight;

    .line 169
    .line 170
    .line 171
    move-result-object v15

    .line 172
    invoke-virtual {v9}, Landroidx/compose/ui/text/font/FontWeight$Companion;->getMedium()Landroidx/compose/ui/text/font/FontWeight;

    .line 173
    .line 174
    .line 175
    move-result-object v14

    .line 176
    invoke-static {v1, v4}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 177
    .line 178
    .line 179
    move-result-object v9

    .line 180
    invoke-virtual {v9}, Lw7/a;->h()J

    .line 181
    .line 182
    .line 183
    move-result-wide v16

    .line 184
    invoke-static {v1, v4}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 185
    .line 186
    .line 187
    move-result-object v9

    .line 188
    invoke-virtual {v9}, Lw7/a;->o()J

    .line 189
    .line 190
    .line 191
    move-result-wide v18

    .line 192
    move-object v9, v3

    .line 193
    move v3, v5

    .line 194
    move-object v5, v6

    .line 195
    move-object v1, v7

    .line 196
    move-wide/from16 v6, v18

    .line 197
    .line 198
    const/16 v19, 0x6

    .line 199
    .line 200
    const/16 v20, 0x800

    .line 201
    .line 202
    move/from16 v18, v4

    .line 203
    .line 204
    move v4, v8

    .line 205
    move-wide/from16 v25, v16

    .line 206
    .line 207
    move-object/from16 v17, v9

    .line 208
    .line 209
    move-wide/from16 v8, v25

    .line 210
    .line 211
    const/16 v16, 0x0

    .line 212
    .line 213
    move/from16 v23, v18

    .line 214
    .line 215
    const/high16 v18, 0x36c00000

    .line 216
    .line 217
    move/from16 v24, v23

    .line 218
    .line 219
    move-object/from16 v23, v17

    .line 220
    .line 221
    move-object/from16 v17, p1

    .line 222
    .line 223
    invoke-static/range {v1 .. v20}, Ltop/cycdm/cycapp/ui/common/c1;->e(Landroidx/compose/ui/Modifier;Landroidx/compose/foundation/pager/PagerState;IILjava/lang/String;JJJJLandroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontWeight;Lkotlin/jvm/functions/Function3;Landroidx/compose/runtime/Composer;III)V

    .line 224
    .line 225
    .line 226
    move-object/from16 v1, p1

    .line 227
    .line 228
    move/from16 v5, v22

    .line 229
    .line 230
    move-object/from16 v3, v23

    .line 231
    .line 232
    move/from16 v4, v24

    .line 233
    .line 234
    goto/16 :goto_1

    .line 235
    .line 236
    :cond_7
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 237
    .line 238
    .line 239
    move-result v1

    .line 240
    if-eqz v1, :cond_8

    .line 241
    .line 242
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 243
    .line 244
    .line 245
    :cond_8
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/runtime/Composer;

    .line 2
    .line 3
    check-cast p2, Ljava/lang/Number;

    .line 4
    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/Composer;I)V

    .line 10
    .line 11
    .line 12
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 13
    .line 14
    return-object p1
.end method
