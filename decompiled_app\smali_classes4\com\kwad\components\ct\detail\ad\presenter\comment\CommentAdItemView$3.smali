.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;
.super Lcom/kwad/sdk/utils/bh;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->s(Ljava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

.field final synthetic anj:Ljava/lang/String;

.field final synthetic ank:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 2
    .line 3
    iput-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->anj:Ljava/lang/String;

    .line 4
    .line 5
    iput-object p3, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ank:Ljava/lang/String;

    .line 6
    .line 7
    invoke-direct {p0}, Lcom/kwad/sdk/utils/bh;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final doTask()V
    .locals 10

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Landroid/widget/TextView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->anj:Ljava/lang/String;

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->anj:Ljava/lang/String;

    .line 13
    .line 14
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 15
    .line 16
    invoke-virtual {v1}, Landroid/view/View;->requestLayout()V

    .line 17
    .line 18
    .line 19
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 20
    .line 21
    invoke-static {v1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Landroid/widget/TextView;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1}, Landroid/widget/TextView;->getLayout()Landroid/text/Layout;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    const/4 v2, 0x0

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    invoke-virtual {v1}, Landroid/text/Layout;->getLineCount()I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    goto :goto_0

    .line 37
    :cond_0
    move v3, v2

    .line 38
    :goto_0
    const/4 v4, 0x4

    .line 39
    if-lt v3, v4, :cond_2

    .line 40
    .line 41
    iget-object v3, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 42
    .line 43
    invoke-static {v3}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Landroid/widget/TextView;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    invoke-virtual {v3}, Landroid/widget/TextView;->getText()Ljava/lang/CharSequence;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    invoke-interface {v3}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v3

    .line 55
    const-string v5, ""

    .line 56
    .line 57
    move v6, v2

    .line 58
    :goto_1
    if-ge v6, v4, :cond_1

    .line 59
    .line 60
    invoke-virtual {v1, v6}, Landroid/text/Layout;->getLineStart(I)I

    .line 61
    .line 62
    .line 63
    move-result v7

    .line 64
    invoke-virtual {v1, v6}, Landroid/text/Layout;->getLineEnd(I)I

    .line 65
    .line 66
    .line 67
    move-result v8

    .line 68
    new-instance v9, Ljava/lang/StringBuilder;

    .line 69
    .line 70
    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    .line 71
    .line 72
    .line 73
    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v3, v7, v8}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v5

    .line 80
    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 81
    .line 82
    .line 83
    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v5

    .line 87
    add-int/lit8 v6, v6, 0x1

    .line 88
    .line 89
    goto :goto_1

    .line 90
    :cond_1
    invoke-virtual {v5}, Ljava/lang/String;->length()I

    .line 91
    .line 92
    .line 93
    move-result v1

    .line 94
    iget-object v3, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ank:Ljava/lang/String;

    .line 95
    .line 96
    invoke-virtual {v3}, Ljava/lang/String;->length()I

    .line 97
    .line 98
    .line 99
    move-result v3

    .line 100
    sub-int/2addr v1, v3

    .line 101
    add-int/lit8 v1, v1, -0x6

    .line 102
    .line 103
    if-lez v1, :cond_2

    .line 104
    .line 105
    new-instance v0, Ljava/lang/StringBuilder;

    .line 106
    .line 107
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 108
    .line 109
    .line 110
    invoke-virtual {v5, v2, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 115
    .line 116
    .line 117
    const-string v1, "..."

    .line 118
    .line 119
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 120
    .line 121
    .line 122
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    :cond_2
    new-instance v1, Landroid/text/SpannableStringBuilder;

    .line 127
    .line 128
    invoke-direct {v1}, Landroid/text/SpannableStringBuilder;-><init>()V

    .line 129
    .line 130
    .line 131
    invoke-virtual {v1}, Landroid/text/SpannableStringBuilder;->length()I

    .line 132
    .line 133
    .line 134
    move-result v2

    .line 135
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 136
    .line 137
    .line 138
    move-result v3

    .line 139
    add-int/2addr v3, v2

    .line 140
    invoke-virtual {v1, v0}, Landroid/text/SpannableStringBuilder;->append(Ljava/lang/CharSequence;)Landroid/text/SpannableStringBuilder;

    .line 141
    .line 142
    .line 143
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;

    .line 144
    .line 145
    new-instance v4, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3$1;

    .line 146
    .line 147
    invoke-direct {v4, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;)V

    .line 148
    .line 149
    .line 150
    iget-object v5, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 151
    .line 152
    invoke-static {v5}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 153
    .line 154
    .line 155
    move-result-object v5

    .line 156
    iget-object v5, v5, Lcom/kwad/components/ct/detail/photo/comment/h;->apL:Ljava/lang/String;

    .line 157
    .line 158
    invoke-static {v5}, Lcom/kwad/components/ct/f/g;->parseColor(Ljava/lang/String;)I

    .line 159
    .line 160
    .line 161
    move-result v5

    .line 162
    invoke-direct {v0, v4, v5}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;-><init>(Landroid/view/View$OnClickListener;I)V

    .line 163
    .line 164
    .line 165
    const/16 v4, 0x21

    .line 166
    .line 167
    invoke-virtual {v1, v0, v2, v3, v4}, Landroid/text/SpannableStringBuilder;->setSpan(Ljava/lang/Object;III)V

    .line 168
    .line 169
    .line 170
    const-string v0, " "

    .line 171
    .line 172
    invoke-virtual {v1, v0}, Landroid/text/SpannableStringBuilder;->append(Ljava/lang/CharSequence;)Landroid/text/SpannableStringBuilder;

    .line 173
    .line 174
    .line 175
    invoke-virtual {v1}, Landroid/text/SpannableStringBuilder;->length()I

    .line 176
    .line 177
    .line 178
    move-result v0

    .line 179
    const-string v2, "${ad}"

    .line 180
    .line 181
    invoke-virtual {v1, v2}, Landroid/text/SpannableStringBuilder;->append(Ljava/lang/CharSequence;)Landroid/text/SpannableStringBuilder;

    .line 182
    .line 183
    .line 184
    iget-object v3, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 185
    .line 186
    invoke-virtual {v3}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 187
    .line 188
    .line 189
    move-result-object v3

    .line 190
    iget-object v5, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 191
    .line 192
    invoke-static {v5}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 193
    .line 194
    .line 195
    move-result-object v5

    .line 196
    iget v5, v5, Lcom/kwad/components/ct/detail/photo/comment/h;->apQ:I

    .line 197
    .line 198
    invoke-static {v3, v5}, Lcom/kwad/components/ct/f/g;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 199
    .line 200
    .line 201
    move-result-object v3

    .line 202
    new-instance v5, Lcom/kwad/sdk/core/view/b;

    .line 203
    .line 204
    invoke-direct {v5, v3, v2}, Lcom/kwad/sdk/core/view/b;-><init>(Landroid/graphics/drawable/Drawable;Ljava/lang/String;)V

    .line 205
    .line 206
    .line 207
    invoke-virtual {v3}, Landroid/graphics/drawable/Drawable;->getIntrinsicWidth()I

    .line 208
    .line 209
    .line 210
    move-result v2

    .line 211
    invoke-virtual {v3}, Landroid/graphics/drawable/Drawable;->getIntrinsicHeight()I

    .line 212
    .line 213
    .line 214
    move-result v3

    .line 215
    invoke-virtual {v5, v2, v3}, Lcom/kwad/sdk/core/view/b;->F(II)Lcom/kwad/sdk/core/view/b;

    .line 216
    .line 217
    .line 218
    move-result-object v2

    .line 219
    add-int/lit8 v3, v0, 0x5

    .line 220
    .line 221
    invoke-virtual {v1, v2, v0, v3, v4}, Landroid/text/SpannableStringBuilder;->setSpan(Ljava/lang/Object;III)V

    .line 222
    .line 223
    .line 224
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ank:Ljava/lang/String;

    .line 225
    .line 226
    invoke-virtual {v1, v0}, Landroid/text/SpannableStringBuilder;->append(Ljava/lang/CharSequence;)Landroid/text/SpannableStringBuilder;

    .line 227
    .line 228
    .line 229
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;

    .line 230
    .line 231
    new-instance v2, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3$2;

    .line 232
    .line 233
    invoke-direct {v2, p0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;)V

    .line 234
    .line 235
    .line 236
    iget-object v5, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 237
    .line 238
    invoke-static {v5}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Lcom/kwad/components/ct/detail/photo/comment/h;

    .line 239
    .line 240
    .line 241
    move-result-object v5

    .line 242
    iget-object v5, v5, Lcom/kwad/components/ct/detail/photo/comment/h;->apR:Ljava/lang/String;

    .line 243
    .line 244
    invoke-static {v5}, Lcom/kwad/components/ct/f/g;->parseColor(Ljava/lang/String;)I

    .line 245
    .line 246
    .line 247
    move-result v5

    .line 248
    invoke-direct {v0, v2, v5}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;-><init>(Landroid/view/View$OnClickListener;I)V

    .line 249
    .line 250
    .line 251
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ank:Ljava/lang/String;

    .line 252
    .line 253
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    .line 254
    .line 255
    .line 256
    move-result v2

    .line 257
    add-int/2addr v2, v3

    .line 258
    invoke-virtual {v1, v0, v3, v2, v4}, Landroid/text/SpannableStringBuilder;->setSpan(Ljava/lang/Object;III)V

    .line 259
    .line 260
    .line 261
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 262
    .line 263
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Landroid/widget/TextView;

    .line 264
    .line 265
    .line 266
    move-result-object v0

    .line 267
    invoke-static {}, Landroid/text/method/LinkMovementMethod;->getInstance()Landroid/text/method/MovementMethod;

    .line 268
    .line 269
    .line 270
    move-result-object v2

    .line 271
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setMovementMethod(Landroid/text/method/MovementMethod;)V

    .line 272
    .line 273
    .line 274
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$3;->ani:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;

    .line 275
    .line 276
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;)Landroid/widget/TextView;

    .line 277
    .line 278
    .line 279
    move-result-object v0

    .line 280
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 281
    .line 282
    .line 283
    return-void
.end method
