.class final Lcom/kwad/components/ct/detail/b/c/a$5;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awE:Lcom/kwad/components/ct/detail/b/c/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayCompleted()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->zZ()V

    .line 11
    .line 12
    .line 13
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    new-instance v0, Ljava/lang/StringBuilder;

    .line 20
    .line 21
    const-string v1, "position: "

    .line 22
    .line 23
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 27
    .line 28
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 33
    .line 34
    .line 35
    const-string v1, " onVideoPlayCompleted playDuration: "

    .line 36
    .line 37
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 38
    .line 39
    .line 40
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 41
    .line 42
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 47
    .line 48
    .line 49
    move-result-wide v1

    .line 50
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 51
    .line 52
    .line 53
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    const-string v1, "DetailLogPagePresenter"

    .line 58
    .line 59
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 63
    .line 64
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->m(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 65
    .line 66
    .line 67
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 68
    .line 69
    const-wide/16 v1, 0x0

    .line 70
    .line 71
    invoke-static {v0, v1, v2}, Lcom/kwad/components/ct/detail/b/c/a;->a(Lcom/kwad/components/ct/detail/b/c/a;J)J

    .line 72
    .line 73
    .line 74
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 75
    .line 76
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->l(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/video/l;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    invoke-virtual {v0}, Lcom/kwad/components/core/video/l;->tU()V

    .line 81
    .line 82
    .line 83
    return-void
.end method

.method public final onMediaPlayError(II)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2}, Lcom/kwad/components/core/video/o;->onMediaPlayError(II)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-virtual {p1}, Lcom/kwad/sdk/utils/bw;->zZ()V

    .line 11
    .line 12
    .line 13
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 14
    .line 15
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/c/a;->l(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/video/l;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Lcom/kwad/components/core/video/l;->tU()V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final onMediaPlayPaused()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayPaused()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->zZ()V

    .line 11
    .line 12
    .line 13
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    new-instance v0, Ljava/lang/StringBuilder;

    .line 20
    .line 21
    const-string v1, "position: "

    .line 22
    .line 23
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 27
    .line 28
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 33
    .line 34
    .line 35
    const-string v1, " onVideoPlayPaused playDuration: "

    .line 36
    .line 37
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 38
    .line 39
    .line 40
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 41
    .line 42
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 47
    .line 48
    .line 49
    move-result-wide v1

    .line 50
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 51
    .line 52
    .line 53
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    const-string v1, "DetailLogPagePresenter"

    .line 58
    .line 59
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 63
    .line 64
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->l(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/video/l;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    invoke-virtual {v0}, Lcom/kwad/components/core/video/l;->tU()V

    .line 69
    .line 70
    .line 71
    return-void
.end method

.method public final onMediaPlayProgress(JJ)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2, p3, p4}, Lcom/kwad/components/core/video/o;->onMediaPlayProgress(JJ)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {p1, p3, p4}, Lcom/kwad/components/ct/detail/b/c/a;->a(Lcom/kwad/components/ct/detail/b/c/a;J)J

    .line 7
    .line 8
    .line 9
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 10
    .line 11
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/c/a;->k(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final onMediaPlayStart()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayStart()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->Aa()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 17
    .line 18
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->zY()V

    .line 23
    .line 24
    .line 25
    :cond_0
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    if-eqz v0, :cond_1

    .line 30
    .line 31
    new-instance v0, Ljava/lang/StringBuilder;

    .line 32
    .line 33
    const-string v1, "position: "

    .line 34
    .line 35
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 39
    .line 40
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 45
    .line 46
    .line 47
    const-string v1, " onVideoPlayStart resumeTiming playDuration: "

    .line 48
    .line 49
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 50
    .line 51
    .line 52
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 53
    .line 54
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 59
    .line 60
    .line 61
    move-result-wide v1

    .line 62
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 63
    .line 64
    .line 65
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    const-string v1, "DetailLogPagePresenter"

    .line 70
    .line 71
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 72
    .line 73
    .line 74
    :cond_1
    return-void
.end method

.method public final onMediaPlaying()V
    .locals 5

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlaying()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->Aa()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    const-string v1, "position: "

    .line 15
    .line 16
    const-string v2, "DetailLogPagePresenter"

    .line 17
    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 21
    .line 22
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->zY()V

    .line 27
    .line 28
    .line 29
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-eqz v0, :cond_1

    .line 34
    .line 35
    new-instance v0, Ljava/lang/StringBuilder;

    .line 36
    .line 37
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 41
    .line 42
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 47
    .line 48
    .line 49
    const-string v1, " onVideoPlaying resumeTiming playDuration: "

    .line 50
    .line 51
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 52
    .line 53
    .line 54
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 55
    .line 56
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 61
    .line 62
    .line 63
    move-result-wide v3

    .line 64
    invoke-virtual {v0, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 65
    .line 66
    .line 67
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    invoke-static {v2, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 72
    .line 73
    .line 74
    goto :goto_0

    .line 75
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 76
    .line 77
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->startTiming()V

    .line 82
    .line 83
    .line 84
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 85
    .line 86
    .line 87
    move-result v0

    .line 88
    if-eqz v0, :cond_1

    .line 89
    .line 90
    new-instance v0, Ljava/lang/StringBuilder;

    .line 91
    .line 92
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 93
    .line 94
    .line 95
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 96
    .line 97
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 98
    .line 99
    .line 100
    move-result v1

    .line 101
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 102
    .line 103
    .line 104
    const-string v1, " onVideoPlaying startTiming playDuration: "

    .line 105
    .line 106
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 107
    .line 108
    .line 109
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 110
    .line 111
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 116
    .line 117
    .line 118
    move-result-wide v3

    .line 119
    invoke-virtual {v0, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 120
    .line 121
    .line 122
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    invoke-static {v2, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 127
    .line 128
    .line 129
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 130
    .line 131
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->l(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/video/l;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    invoke-virtual {v0}, Lcom/kwad/components/core/video/l;->tU()V

    .line 136
    .line 137
    .line 138
    return-void
.end method

.method public final onVideoPlayBufferingPaused()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onVideoPlayBufferingPaused()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->l(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/video/l;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0}, Lcom/kwad/components/core/video/l;->tT()V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final onVideoPlayBufferingPlaying()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onVideoPlayBufferingPlaying()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$5;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->l(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/video/l;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0}, Lcom/kwad/components/core/video/l;->tT()V

    .line 11
    .line 12
    .line 13
    return-void
.end method
