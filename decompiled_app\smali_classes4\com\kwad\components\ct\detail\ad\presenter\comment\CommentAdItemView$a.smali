.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;
.super Landroid/text/style/ClickableSpan;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# instance fields
.field private anm:Landroid/view/View$OnClickListener;

.field private color:I


# direct methods
.method public constructor <init>(Landroid/view/View$OnClickListener;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Landroid/text/style/ClickableSpan;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;->anm:Landroid/view/View$OnClickListener;

    .line 5
    .line 6
    iput p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;->color:I

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;->anm:Landroid/view/View$OnClickListener;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, Landroid/view/View$OnClickListener;->onClick(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final updateDrawState(Landroid/text/TextPaint;)V
    .locals 1

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentAdItemView$a;->color:I

    .line 2
    .line 3
    iput v0, p1, Landroid/text/TextPaint;->linkColor:I

    .line 4
    .line 5
    invoke-virtual {p1, v0}, Landroid/graphics/Paint;->setColor(I)V

    .line 6
    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    invoke-virtual {p1, v0}, Landroid/graphics/Paint;->setUnderlineText(Z)V

    .line 10
    .line 11
    .line 12
    return-void
.end method
