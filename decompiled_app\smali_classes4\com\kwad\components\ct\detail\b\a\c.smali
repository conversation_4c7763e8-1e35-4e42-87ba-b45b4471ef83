.class public final Lcom/kwad/components/ct/detail/b/a/c;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/kwad/components/ct/detail/b/a/c$a;
    }
.end annotation


# instance fields
.field private amo:Lcom/kwad/components/core/j/a;

.field private awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

.field private awi:Lcom/kwad/components/ct/detail/b/a/c$a;

.field private fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/ct/detail/b/a/c$1;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/a/c$1;-><init>(Lcom/kwad/components/ct/detail/b/a/c;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->amo:Lcom/kwad/components/core/j/a;

    .line 10
    .line 11
    new-instance v0, Lcom/kwad/components/ct/detail/b/a/c$2;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/a/c$2;-><init>(Lcom/kwad/components/ct/detail/b/a/c;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 17
    .line 18
    return-void
.end method

.method private BK()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awi:Lcom/kwad/components/ct/detail/b/a/c$a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 6
    .line 7
    invoke-virtual {v1, v0}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 11
    .line 12
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->awi:Lcom/kwad/components/ct/detail/b/a/c$a;

    .line 13
    .line 14
    const-wide/16 v2, 0xc8

    .line 15
    .line 16
    invoke-virtual {v0, v1, v2, v3}, Landroid/view/View;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 17
    .line 18
    .line 19
    :cond_0
    return-void
.end method

.method private BL()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;->zH()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/a/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/a/c;->BL()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/a/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/a/c;->BK()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private handleAdClick()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 4
    .line 5
    instance-of v0, v0, Lcom/kwad/components/ct/detail/ad/a;

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-static {}, Lcom/kwad/components/core/t/d;->sM()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 21
    .line 22
    iget-object v2, v2, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 23
    .line 24
    invoke-virtual {v0, v2, v1}, Lcom/kwad/components/ct/e/b;->b(Lcom/kwad/components/ct/response/model/CtAdTemplate;I)V

    .line 25
    .line 26
    .line 27
    :cond_0
    new-instance v0, Lcom/kwad/components/core/e/d/a$a;

    .line 28
    .line 29
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-direct {v0, v2}, Lcom/kwad/components/core/e/d/a$a;-><init>(Landroid/content/Context;)V

    .line 34
    .line 35
    .line 36
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 37
    .line 38
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->aC(Lcom/kwad/sdk/core/response/model/AdTemplate;)Lcom/kwad/components/core/e/d/a$a;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 43
    .line 44
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->b(Lcom/kwad/components/core/e/d/c;)Lcom/kwad/components/core/e/d/a$a;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    const/4 v2, 0x2

    .line 49
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->ap(I)Lcom/kwad/components/core/e/d/a$a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    const/4 v2, 0x0

    .line 54
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->aq(Z)Lcom/kwad/components/core/e/d/a$a;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    const/16 v2, 0x19

    .line 59
    .line 60
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->ao(I)Lcom/kwad/components/core/e/d/a$a;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/a/c;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 65
    .line 66
    invoke-virtual {v2}, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;->getTouchCoords()Lcom/kwad/sdk/utils/ai$a;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-virtual {v0, v2}, Lcom/kwad/components/core/e/d/a$a;->d(Lcom/kwad/sdk/utils/ai$a;)Lcom/kwad/components/core/e/d/a$a;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/e/d/a$a;->as(Z)Lcom/kwad/components/core/e/d/a$a;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-static {v0}, Lcom/kwad/components/core/e/d/a;->a(Lcom/kwad/components/core/e/d/a$a;)I

    .line 79
    .line 80
    .line 81
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    iput-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->mApkDownloadHelper:Lcom/kwad/components/core/e/d/c;

    .line 13
    .line 14
    invoke-static {v1}, Lcom/kwad/components/ct/response/a/a;->aR(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-static {v0}, Lcom/kwad/sdk/utils/bq;->isNullString(Ljava/lang/String;)Z

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-eqz v1, :cond_0

    .line 23
    .line 24
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 25
    .line 26
    invoke-static {v1}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    sget v1, Lcom/kwad/sdk/R$string;->ksad_ad_default_author:I

    .line 37
    .line 38
    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    :cond_0
    invoke-static {v0}, Lcom/kwad/sdk/utils/bq;->isNullString(Ljava/lang/String;)Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-nez v1, :cond_2

    .line 47
    .line 48
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 49
    .line 50
    invoke-static {v1}, Lcom/kwad/components/ct/response/a/a;->ay(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Lcom/kwad/components/ct/response/model/CtPhotoInfo;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-static {v1}, Lcom/kwad/components/ct/response/a/c;->o(Lcom/kwad/components/ct/response/model/CtPhotoInfo;)Z

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    if-eqz v1, :cond_1

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_1
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 62
    .line 63
    invoke-virtual {v1, v0}, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;->setContent(Ljava/lang/String;)V

    .line 64
    .line 65
    .line 66
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 67
    .line 68
    const/4 v1, 0x0

    .line 69
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 70
    .line 71
    .line 72
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 73
    .line 74
    const/4 v1, 0x1

    .line 75
    invoke-virtual {v0, v1}, Landroid/view/View;->setSelected(Z)V

    .line 76
    .line 77
    .line 78
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 79
    .line 80
    invoke-virtual {v0, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 81
    .line 82
    .line 83
    goto :goto_1

    .line 84
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 85
    .line 86
    const/16 v1, 0x8

    .line 87
    .line 88
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 89
    .line 90
    .line 91
    :goto_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 92
    .line 93
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 94
    .line 95
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 96
    .line 97
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 98
    .line 99
    .line 100
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 101
    .line 102
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 103
    .line 104
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->amo:Lcom/kwad/components/core/j/a;

    .line 105
    .line 106
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    return-void
.end method

.method public final onClick(Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/a/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/sdk/core/response/b/e;->eH(Lcom/kwad/sdk/core/response/model/AdTemplate;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b/a/c;->handleAdClick()V

    .line 10
    .line 11
    .line 12
    :cond_0
    return-void
.end method

.method public final onCreate()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_root_container:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 13
    .line 14
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_marquee_tip:I

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    check-cast v0, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 23
    .line 24
    const/4 v1, 0x1

    .line 25
    invoke-virtual {v0, v1}, Landroid/view/View;->setSelected(Z)V

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 29
    .line 30
    const v1, -0x10002

    .line 31
    .line 32
    .line 33
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;->setTextColor(I)V

    .line 34
    .line 35
    .line 36
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 37
    .line 38
    const/high16 v1, 0x40400000    # 3.0f

    .line 39
    .line 40
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;->setTextSpeed(F)V

    .line 41
    .line 42
    .line 43
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 44
    .line 45
    const/high16 v1, 0x41600000    # 14.0f

    .line 46
    .line 47
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;->setTextSize(F)V

    .line 48
    .line 49
    .line 50
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 51
    .line 52
    const/4 v1, 0x2

    .line 53
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;->setRepetType(I)V

    .line 54
    .line 55
    .line 56
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;->setStartLocationDistance(F)V

    .line 60
    .line 61
    .line 62
    new-instance v0, Lcom/kwad/components/ct/detail/b/a/c$a;

    .line 63
    .line 64
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 65
    .line 66
    const/4 v2, 0x0

    .line 67
    invoke-direct {v0, v1, v2}, Lcom/kwad/components/ct/detail/b/a/c$a;-><init>(Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;B)V

    .line 68
    .line 69
    .line 70
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awi:Lcom/kwad/components/ct/detail/b/a/c$a;

    .line 71
    .line 72
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/a/c;->awh:Lcom/kwad/components/ct/detail/photo/newui/MarqueeView;

    .line 5
    .line 6
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->awi:Lcom/kwad/components/ct/detail/b/a/c$a;

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 12
    .line 13
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 14
    .line 15
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 21
    .line 22
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 23
    .line 24
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/c;->amo:Lcom/kwad/components/core/j/a;

    .line 25
    .line 26
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    return-void
.end method
