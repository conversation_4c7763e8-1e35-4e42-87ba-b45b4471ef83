.class public final synthetic Ltop/cycdm/cycapp/ui/history/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/MutableState;

.field public final synthetic b:Ltop/cycdm/cycapp/ui/history/HistoryVM;

.field public final synthetic c:Ltop/cycdm/model/j;


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/f;->a:Landroidx/compose/runtime/MutableState;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/history/f;->b:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/history/f;->c:Ltop/cycdm/model/j;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/history/f;->a:Landroidx/compose/runtime/MutableState;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/f;->b:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/history/f;->c:Ltop/cycdm/model/j;

    invoke-static {v0, v1, v2}, Ltop/cycdm/cycapp/ui/history/HistoryScreenKt;->d(Landroidx/compose/runtime/MutableState;Ltop/cycdm/cycapp/ui/history/HistoryVM;Ltop/cycdm/model/j;)Lkotlin/t;

    move-result-object v0

    return-object v0
.end method
