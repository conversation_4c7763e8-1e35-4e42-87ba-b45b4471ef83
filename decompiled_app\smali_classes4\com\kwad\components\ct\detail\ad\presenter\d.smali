.class public final Lcom/kwad/components/ct/detail/ad/presenter/d;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

.field private alX:Lcom/kwad/components/ct/detail/e/a;

.field private final amy:Ljava/lang/Runnable;

.field private final mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/d$1;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/d$1;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/d;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 10
    .line 11
    new-instance v0, Lcom/kwad/components/ct/detail/ad/presenter/d$2;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/ad/presenter/d$2;-><init>(Lcom/kwad/components/ct/detail/ad/presenter/d;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->amy:Ljava/lang/Runnable;

    .line 17
    .line 18
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/ad/presenter/d;)Ljava/lang/Runnable;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->amy:Ljava/lang/Runnable;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/ad/presenter/d;)Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/ad/presenter/d;)Z
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/d;->xM()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/ad/presenter/d;)Lcom/kwad/components/ct/detail/e/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/ad/presenter/d;)Z
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/ad/presenter/d;->xN()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method private xM()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->Cm()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x1

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    return v1

    .line 11
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 12
    .line 13
    iget-boolean v2, v0, Lcom/kwad/components/ct/detail/c;->ame:Z

    .line 14
    .line 15
    if-eqz v2, :cond_1

    .line 16
    .line 17
    return v1

    .line 18
    :cond_1
    invoke-static {v0}, Lcom/kwad/components/ct/detail/d/b;->a(Lcom/kwad/components/ct/detail/c;)Z

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    if-eqz v0, :cond_2

    .line 23
    .line 24
    return v1

    .line 25
    :cond_2
    const/4 v0, 0x0

    .line 26
    return v0
.end method

.method private xN()Z
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->Cm()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x1

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    return v1

    .line 11
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 12
    .line 13
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 14
    .line 15
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->hasNext()Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 22
    .line 23
    iget-boolean v0, v0, Lcom/kwad/components/ct/detail/c;->amf:Z

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    return v1

    .line 28
    :cond_1
    const/4 v0, 0x0

    .line 29
    return v0
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v1, v0, Lcom/kwad/components/ct/detail/c;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 7
    .line 8
    iput-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 9
    .line 10
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 13
    .line 14
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 15
    .line 16
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 5
    .line 6
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->amy:Ljava/lang/Runnable;

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 12
    .line 13
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/d;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 14
    .line 15
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method
