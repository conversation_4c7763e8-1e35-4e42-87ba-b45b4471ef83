.class Lcom/beizi/ad/v2/f/b$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/beizi/ad/internal/h/i$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/f/b;->w()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/f/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()V
    .locals 2

    .line 28
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Lcom/beizi/ad/v2/f/b;->b(I)V

    return-void
.end method

.method public a(Landroid/graphics/Bitmap;)V
    .locals 10

    const/16 v0, 0x8

    .line 1
    :try_start_0
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {v1}, Lcom/beizi/ad/v2/f/b;->c(Lcom/beizi/ad/v2/f/b;)Landroid/view/ViewGroup;

    move-result-object v1

    if-nez v1, :cond_0

    .line 2
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    const/16 v1, 0xa

    invoke-virtual {p1, v1}, Lcom/beizi/ad/v2/f/b;->b(I)V

    return-void

    :catch_0
    move-exception p1

    goto/16 :goto_2

    :cond_0
    if-nez p1, :cond_1

    .line 3
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-virtual {p1, v0}, Lcom/beizi/ad/v2/f/b;->b(I)V

    return-void

    .line 4
    :cond_1
    const-string v1, "BeiZisAd"

    const-string v2, "renderImageView onBitmapLoaded"

    invoke-static {v1, v2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 5
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {v1}, Lcom/beizi/ad/v2/f/b;->c(Lcom/beizi/ad/v2/f/b;)Landroid/view/ViewGroup;

    move-result-object v1

    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    .line 6
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v2

    .line 7
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v3

    int-to-double v4, v2

    const-wide/high16 v6, 0x3ff0000000000000L    # 1.0

    mul-double/2addr v4, v6

    int-to-double v2, v3

    div-double/2addr v4, v2

    double-to-float v2, v4

    .line 8
    iget-object v3, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {v3}, Lcom/beizi/ad/v2/f/b;->i(Lcom/beizi/ad/v2/f/b;)I

    move-result v3

    .line 9
    iget-object v4, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {v4}, Lcom/beizi/ad/v2/f/b;->j(Lcom/beizi/ad/v2/f/b;)I

    move-result v4

    if-lez v4, :cond_2

    .line 10
    iget-object v4, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {v4}, Lcom/beizi/ad/v2/f/b;->j(Lcom/beizi/ad/v2/f/b;)I

    move-result v4

    goto :goto_0

    :cond_2
    int-to-float v4, v3

    div-float/2addr v4, v2

    float-to-int v4, v4

    .line 11
    :goto_0
    new-instance v5, Landroid/widget/ImageView;

    invoke-direct {v5, v1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 12
    invoke-virtual {v5}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v8

    if-eqz v8, :cond_3

    .line 13
    iput v3, v8, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 14
    iput v4, v8, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 15
    invoke-virtual {v5, v8}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    :cond_3
    int-to-double v8, v3

    mul-double/2addr v8, v6

    int-to-double v6, v4

    div-double/2addr v8, v6

    double-to-float v6, v8

    sub-float/2addr v6, v2

    .line 16
    invoke-static {v6}, Ljava/lang/Math;->abs(F)F

    move-result v2

    const v6, 0x3dcccccd    # 0.1f

    cmpl-float v6, v2, v6

    if-lez v6, :cond_4

    .line 17
    invoke-static {}, Lcom/beizi/ad/lance/a/c;->b()Lcom/beizi/ad/lance/a/c;

    move-result-object v2

    invoke-virtual {v2}, Lcom/beizi/ad/lance/a/c;->e()Ljava/util/concurrent/ExecutorService;

    move-result-object v2

    new-instance v6, Lcom/beizi/ad/v2/f/b$2$1;

    invoke-direct {v6, p0, v1, p1, v5}, Lcom/beizi/ad/v2/f/b$2$1;-><init>(Lcom/beizi/ad/v2/f/b$2;Landroid/content/Context;Landroid/graphics/Bitmap;Landroid/widget/ImageView;)V

    invoke-interface {v2, v6}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    goto :goto_1

    :cond_4
    float-to-double v1, v2

    const-wide v6, 0x3fb1eb851eb851ecL    # 0.07

    cmpl-double v1, v1, v6

    if-lez v1, :cond_5

    .line 18
    sget-object v1, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v5, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    goto :goto_1

    .line 19
    :cond_5
    sget-object v1, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v5, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 20
    :goto_1
    invoke-virtual {v5, p1}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 21
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {p1, v3, v4}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 22
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {v1}, Lcom/beizi/ad/v2/f/b;->c(Lcom/beizi/ad/v2/f/b;)Landroid/view/ViewGroup;

    move-result-object v1

    invoke-virtual {v1, v5, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 23
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->m(Lcom/beizi/ad/v2/f/b;)V

    .line 24
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->n(Lcom/beizi/ad/v2/f/b;)V

    .line 25
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->o(Lcom/beizi/ad/v2/f/b;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 26
    :goto_2
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 27
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$2;->a:Lcom/beizi/ad/v2/f/b;

    invoke-virtual {p1, v0}, Lcom/beizi/ad/v2/f/b;->b(I)V

    return-void
.end method
