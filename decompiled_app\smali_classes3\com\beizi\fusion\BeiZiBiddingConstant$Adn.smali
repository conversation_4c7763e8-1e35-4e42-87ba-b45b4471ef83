.class public Lcom/beizi/fusion/BeiZiBiddingConstant$Adn;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/beizi/fusion/BeiZiBiddingConstant;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Adn"
.end annotation


# static fields
.field public static final ADN_BD:Ljava/lang/String; = "1018"

.field public static final ADN_BZ:Ljava/lang/String; = "6666"

.field public static final ADN_CSJ:Ljava/lang/String; = "1013"

.field public static final ADN_GDT:Ljava/lang/String; = "1012"

.field public static final ADN_GM:Ljava/lang/String; = "1022"

.field public static final ADN_HW:Ljava/lang/String; = "1020"

.field public static final ADN_JD:Ljava/lang/String; = "1021"

.field public static final ADN_KS:Ljava/lang/String; = "1019"

.field public static final ADN_OTHER:Ljava/lang/String; = "9999"

.field public static final ADN_QM:Ljava/lang/String; = "1030"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
