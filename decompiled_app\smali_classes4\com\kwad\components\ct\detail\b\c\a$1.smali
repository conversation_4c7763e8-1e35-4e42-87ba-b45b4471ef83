.class final Lcom/kwad/components/ct/detail/b/c/a$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/widget/n;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awE:Lcom/kwad/components/ct/detail/b/c/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$1;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final at()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$1;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->a(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lcom/kwad/sdk/utils/n;->fm(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
