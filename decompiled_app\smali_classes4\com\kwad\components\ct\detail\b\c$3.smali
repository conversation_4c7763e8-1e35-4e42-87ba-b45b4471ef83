.class final Lcom/kwad/components/ct/detail/b/c$3;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avk:Lcom/kwad/components/ct/detail/b/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/b/c;->Bv()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final onMediaPlayError(II)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lcom/kwad/components/ct/detail/b/c;->s(II)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final onMediaPlayPaused()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->b(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/widget/a/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lcom/kwad/components/core/widget/a/a;->wg()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 14
    .line 15
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->g(Lcom/kwad/components/ct/detail/b/c;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 19
    .line 20
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/b/c;->Bt()V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final onMediaPlayStart()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->j(Lcom/kwad/components/ct/detail/b/c;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->k(Lcom/kwad/components/ct/detail/b/c;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 12
    .line 13
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/b/c;->Bs()V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final onMediaPlaying()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->b(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/widget/a/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lcom/kwad/components/core/widget/a/a;->wg()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 14
    .line 15
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->k(Lcom/kwad/components/ct/detail/b/c;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$3;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 19
    .line 20
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/b/c;->Bu()V

    .line 21
    .line 22
    .line 23
    return-void
.end method
