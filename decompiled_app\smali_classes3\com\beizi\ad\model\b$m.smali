.class public Lcom/beizi/ad/model/b$m;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/beizi/ad/model/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "m"
.end annotation


# instance fields
.field private a:Ljava/lang/String;

.field private b:Ljava/lang/String;

.field private c:Lcom/beizi/ad/model/e$a;

.field private d:I

.field private e:Lcom/beizi/ad/model/e$h;

.field private f:Ljava/lang/String;

.field private g:Ljava/lang/String;

.field private h:Lcom/beizi/ad/model/b$h;

.field private i:Z

.field private j:I

.field private k:Z

.field private l:I

.field private m:Z

.field private n:Z

.field private o:Z

.field private p:Z

.field private q:Z

.field private r:I

.field private s:I

.field private t:Ljava/lang/String;

.field private u:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$d;",
            ">;"
        }
    .end annotation
.end field

.field private v:Lcom/beizi/ad/model/b$i;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->a:Ljava/lang/String;

    return-object v0
.end method

.method public a(I)V
    .locals 0

    .line 4
    iput p1, p0, Lcom/beizi/ad/model/b$m;->d:I

    return-void
.end method

.method public a(Lcom/beizi/ad/model/b$h;)V
    .locals 0

    .line 6
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->h:Lcom/beizi/ad/model/b$h;

    return-void
.end method

.method public a(Lcom/beizi/ad/model/b$i;)V
    .locals 0

    .line 9
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->v:Lcom/beizi/ad/model/b$i;

    return-void
.end method

.method public a(Lcom/beizi/ad/model/e$a;)V
    .locals 0

    .line 3
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->c:Lcom/beizi/ad/model/e$a;

    return-void
.end method

.method public a(Lcom/beizi/ad/model/e$h;)V
    .locals 0

    .line 5
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->e:Lcom/beizi/ad/model/e$h;

    return-void
.end method

.method public a(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->a:Ljava/lang/String;

    return-void
.end method

.method public a(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$d;",
            ">;)V"
        }
    .end annotation

    .line 8
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->u:Ljava/util/List;

    return-void
.end method

.method public a(Z)V
    .locals 0

    .line 7
    iput-boolean p1, p0, Lcom/beizi/ad/model/b$m;->i:Z

    return-void
.end method

.method public b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->b:Ljava/lang/String;

    return-object v0
.end method

.method public b(I)V
    .locals 0

    .line 3
    iput p1, p0, Lcom/beizi/ad/model/b$m;->j:I

    return-void
.end method

.method public b(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->b:Ljava/lang/String;

    return-void
.end method

.method public b(Z)V
    .locals 0

    .line 4
    iput-boolean p1, p0, Lcom/beizi/ad/model/b$m;->k:Z

    return-void
.end method

.method public c()Lcom/beizi/ad/model/e$a;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->c:Lcom/beizi/ad/model/e$a;

    return-object v0
.end method

.method public c(I)V
    .locals 0

    .line 3
    iput p1, p0, Lcom/beizi/ad/model/b$m;->l:I

    return-void
.end method

.method public c(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->f:Ljava/lang/String;

    return-void
.end method

.method public c(Z)V
    .locals 0

    .line 4
    iput-boolean p1, p0, Lcom/beizi/ad/model/b$m;->m:Z

    return-void
.end method

.method public d()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/beizi/ad/model/b$m;->d:I

    return v0
.end method

.method public d(I)V
    .locals 0

    .line 4
    iput p1, p0, Lcom/beizi/ad/model/b$m;->r:I

    return-void
.end method

.method public d(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->g:Ljava/lang/String;

    return-void
.end method

.method public d(Z)V
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/beizi/ad/model/b$m;->n:Z

    return-void
.end method

.method public e()Lcom/beizi/ad/model/e$h;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->e:Lcom/beizi/ad/model/e$h;

    return-object v0
.end method

.method public e(I)V
    .locals 0

    .line 3
    iput p1, p0, Lcom/beizi/ad/model/b$m;->s:I

    return-void
.end method

.method public e(Ljava/lang/String;)V
    .locals 0

    .line 4
    iput-object p1, p0, Lcom/beizi/ad/model/b$m;->t:Ljava/lang/String;

    return-void
.end method

.method public e(Z)V
    .locals 0

    .line 2
    iput-boolean p1, p0, Lcom/beizi/ad/model/b$m;->o:Z

    return-void
.end method

.method public f()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->f:Ljava/lang/String;

    return-object v0
.end method

.method public f(Z)V
    .locals 0

    .line 2
    iput-boolean p1, p0, Lcom/beizi/ad/model/b$m;->p:Z

    return-void
.end method

.method public g()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->g:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public h()Lcom/beizi/ad/model/b$h;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->h:Lcom/beizi/ad/model/b$h;

    .line 2
    .line 3
    return-object v0
.end method

.method public i()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/model/b$m;->i:Z

    .line 2
    .line 3
    return v0
.end method

.method public j()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/beizi/ad/model/b$m;->j:I

    .line 2
    .line 3
    return v0
.end method

.method public k()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/model/b$m;->k:Z

    .line 2
    .line 3
    return v0
.end method

.method public l()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/beizi/ad/model/b$m;->l:I

    .line 2
    .line 3
    return v0
.end method

.method public m()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/model/b$m;->m:Z

    .line 2
    .line 3
    return v0
.end method

.method public n()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/model/b$m;->n:Z

    .line 2
    .line 3
    return v0
.end method

.method public o()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/model/b$m;->o:Z

    .line 2
    .line 3
    return v0
.end method

.method public p()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/model/b$m;->p:Z

    .line 2
    .line 3
    return v0
.end method

.method public q()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/model/b$m;->q:Z

    .line 2
    .line 3
    return v0
.end method

.method public r()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/beizi/ad/model/b$d;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->u:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public s()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->u:Ljava/util/List;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    return v0

    .line 7
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method

.method public t()Lcom/beizi/ad/model/b$i;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/model/b$m;->v:Lcom/beizi/ad/model/b$i;

    .line 2
    .line 3
    return-object v0
.end method
