.class final Lcom/kwad/components/ct/detail/b/c/a$3;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awE:Lcom/kwad/components/ct/detail/b/c/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pQ()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    const-string v1, "DetailLogPagePresenter"

    .line 9
    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    new-instance v0, Ljava/lang/StringBuilder;

    .line 13
    .line 14
    const-string v2, "position: "

    .line 15
    .line 16
    invoke-direct {v0, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 20
    .line 21
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 26
    .line 27
    .line 28
    const-string v2, " becomesAttachedOnPageSelected"

    .line 29
    .line 30
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 41
    .line 42
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->e(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/widget/a/b;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    if-nez v0, :cond_1

    .line 47
    .line 48
    const-string v0, "mVisibleHelper is null"

    .line 49
    .line 50
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->w(Ljava/lang/String;Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 55
    .line 56
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->a(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-static {v0}, Lcom/kwad/sdk/utils/n;->fm(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 61
    .line 62
    .line 63
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 64
    .line 65
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->e(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/widget/a/b;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 70
    .line 71
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->f(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/core/i/c;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/widget/a/a;->a(Lcom/kwad/sdk/core/i/c;)V

    .line 76
    .line 77
    .line 78
    return-void
.end method

.method public final pR()V
    .locals 5

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pR()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->a(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {v0}, Lcom/kwad/sdk/utils/n;->fk(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 11
    .line 12
    .line 13
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    const-string v1, "DetailLogPagePresenter"

    .line 18
    .line 19
    if-eqz v0, :cond_0

    .line 20
    .line 21
    new-instance v0, Ljava/lang/StringBuilder;

    .line 22
    .line 23
    const-string v2, "position: "

    .line 24
    .line 25
    invoke-direct {v0, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 29
    .line 30
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 35
    .line 36
    .line 37
    const-string v2, " becomesDetachedOnPageSelected"

    .line 38
    .line 39
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 50
    .line 51
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->e(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/widget/a/b;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    if-nez v0, :cond_1

    .line 56
    .line 57
    const-string v0, "mVisibleHelper is null"

    .line 58
    .line 59
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->w(Ljava/lang/String;Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    return-void

    .line 63
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 64
    .line 65
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->e(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/components/core/widget/a/b;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 70
    .line 71
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->f(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/core/i/c;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/widget/a/a;->b(Lcom/kwad/sdk/core/i/c;)V

    .line 76
    .line 77
    .line 78
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 79
    .line 80
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->apx()J

    .line 85
    .line 86
    .line 87
    move-result-wide v0

    .line 88
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 89
    .line 90
    invoke-static {v2}, Lcom/kwad/components/ct/detail/b/c/a;->g(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    invoke-virtual {v2}, Lcom/kwad/sdk/utils/bw;->apx()J

    .line 95
    .line 96
    .line 97
    move-result-wide v2

    .line 98
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 99
    .line 100
    invoke-static {v4, v0, v1, v2, v3}, Lcom/kwad/components/ct/detail/b/c/a;->a(Lcom/kwad/components/ct/detail/b/c/a;JJ)V

    .line 101
    .line 102
    .line 103
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$3;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 104
    .line 105
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->h(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 106
    .line 107
    .line 108
    return-void
.end method
