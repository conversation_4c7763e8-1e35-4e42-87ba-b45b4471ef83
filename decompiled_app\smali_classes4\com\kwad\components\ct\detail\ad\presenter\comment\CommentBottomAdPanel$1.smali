.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->a(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$1;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 7
    .line 8
    invoke-static {p1}, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->b(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    invoke-static {p1}, Lcom/kwad/sdk/core/adlog/c;->cp(Lcom/kwad/sdk/core/response/model/AdTemplate;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method
