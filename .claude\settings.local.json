{"permissions": {"allow": ["Bash(java -jar jar/3rd/apktool_2.11.0.jar d 1.apk -o decompiled_app)", "Bash(find /mnt/d/augment-projects/CatVodSpider/decompiled_app/smali_classes* -name \"*.smali\")", "Bash(find /mnt/d/augment-projects/CatVodSpider/decompiled_app/smali_classes6/top/cycdm -name \"*.smali\")", "Bash(find /mnt/d/augment-projects/CatVodSpider/decompiled_app/smali_classes* -maxdepth 2 -type d)", "Bash(grep -A5 -B5 \"app_name\\|次元城\" /mnt/d/augment-projects/CatVodSpider/decompiled_app/res/values/strings.xml)", "Bash(find /mnt/d/augment-projects/CatVodSpider/decompiled_app -type d -name \"assets\" -o -name \"raw\")", "Bash(find /mnt/d/augment-projects/CatVodSpider/decompiled_app/smali* -name \"*.smali\" -exec grep -l \"const-string.*http\\|const-string.*api\\|const-string.*url\" {})", "Bash(find /mnt/d/augment-projects/CatVodSpider/decompiled_app/smali* -path \"*/top/cycdm/*\" -name \"*.smali\" -exec grep -l \"const-string.*http\\|const-string.*api\\|\\.cycdm\\.\" {})"], "deny": []}}