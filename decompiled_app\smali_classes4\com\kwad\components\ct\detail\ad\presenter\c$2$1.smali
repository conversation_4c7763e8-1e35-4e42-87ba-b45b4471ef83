.class final Lcom/kwad/components/ct/detail/ad/presenter/c$2$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/c$2;->xL()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amx:Lcom/kwad/components/ct/detail/ad/presenter/c$2;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/c$2;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2$1;->amx:Lcom/kwad/components/ct/detail/ad/presenter/c$2;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onGlobalLayout()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2$1;->amx:Lcom/kwad/components/ct/detail/ad/presenter/c$2;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 4
    .line 5
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->z(Lcom/kwad/components/ct/detail/ad/presenter/c;)Lcom/kwad/sdk/core/webview/KSApiWebView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0, p0}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/c$2$1;->amx:Lcom/kwad/components/ct/detail/ad/presenter/c$2;

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    iput-object v1, v0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amw:Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;

    .line 20
    .line 21
    iget-object v0, v0, Lcom/kwad/components/ct/detail/ad/presenter/c$2;->amv:Lcom/kwad/components/ct/detail/ad/presenter/c;

    .line 22
    .line 23
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/c;->A(Lcom/kwad/components/ct/detail/ad/presenter/c;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method
