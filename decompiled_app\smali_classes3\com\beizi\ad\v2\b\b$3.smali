.class Lcom/beizi/ad/v2/b/b$3;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/b/b;->a(Lcom/beizi/ad/internal/f/c;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/b/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/b/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/b/b$3;->a:Lcom/beizi/ad/v2/b/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b$3;->a:Lcom/beizi/ad/v2/b/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/beizi/ad/v2/b/b;->b(Lcom/beizi/ad/v2/b/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
