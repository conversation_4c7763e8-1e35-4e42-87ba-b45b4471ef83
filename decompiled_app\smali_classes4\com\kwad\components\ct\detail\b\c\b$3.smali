.class final Lcom/kwad/components/ct/detail/b/c/b$3;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/core/i/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awL:Lcom/kwad/components/ct/detail/b/c/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/b$3;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final aT()V
    .locals 3

    .line 1
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/b;->BY()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    new-instance v0, Ljava/lang/StringBuilder;

    .line 8
    .line 9
    const-string v1, "position: "

    .line 10
    .line 11
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/b$3;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 15
    .line 16
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/b;->h(Lcom/kwad/components/ct/detail/b/c/b;)I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 21
    .line 22
    .line 23
    const-string v1, " onPageVisible"

    .line 24
    .line 25
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    const-string v1, "DetailLogVideoPresenter"

    .line 33
    .line 34
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 35
    .line 36
    .line 37
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$3;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 38
    .line 39
    const/4 v1, 0x1

    .line 40
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c/b;->b(Lcom/kwad/components/ct/detail/b/c/b;Z)Z

    .line 41
    .line 42
    .line 43
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$3;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 44
    .line 45
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    .line 46
    .line 47
    .line 48
    move-result-wide v1

    .line 49
    invoke-static {v0, v1, v2}, Lcom/kwad/components/ct/detail/b/c/b;->a(Lcom/kwad/components/ct/detail/b/c/b;J)J

    .line 50
    .line 51
    .line 52
    return-void
.end method

.method public final aU()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/b$3;->awL:Lcom/kwad/components/ct/detail/b/c/b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c/b;->b(Lcom/kwad/components/ct/detail/b/c/b;Z)Z

    .line 5
    .line 6
    .line 7
    return-void
.end method
