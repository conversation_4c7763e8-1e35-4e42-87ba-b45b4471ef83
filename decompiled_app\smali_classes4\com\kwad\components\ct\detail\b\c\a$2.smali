.class final Lcom/kwad/components/ct/detail/b/c/a$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/sdk/core/i/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awE:Lcom/kwad/components/ct/detail/b/c/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final aT()V
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->b(Lcom/kwad/components/ct/detail/b/c/a;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->Aa()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    const-string v1, "position: "

    .line 17
    .line 18
    const-string v2, "DetailLogPagePresenter"

    .line 19
    .line 20
    if-eqz v0, :cond_0

    .line 21
    .line 22
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 23
    .line 24
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->zY()V

    .line 29
    .line 30
    .line 31
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    new-instance v0, Ljava/lang/StringBuilder;

    .line 38
    .line 39
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 40
    .line 41
    .line 42
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 43
    .line 44
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 49
    .line 50
    .line 51
    const-string v1, " onPageVisible resumeTiming stayDuration: "

    .line 52
    .line 53
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 54
    .line 55
    .line 56
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 57
    .line 58
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 63
    .line 64
    .line 65
    move-result-wide v3

    .line 66
    invoke-virtual {v0, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-static {v2, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    return-void

    .line 77
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 78
    .line 79
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->startTiming()V

    .line 84
    .line 85
    .line 86
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 87
    .line 88
    .line 89
    move-result v0

    .line 90
    if-eqz v0, :cond_1

    .line 91
    .line 92
    new-instance v0, Ljava/lang/StringBuilder;

    .line 93
    .line 94
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 98
    .line 99
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 100
    .line 101
    .line 102
    move-result v1

    .line 103
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 104
    .line 105
    .line 106
    const-string v1, " onPageVisible startTiming stayDuration: "

    .line 107
    .line 108
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 109
    .line 110
    .line 111
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 112
    .line 113
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 118
    .line 119
    .line 120
    move-result-wide v3

    .line 121
    invoke-virtual {v0, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 122
    .line 123
    .line 124
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 125
    .line 126
    .line 127
    move-result-object v0

    .line 128
    invoke-static {v2, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 129
    .line 130
    .line 131
    :cond_1
    return-void
.end method

.method public final aU()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lcom/kwad/sdk/utils/bw;->zZ()V

    .line 8
    .line 9
    .line 10
    invoke-static {}, Lcom/kwad/components/ct/detail/b/c/a;->BU()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    new-instance v0, Ljava/lang/StringBuilder;

    .line 17
    .line 18
    const-string v1, "position: "

    .line 19
    .line 20
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 24
    .line 25
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->d(Lcom/kwad/components/ct/detail/b/c/a;)I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 30
    .line 31
    .line 32
    const-string v1, " onPageInvisible stayDuration: "

    .line 33
    .line 34
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 35
    .line 36
    .line 37
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c/a$2;->awE:Lcom/kwad/components/ct/detail/b/c/a;

    .line 38
    .line 39
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c/a;->c(Lcom/kwad/components/ct/detail/b/c/a;)Lcom/kwad/sdk/utils/bw;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    invoke-virtual {v1}, Lcom/kwad/sdk/utils/bw;->getTime()J

    .line 44
    .line 45
    .line 46
    move-result-wide v1

    .line 47
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 48
    .line 49
    .line 50
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    const-string v1, "DetailLogPagePresenter"

    .line 55
    .line 56
    invoke-static {v1, v0}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 57
    .line 58
    .line 59
    :cond_0
    return-void
.end method
