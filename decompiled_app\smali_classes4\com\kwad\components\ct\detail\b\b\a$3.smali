.class final Lcom/kwad/components/ct/detail/b/b/a$3;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/b/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awv:Lcom/kwad/components/ct/detail/b/b/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$3;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$3;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->h(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
