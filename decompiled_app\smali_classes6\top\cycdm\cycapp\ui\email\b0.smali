.class public final synthetic Ltop/cycdm/cycapp/ui/email/b0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/b0;->a:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/b0;->a:Ljava/lang/String;

    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/a;

    invoke-static {v0, p1}, Ltop/cycdm/cycapp/ui/email/EmailVM$sendEmail$1$3;->a(Ljava/lang/String;Lorg/orbitmvi/orbit/syntax/simple/a;)Ltop/cycdm/cycapp/ui/email/x;

    move-result-object p1

    return-object p1
.end method
