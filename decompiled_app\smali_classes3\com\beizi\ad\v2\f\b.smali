.class public Lcom/beizi/ad/v2/f/b;
.super Lcom/beizi/ad/v2/a/b;
.source "SourceFile"


# instance fields
.field private C:Landroid/view/ViewGroup;

.field private D:Landroid/view/View;

.field private E:Lcom/beizi/ad/a;

.field private F:Landroid/os/CountDownTimer;

.field private G:Ljava/lang/String;

.field private H:Lcom/beizi/ad/model/e$f;

.field private I:I

.field private J:I

.field private K:Landroid/webkit/WebView;


# direct methods
.method public constructor <init>(Landroid/content/Context;Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/String;)V
    .locals 0

    .line 1
    sget-object p3, Lcom/beizi/ad/internal/k;->b:Lcom/beizi/ad/internal/k;

    .line 2
    .line 3
    invoke-direct {p0, p1, p4, p3}, Lcom/beizi/ad/v2/a/b;-><init>(Landroid/content/Context;Ljava/lang/String;Lcom/beizi/ad/internal/k;)V

    .line 4
    .line 5
    .line 6
    iput-object p2, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 7
    .line 8
    return-void
.end method

.method private A()V
    .locals 10

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 6
    .line 7
    if-nez v1, :cond_1

    .line 8
    .line 9
    :cond_0
    move-object v3, p0

    .line 10
    goto/16 :goto_2

    .line 11
    .line 12
    :cond_1
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->I()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    const/4 v1, 0x2

    .line 17
    if-eq v0, v1, :cond_2

    .line 18
    .line 19
    const/4 v2, 0x5

    .line 20
    if-ne v0, v2, :cond_0

    .line 21
    .line 22
    :cond_2
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 23
    .line 24
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->B()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v5

    .line 28
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 29
    .line 30
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->D()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 35
    .line 36
    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->C()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 41
    .line 42
    invoke-virtual {v3}, Lcom/beizi/ad/internal/f/c;->F()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v3

    .line 46
    iget-object v4, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 47
    .line 48
    invoke-virtual {v4}, Lcom/beizi/ad/internal/f/c;->E()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    invoke-static {v3}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 53
    .line 54
    .line 55
    move-result v6

    .line 56
    if-nez v6, :cond_3

    .line 57
    .line 58
    move-object v7, v3

    .line 59
    goto :goto_0

    .line 60
    :cond_3
    move-object v7, v4

    .line 61
    :goto_0
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 62
    .line 63
    invoke-virtual {v3}, Lcom/beizi/ad/internal/f/c;->G()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object v6

    .line 67
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 68
    .line 69
    invoke-virtual {v3}, Lcom/beizi/ad/internal/f/c;->H()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v8

    .line 73
    new-instance v3, Ljava/lang/StringBuilder;

    .line 74
    .line 75
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 76
    .line 77
    .line 78
    const-string v4, "\u5e94\u7528\u540d\u79f0\uff1a"

    .line 79
    .line 80
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 81
    .line 82
    .line 83
    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 84
    .line 85
    .line 86
    const-string v4, " | \u5f00\u53d1\u8005\uff1a"

    .line 87
    .line 88
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 89
    .line 90
    .line 91
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 92
    .line 93
    .line 94
    const-string v0, " | \u5e94\u7528\u7248\u672c\uff1a"

    .line 95
    .line 96
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 97
    .line 98
    .line 99
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 100
    .line 101
    .line 102
    const-string v0, " | <u>\u6743\u9650\u8be6\u60c5</u> | <u>\u9690\u79c1\u534f\u8bae</u> | <u>\u529f\u80fd\u4ecb\u7ecd</u>"

    .line 103
    .line 104
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 105
    .line 106
    .line 107
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 108
    .line 109
    .line 110
    move-result-object v0

    .line 111
    iget-object v2, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 112
    .line 113
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 114
    .line 115
    .line 116
    move-result-object v4

    .line 117
    new-instance v9, Landroid/widget/TextView;

    .line 118
    .line 119
    invoke-direct {v9, v4}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 120
    .line 121
    .line 122
    invoke-static {v0}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    invoke-virtual {v9, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 127
    .line 128
    .line 129
    const/high16 v0, 0x40c00000    # 6.0f

    .line 130
    .line 131
    invoke-virtual {v9, v1, v0}, Landroid/widget/TextView;->setTextSize(IF)V

    .line 132
    .line 133
    .line 134
    const-string v0, "#999999"

    .line 135
    .line 136
    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    .line 137
    .line 138
    .line 139
    move-result v0

    .line 140
    invoke-virtual {v9, v0}, Landroid/widget/TextView;->setTextColor(I)V

    .line 141
    .line 142
    .line 143
    const-string v0, "#333333"

    .line 144
    .line 145
    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    .line 146
    .line 147
    .line 148
    move-result v0

    .line 149
    const/high16 v2, 0x3f800000    # 1.0f

    .line 150
    .line 151
    invoke-virtual {v9, v2, v2, v2, v0}, Landroid/widget/TextView;->setShadowLayer(FFFI)V

    .line 152
    .line 153
    .line 154
    const/16 v0, 0xa

    .line 155
    .line 156
    invoke-virtual {v9, v0, v0, v0, v0}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 157
    .line 158
    .line 159
    new-instance v2, Lcom/beizi/ad/v2/f/b$7;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_1

    .line 160
    .line 161
    move-object v3, p0

    .line 162
    :try_start_1
    invoke-direct/range {v2 .. v8}, Lcom/beizi/ad/v2/f/b$7;-><init>(Lcom/beizi/ad/v2/f/b;Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 163
    .line 164
    .line 165
    invoke-virtual {v9, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 166
    .line 167
    .line 168
    invoke-static {v4}, Lcom/beizi/ad/lance/a/q;->g(Landroid/content/Context;)I

    .line 169
    .line 170
    .line 171
    move-result v0

    .line 172
    mul-int/2addr v0, v1

    .line 173
    div-int/lit8 v0, v0, 0x3

    .line 174
    .line 175
    iget-object v1, v3, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 176
    .line 177
    instance-of v2, v1, Landroid/widget/FrameLayout;

    .line 178
    .line 179
    if-eqz v2, :cond_4

    .line 180
    .line 181
    check-cast v1, Landroid/widget/FrameLayout;

    .line 182
    .line 183
    new-instance v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 184
    .line 185
    const/4 v4, -0x2

    .line 186
    const/16 v5, 0x53

    .line 187
    .line 188
    invoke-direct {v2, v0, v4, v5}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    .line 189
    .line 190
    .line 191
    invoke-virtual {v1, v9, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    .line 192
    .line 193
    .line 194
    return-void

    .line 195
    :catch_0
    move-exception v0

    .line 196
    goto :goto_1

    .line 197
    :catch_1
    move-exception v0

    .line 198
    move-object v3, p0

    .line 199
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 200
    .line 201
    .line 202
    :cond_4
    :goto_2
    return-void
.end method

.method private B()V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    .line 8
    .line 9
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    .line 10
    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    const-string v0, "BeiZisAd"

    .line 14
    .line 15
    const-string v1, "enter BeiZi ad load"

    .line 16
    .line 17
    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    .line 21
    .line 22
    invoke-virtual {v0}, Lcom/beizi/ad/a;->a()V

    .line 23
    .line 24
    .line 25
    :cond_1
    :goto_0
    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/f/b;->y()V

    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/f/b;Z)Z
    .locals 0

    .line 2
    iput-boolean p1, p0, Lcom/beizi/ad/v2/a/b;->u:Z

    return p1
.end method

.method public static synthetic b(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/internal/f/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    return-object p0
.end method

.method public static synthetic c(Lcom/beizi/ad/v2/f/b;)Landroid/view/ViewGroup;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    return-object p0
.end method

.method public static synthetic d(Lcom/beizi/ad/v2/f/b;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/internal/f/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lcom/beizi/ad/v2/f/b;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/beizi/ad/v2/a/b;->t:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic g(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/internal/a/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic h(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic i(Lcom/beizi/ad/v2/f/b;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/f/b;->I:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic j(Lcom/beizi/ad/v2/f/b;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/f/b;->J:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic k(Lcom/beizi/ad/v2/f/b;)Landroid/os/Handler;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic l(Lcom/beizi/ad/v2/f/b;)Landroid/os/Handler;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic m(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/f/b;->z()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic n(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/f/b;->A()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic o(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/f/b;->B()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic p(Lcom/beizi/ad/v2/f/b;)Landroid/webkit/WebView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/f/b;->K:Landroid/webkit/WebView;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic q(Lcom/beizi/ad/v2/f/b;)Landroid/os/CountDownTimer;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/f/b;->F:Landroid/os/CountDownTimer;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic r(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/f/b;->v()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private v()V
    .locals 4

    .line 1
    const/4 v0, 0x3

    .line 2
    :try_start_0
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->H:Lcom/beizi/ad/model/e$f;

    .line 3
    .line 4
    sget-object v2, Lcom/beizi/ad/model/e$f;->c:Lcom/beizi/ad/model/e$f;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 5
    .line 6
    const-string v3, "http"

    .line 7
    .line 8
    if-ne v1, v2, :cond_2

    .line 9
    .line 10
    :try_start_1
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 11
    .line 12
    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 19
    .line 20
    .line 21
    return-void

    .line 22
    :catch_0
    move-exception v1

    .line 23
    goto :goto_0

    .line 24
    :cond_0
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 25
    .line 26
    invoke-virtual {v1, v3}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-nez v1, :cond_1

    .line 31
    .line 32
    const/4 v1, 0x7

    .line 33
    invoke-virtual {p0, v1}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_1
    invoke-direct {p0}, Lcom/beizi/ad/v2/f/b;->w()V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_2
    sget-object v2, Lcom/beizi/ad/model/e$f;->d:Lcom/beizi/ad/model/e$f;

    .line 42
    .line 43
    if-ne v1, v2, :cond_5

    .line 44
    .line 45
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 46
    .line 47
    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    if-eqz v1, :cond_3

    .line 52
    .line 53
    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 54
    .line 55
    .line 56
    return-void

    .line 57
    :cond_3
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 58
    .line 59
    invoke-virtual {v1, v3}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    .line 60
    .line 61
    .line 62
    move-result v1

    .line 63
    if-eqz v1, :cond_4

    .line 64
    .line 65
    const-string v1, "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Document</title>\n  <style>\n    html, body { width: 100%; height: 100%; margin: 0; padding: 0 }\n    .material-wrap { overflow: hidden; position: relative; height: 100%; background-repeat: no-repeat; background-position: center center; background-size: cover }\n    .filter-shadow { content: \"\"; position: absolute; z-index: 2; top: -3%; left: -3%; right: -3%; bottom: -3%; background: inherit; filter: blur(10px) }\n    .material-wrap .black-shadow { position: absolute; z-index: 3; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, .05) }\n    .material-wrap .material { position: absolute; z-index: 3; top: 0; left: 0; width: 100%; height: 100%; background-repeat: no-repeat; background-position: center center; background-size: contain }\n  </style>\n</head>\n<body>\n  <div class=\"material-wrap\" style=\"background-image: url(\'__IMAGE_SRC_PATH__\')\">\n    <div class=\"filter-shadow\"></div>\n    <div class=\"black-shadow\"></div>\n    <div class=\"material\" style=\"background-image: url(\'__IMAGE_SRC_PATH__\')\"></div>\n  </div>\n</body>\n</html>\n"

    .line 66
    .line 67
    const-string v2, "__IMAGE_SRC_PATH__"

    .line 68
    .line 69
    iget-object v3, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 70
    .line 71
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    iput-object v1, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 76
    .line 77
    :cond_4
    invoke-direct {p0}, Lcom/beizi/ad/v2/f/b;->x()V

    .line 78
    .line 79
    .line 80
    return-void

    .line 81
    :cond_5
    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/f/b;->b(I)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    .line 82
    .line 83
    .line 84
    return-void

    .line 85
    :goto_0
    invoke-virtual {v1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 86
    .line 87
    .line 88
    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 89
    .line 90
    .line 91
    return-void
.end method

.method private w()V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {v0}, Lcom/beizi/ad/internal/h/i;->a(Landroid/content/Context;)Lcom/beizi/ad/internal/h/i;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 7
    .line 8
    new-instance v2, Lcom/beizi/ad/v2/f/b$2;

    .line 9
    .line 10
    invoke-direct {v2, p0}, Lcom/beizi/ad/v2/f/b$2;-><init>(Lcom/beizi/ad/v2/f/b;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, Lcom/beizi/ad/internal/h/i;->a(Ljava/lang/String;Lcom/beizi/ad/internal/h/i$a;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method private x()V
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/16 v0, 0xa

    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    new-instance v1, Landroid/webkit/WebView;

    .line 16
    .line 17
    invoke-direct {v1, v0}, Landroid/webkit/WebView;-><init>(Landroid/content/Context;)V

    .line 18
    .line 19
    .line 20
    iput-object v1, p0, Lcom/beizi/ad/v2/f/b;->K:Landroid/webkit/WebView;

    .line 21
    .line 22
    invoke-static {v1}, Lcom/beizi/ad/internal/h/y;->a(Landroid/webkit/WebView;)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->K:Landroid/webkit/WebView;

    .line 26
    .line 27
    new-instance v1, Lcom/beizi/ad/v2/f/b$3;

    .line 28
    .line 29
    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/f/b$3;-><init>(Lcom/beizi/ad/v2/f/b;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {v0, v1}, Landroid/webkit/WebView;->setWebViewClient(Landroid/webkit/WebViewClient;)V

    .line 33
    .line 34
    .line 35
    iget-object v2, p0, Lcom/beizi/ad/v2/f/b;->K:Landroid/webkit/WebView;

    .line 36
    .line 37
    iget-object v4, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 38
    .line 39
    const-string v6, "UTF-8"

    .line 40
    .line 41
    const/4 v7, 0x0

    .line 42
    const/4 v3, 0x0

    .line 43
    const-string v5, "text/html"

    .line 44
    .line 45
    invoke-virtual/range {v2 .. v7}, Landroid/webkit/WebView;->loadDataWithBaseURL(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method private y()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->D:Landroid/view/View;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    const/4 v1, 0x0

    .line 7
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->D:Landroid/view/View;

    .line 11
    .line 12
    new-instance v1, Lcom/beizi/ad/v2/f/b$5;

    .line 13
    .line 14
    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/f/b$5;-><init>(Lcom/beizi/ad/v2/f/b;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method private z()V
    .locals 8

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto/16 :goto_2

    .line 6
    .line 7
    :cond_0
    instance-of v1, v0, Landroid/widget/FrameLayout;

    .line 8
    .line 9
    if-eqz v1, :cond_3

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    new-instance v1, Landroid/widget/LinearLayout;

    .line 16
    .line 17
    iget-object v2, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 18
    .line 19
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-direct {v1, v2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 24
    .line 25
    .line 26
    new-instance v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 27
    .line 28
    const/16 v3, 0x11

    .line 29
    .line 30
    const/4 v4, -0x2

    .line 31
    invoke-direct {v2, v4, v4, v3}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 35
    .line 36
    .line 37
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 38
    .line 39
    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->A()Lcom/beizi/ad/internal/f/c$a;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c$a;->a()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v5

    .line 47
    invoke-static {v5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 48
    .line 49
    .line 50
    move-result v5

    .line 51
    const/4 v6, 0x0

    .line 52
    if-nez v5, :cond_1

    .line 53
    .line 54
    new-instance v5, Landroid/content/MutableContextWrapper;

    .line 55
    .line 56
    invoke-direct {v5, v0}, Landroid/content/MutableContextWrapper;-><init>(Landroid/content/Context;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v5, v2}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;Lcom/beizi/ad/internal/f/c$a;)Landroid/widget/FrameLayout;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    if-eqz v2, :cond_1

    .line 64
    .line 65
    new-instance v5, Landroid/widget/FrameLayout$LayoutParams;

    .line 66
    .line 67
    invoke-direct {v5, v4, v4, v3}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {v1, v2, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 71
    .line 72
    .line 73
    invoke-virtual {v2, v6}, Landroid/view/View;->setVisibility(I)V

    .line 74
    .line 75
    .line 76
    new-instance v5, Lcom/beizi/ad/v2/f/b$6;

    .line 77
    .line 78
    invoke-direct {v5, p0}, Lcom/beizi/ad/v2/f/b$6;-><init>(Lcom/beizi/ad/v2/f/b;)V

    .line 79
    .line 80
    .line 81
    invoke-virtual {v2, v5}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 82
    .line 83
    .line 84
    goto :goto_0

    .line 85
    :catch_0
    move-exception v0

    .line 86
    goto :goto_1

    .line 87
    :cond_1
    :goto_0
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 88
    .line 89
    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->z()Lcom/beizi/ad/internal/f/c$a;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c$a;->a()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v5

    .line 97
    invoke-static {v5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 98
    .line 99
    .line 100
    move-result v5

    .line 101
    if-nez v5, :cond_2

    .line 102
    .line 103
    new-instance v5, Landroid/content/MutableContextWrapper;

    .line 104
    .line 105
    iget-object v7, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 106
    .line 107
    invoke-virtual {v7}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 108
    .line 109
    .line 110
    move-result-object v7

    .line 111
    invoke-direct {v5, v7}, Landroid/content/MutableContextWrapper;-><init>(Landroid/content/Context;)V

    .line 112
    .line 113
    .line 114
    invoke-static {v5, v2}, Lcom/beizi/ad/internal/h/v;->b(Landroid/content/Context;Lcom/beizi/ad/internal/f/c$a;)Landroid/widget/FrameLayout;

    .line 115
    .line 116
    .line 117
    move-result-object v2

    .line 118
    if-eqz v2, :cond_2

    .line 119
    .line 120
    new-instance v5, Landroid/widget/FrameLayout$LayoutParams;

    .line 121
    .line 122
    invoke-direct {v5, v4, v4, v3}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    .line 123
    .line 124
    .line 125
    invoke-virtual {v1, v2, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 126
    .line 127
    .line 128
    invoke-virtual {v2, v6}, Landroid/view/View;->setVisibility(I)V

    .line 129
    .line 130
    .line 131
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 132
    .line 133
    .line 134
    move-result-object v5

    .line 135
    check-cast v5, Landroid/widget/LinearLayout$LayoutParams;

    .line 136
    .line 137
    const/4 v7, 0x5

    .line 138
    invoke-virtual {v5, v7, v6, v6, v6}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 139
    .line 140
    .line 141
    iput v3, v5, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 142
    .line 143
    invoke-virtual {v2, v5}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 144
    .line 145
    .line 146
    :cond_2
    iget-object v2, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 147
    .line 148
    check-cast v2, Landroid/widget/FrameLayout;

    .line 149
    .line 150
    new-instance v3, Landroid/widget/FrameLayout$LayoutParams;

    .line 151
    .line 152
    const/16 v5, 0x55

    .line 153
    .line 154
    invoke-direct {v3, v4, v4, v5}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    .line 155
    .line 156
    .line 157
    invoke-virtual {v2, v1, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 158
    .line 159
    .line 160
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    check-cast v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 165
    .line 166
    const/high16 v3, 0x41400000    # 12.0f

    .line 167
    .line 168
    invoke-static {v0, v3}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    .line 169
    .line 170
    .line 171
    move-result v0

    .line 172
    invoke-virtual {v2, v6, v6, v0, v0}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 173
    .line 174
    .line 175
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 176
    .line 177
    .line 178
    return-void

    .line 179
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 180
    .line 181
    .line 182
    :cond_3
    :goto_2
    return-void
.end method


# virtual methods
.method public a(II)V
    .locals 0

    .line 4
    iput p1, p0, Lcom/beizi/ad/v2/f/b;->I:I

    .line 5
    iput p2, p0, Lcom/beizi/ad/v2/f/b;->J:I

    return-void
.end method

.method public a(Landroid/view/View$OnTouchListener;)V
    .locals 1

    if-nez p1, :cond_0

    goto :goto_0

    .line 10
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->K:Landroid/webkit/WebView;

    if-eqz v0, :cond_1

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    return-void

    .line 12
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    if-eqz v0, :cond_2

    .line 13
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public a(Landroid/view/View;)V
    .locals 1

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    if-nez v0, :cond_0

    const/16 p1, 0xb

    .line 7
    invoke-virtual {p0, p1}, Lcom/beizi/ad/v2/f/b;->b(I)V

    return-void

    .line 8
    :cond_0
    iput-object p1, p0, Lcom/beizi/ad/v2/f/b;->D:Landroid/view/View;

    .line 9
    new-instance p1, Lcom/beizi/ad/v2/f/b$1;

    invoke-direct {p1, p0}, Lcom/beizi/ad/v2/f/b$1;-><init>(Lcom/beizi/ad/v2/f/b;)V

    invoke-virtual {v0, p1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public a(Lcom/beizi/ad/a;)V
    .locals 0

    .line 3
    iput-object p1, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    return-void
.end method

.method public a(Lcom/beizi/ad/internal/f/c;)V
    .locals 1

    .line 25
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->s()I

    move-result v0

    iput v0, p0, Lcom/beizi/ad/v2/a/b;->n:I

    .line 26
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->S()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/f/b;->G:Ljava/lang/String;

    .line 27
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->T()Lcom/beizi/ad/model/e$f;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/f/b;->H:Lcom/beizi/ad/model/e$f;

    .line 28
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->e()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/a/b;->b(Ljava/lang/String;)V

    .line 29
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->f()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/a/b;->c(Ljava/lang/String;)V

    .line 30
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->P()Z

    move-result p1

    invoke-virtual {p0, p1}, Lcom/beizi/ad/v2/a/b;->a(Z)V

    .line 31
    iget-object p1, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    if-eqz p1, :cond_0

    .line 32
    new-instance v0, Lcom/beizi/ad/v2/f/b$9;

    invoke-direct {v0, p0}, Lcom/beizi/ad/v2/f/b$9;-><init>(Lcom/beizi/ad/v2/f/b;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_0
    return-void
.end method

.method public a(Lcom/beizi/ad/model/c;I)V
    .locals 11

    .line 14
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    if-eqz v0, :cond_2

    iget-object v1, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x1

    .line 15
    invoke-virtual {v0, v1}, Lcom/beizi/ad/internal/f/c;->a(Z)V

    .line 16
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {p0}, Lcom/beizi/ad/v2/a/b;->f()Z

    move-result v2

    invoke-virtual {v0, v2}, Lcom/beizi/ad/internal/f/c;->b(Z)V

    .line 17
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    iget-object v4, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 18
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v5

    invoke-static {v5, v6}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    move-result-object v6

    .line 19
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v7

    const-wide/16 v9, 0xa

    add-long/2addr v7, v9

    invoke-static {v7, v8}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    move-result-object v7

    iget-boolean v8, p0, Lcom/beizi/ad/v2/a/b;->i:Z

    iget-object v9, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    move-object v5, p1

    move v10, p2

    .line 20
    invoke-virtual/range {v3 .. v10}, Lcom/beizi/ad/internal/f/c;->a(Landroid/view/View;Lcom/beizi/ad/model/c;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;I)V

    .line 21
    iput-boolean v1, p0, Lcom/beizi/ad/v2/a/b;->i:Z

    .line 22
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    if-nez p1, :cond_1

    goto :goto_0

    .line 23
    :cond_1
    invoke-virtual {p1}, Lcom/beizi/ad/a;->e()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    move-object p1, v0

    .line 24
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_2
    :goto_0
    return-void
.end method

.method public b(I)V
    .locals 2

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    .line 8
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    if-eqz v0, :cond_1

    .line 9
    new-instance v1, Lcom/beizi/ad/v2/f/b$8;

    invoke-direct {v1, p0, p1}, Lcom/beizi/ad/v2/f/b$8;-><init>(Lcom/beizi/ad/v2/f/b;I)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public b(Landroid/view/View$OnTouchListener;)V
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->K:Landroid/webkit/WebView;

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    return-void

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    if-eqz v0, :cond_1

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    :cond_1
    return-void
.end method

.method public c()V
    .locals 1

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {v0}, Lcom/beizi/ad/a;->c()V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    .line 4
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->F:Landroid/os/CountDownTimer;

    if-eqz v0, :cond_1

    .line 5
    invoke-virtual {v0}, Landroid/os/CountDownTimer;->cancel()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 6
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_1
    return-void
.end method

.method public t()V
    .locals 7

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->F:Landroid/os/CountDownTimer;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/os/CountDownTimer;->cancel()V

    .line 6
    .line 7
    .line 8
    :cond_0
    iget v0, p0, Lcom/beizi/ad/v2/a/b;->n:I

    .line 9
    .line 10
    if-gtz v0, :cond_1

    .line 11
    .line 12
    const/4 v0, 0x5

    .line 13
    iput v0, p0, Lcom/beizi/ad/v2/a/b;->n:I

    .line 14
    .line 15
    :cond_1
    new-instance v1, Lcom/beizi/ad/v2/f/b$4;

    .line 16
    .line 17
    iget v0, p0, Lcom/beizi/ad/v2/a/b;->n:I

    .line 18
    .line 19
    mul-int/lit16 v0, v0, 0x3e8

    .line 20
    .line 21
    int-to-long v3, v0

    .line 22
    const-wide/16 v5, 0xc8

    .line 23
    .line 24
    move-object v2, p0

    .line 25
    invoke-direct/range {v1 .. v6}, Lcom/beizi/ad/v2/f/b$4;-><init>(Lcom/beizi/ad/v2/f/b;JJ)V

    .line 26
    .line 27
    .line 28
    iput-object v1, v2, Lcom/beizi/ad/v2/f/b;->F:Landroid/os/CountDownTimer;

    .line 29
    .line 30
    invoke-virtual {v1}, Landroid/os/CountDownTimer;->start()Landroid/os/CountDownTimer;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public u()V
    .locals 14

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-virtual {v0, v1}, Lcom/beizi/ad/internal/f/c;->a(Z)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 8
    .line 9
    invoke-virtual {p0}, Lcom/beizi/ad/v2/a/b;->f()Z

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    invoke-virtual {v0, v2}, Lcom/beizi/ad/internal/f/c;->b(Z)V

    .line 14
    .line 15
    .line 16
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 17
    .line 18
    iget-object v4, p0, Lcom/beizi/ad/v2/f/b;->C:Landroid/view/ViewGroup;

    .line 19
    .line 20
    const-string v5, "100"

    .line 21
    .line 22
    const-string v6, "200"

    .line 23
    .line 24
    const-string v7, "105"

    .line 25
    .line 26
    const-string v8, "206"

    .line 27
    .line 28
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 29
    .line 30
    .line 31
    move-result-wide v9

    .line 32
    invoke-static {v9, v10}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v9

    .line 36
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 37
    .line 38
    .line 39
    move-result-wide v10

    .line 40
    const-wide/16 v12, 0xa

    .line 41
    .line 42
    add-long/2addr v10, v12

    .line 43
    invoke-static {v10, v11}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v10

    .line 47
    iget-boolean v11, p0, Lcom/beizi/ad/v2/a/b;->i:Z

    .line 48
    .line 49
    iget-object v12, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    .line 50
    .line 51
    invoke-virtual/range {v3 .. v12}, Lcom/beizi/ad/internal/f/c;->a(Landroid/view/View;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;)V

    .line 52
    .line 53
    .line 54
    iput-boolean v1, p0, Lcom/beizi/ad/v2/a/b;->i:Z

    .line 55
    .line 56
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b;->E:Lcom/beizi/ad/a;

    .line 57
    .line 58
    if-nez v0, :cond_0

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/a;->e()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 62
    .line 63
    .line 64
    return-void

    .line 65
    :catch_0
    move-exception v0

    .line 66
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 67
    .line 68
    .line 69
    :goto_0
    return-void
.end method
