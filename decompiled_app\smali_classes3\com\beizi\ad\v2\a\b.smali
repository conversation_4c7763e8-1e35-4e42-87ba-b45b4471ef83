.class public Lcom/beizi/ad/v2/a/b;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field protected A:Z

.field protected B:Landroid/os/Handler;

.field private C:Ljava/lang/String;

.field private D:Ljava/lang/String;

.field protected a:Landroid/content/Context;

.field protected b:Lcom/beizi/ad/internal/e;

.field protected c:Lcom/beizi/ad/internal/f/c;

.field protected d:Z

.field protected e:Ljava/lang/String;

.field protected f:Ljava/lang/String;

.field protected g:Z

.field protected h:Z

.field protected i:Z

.field protected j:Z

.field protected k:Ljava/lang/String;

.field protected l:Ljava/lang/String;

.field protected m:Lcom/beizi/ad/internal/k;

.field protected n:I

.field protected o:I

.field protected p:I

.field protected q:I

.field protected r:Lcom/beizi/ad/internal/a/c;

.field protected s:Z

.field protected t:Z

.field protected u:Z

.field protected v:Z

.field protected w:Lcom/beizi/fusion/events/EventBean;

.field protected x:Z

.field protected y:Ljava/lang/String;

.field protected z:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/beizi/ad/internal/k;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    .line 4
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->h:Z

    .line 5
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->i:Z

    .line 6
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    .line 7
    new-instance v0, Lcom/beizi/ad/v2/a/b$1;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lcom/beizi/ad/v2/a/b$1;-><init>(Lcom/beizi/ad/v2/a/b;Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    .line 8
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->a:Landroid/content/Context;

    .line 9
    invoke-static {}, Lcom/beizi/ad/internal/h/p;->a()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    .line 10
    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->k:Ljava/lang/String;

    .line 11
    iput-object p2, p0, Lcom/beizi/ad/v2/a/b;->m:Lcom/beizi/ad/internal/k;

    .line 12
    new-instance v0, Lcom/beizi/ad/internal/e;

    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    invoke-direct {v0, p1, v1}, Lcom/beizi/ad/internal/e;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    .line 13
    invoke-virtual {v0, p2}, Lcom/beizi/ad/internal/e;->a(Lcom/beizi/ad/internal/k;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Ljava/lang/String;Lcom/beizi/ad/internal/k;)V
    .locals 2

    .line 14
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    const/4 v0, 0x0

    .line 16
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    .line 17
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->h:Z

    .line 18
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->i:Z

    .line 19
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    .line 20
    new-instance v0, Lcom/beizi/ad/v2/a/b$1;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lcom/beizi/ad/v2/a/b$1;-><init>(Lcom/beizi/ad/v2/a/b;Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    .line 21
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->a:Landroid/content/Context;

    .line 22
    invoke-static {}, Lcom/beizi/ad/internal/h/p;->a()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    .line 23
    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->k:Ljava/lang/String;

    .line 24
    iput-object p2, p0, Lcom/beizi/ad/v2/a/b;->y:Ljava/lang/String;

    .line 25
    iput-object p3, p0, Lcom/beizi/ad/v2/a/b;->m:Lcom/beizi/ad/internal/k;

    .line 26
    new-instance v0, Lcom/beizi/ad/internal/e;

    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    invoke-direct {v0, p1, v1}, Lcom/beizi/ad/internal/e;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    .line 27
    invoke-virtual {v0, p2}, Lcom/beizi/ad/internal/e;->b(Ljava/lang/String;)V

    .line 28
    iget-object p1, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    invoke-virtual {p1, p3}, Lcom/beizi/ad/internal/e;->a(Lcom/beizi/ad/internal/k;)V

    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/a/b;ZLjava/lang/String;Lcom/beizi/ad/internal/f/c;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2, p3}, Lcom/beizi/ad/v2/a/b;->a(ZLjava/lang/String;Lcom/beizi/ad/internal/f/c;)V

    return-void
.end method

.method private declared-synchronized a(ZLjava/lang/String;Lcom/beizi/ad/internal/f/c;)V
    .locals 2

    monitor-enter p0

    .line 20
    :try_start_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->A:Z

    const/4 v1, 0x1

    if-eqz v0, :cond_2

    if-eqz p1, :cond_0

    .line 21
    invoke-virtual {p0, p3, p2}, Lcom/beizi/ad/v2/a/b;->a(Lcom/beizi/ad/internal/f/c;Ljava/lang/String;)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    .line 22
    :cond_0
    iget-boolean p1, p0, Lcom/beizi/ad/v2/a/b;->s:Z

    if-eqz p1, :cond_1

    .line 23
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    move-result-object p1

    iget-object p2, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    iget p3, p0, Lcom/beizi/ad/v2/a/b;->q:I

    invoke-virtual {p1, p2, v1, p3}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/a/c;II)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 24
    :cond_1
    :goto_0
    monitor-exit p0

    return-void

    .line 25
    :cond_2
    :try_start_1
    iput-boolean v1, p0, Lcom/beizi/ad/v2/a/b;->A:Z

    .line 26
    iput-object p3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    if-eqz p1, :cond_3

    .line 27
    iput-boolean v1, p0, Lcom/beizi/ad/v2/a/b;->s:Z

    goto :goto_1

    .line 28
    :cond_3
    iput-boolean v1, p0, Lcom/beizi/ad/v2/a/b;->t:Z

    .line 29
    iget-object p1, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    invoke-virtual {p1}, Lcom/beizi/ad/internal/a/c;->d()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    .line 30
    iget-object p2, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    if-eqz p2, :cond_4

    .line 31
    invoke-virtual {p2, p1}, Lcom/beizi/ad/internal/e;->a(Ljava/lang/String;)V

    .line 32
    :cond_4
    iget-object p1, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {p1, v1}, Lcom/beizi/ad/internal/f/c;->c(Z)V

    .line 33
    :goto_1
    invoke-virtual {p0, p3}, Lcom/beizi/ad/v2/a/b;->a(Lcom/beizi/ad/internal/f/c;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 34
    monitor-exit p0

    return-void

    :goto_2
    :try_start_2
    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    throw p1
.end method

.method private t()V
    .locals 5

    .line 1
    iget v0, p0, Lcom/beizi/ad/v2/a/b;->q:I

    .line 2
    .line 3
    if-gtz v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    invoke-direct {p0}, Lcom/beizi/ad/v2/a/b;->u()V

    .line 7
    .line 8
    .line 9
    iget v0, p0, Lcom/beizi/ad/v2/a/b;->p:I

    .line 10
    .line 11
    if-gtz v0, :cond_1

    .line 12
    .line 13
    invoke-virtual {p0}, Lcom/beizi/ad/v2/a/b;->n()Z

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_1
    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    .line 18
    .line 19
    if-eqz v1, :cond_2

    .line 20
    .line 21
    const/16 v2, 0x10

    .line 22
    .line 23
    int-to-long v3, v0

    .line 24
    invoke-virtual {v1, v2, v3, v4}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    .line 25
    .line 26
    .line 27
    :cond_2
    :goto_0
    return-void
.end method

.method private u()V
    .locals 5

    .line 1
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->w:Lcom/beizi/fusion/events/EventBean;

    .line 6
    .line 7
    iget v2, p0, Lcom/beizi/ad/v2/a/b;->q:I

    .line 8
    .line 9
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->y:Ljava/lang/String;

    .line 10
    .line 11
    iget-object v4, p0, Lcom/beizi/ad/v2/a/b;->m:Lcom/beizi/ad/internal/k;

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2, v3, v4}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/fusion/events/EventBean;ILjava/lang/String;Lcom/beizi/ad/internal/k;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method private v()V
    .locals 4

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->v:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->t:Z

    .line 7
    .line 8
    if-eqz v0, :cond_1

    .line 9
    .line 10
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->u:Z

    .line 11
    .line 12
    if-nez v0, :cond_1

    .line 13
    .line 14
    const/4 v0, 0x1

    .line 15
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->v:Z

    .line 16
    .line 17
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 22
    .line 23
    iget v3, p0, Lcom/beizi/ad/v2/a/b;->q:I

    .line 24
    .line 25
    invoke-virtual {v1, v2, v0, v3}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/a/c;II)V

    .line 26
    .line 27
    .line 28
    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public a()Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;
    .locals 1

    .line 12
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->z:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    return-object v0
.end method

.method public a(I)V
    .locals 0

    .line 3
    iput p1, p0, Lcom/beizi/ad/v2/a/b;->o:I

    return-void
.end method

.method public a(Lcom/beizi/ad/internal/f/c;)V
    .locals 0

    .line 1
    return-void
.end method

.method public a(Lcom/beizi/ad/internal/f/c;Ljava/lang/String;)V
    .locals 7

    if-eqz p1, :cond_1

    .line 35
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->h()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 36
    :cond_0
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    move-result-object v1

    iget-object v4, p0, Lcom/beizi/ad/v2/a/b;->k:Ljava/lang/String;

    iget-object v5, p0, Lcom/beizi/ad/v2/a/b;->y:Ljava/lang/String;

    iget-object v6, p0, Lcom/beizi/ad/v2/a/b;->m:Lcom/beizi/ad/internal/k;

    move-object v2, p1

    move-object v3, p2

    invoke-virtual/range {v1 .. v6}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/f/c;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/beizi/ad/internal/k;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public a(Lcom/beizi/fusion/events/EventBean;)V
    .locals 0

    .line 13
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->w:Lcom/beizi/fusion/events/EventBean;

    return-void
.end method

.method public a(Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;)V
    .locals 3

    if-nez p1, :cond_0

    goto :goto_1

    .line 4
    :cond_0
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->z:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    .line 5
    invoke-virtual {p1}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getCacheNum()I

    move-result v0

    iput v0, p0, Lcom/beizi/ad/v2/a/b;->q:I

    if-lez v0, :cond_3

    .line 6
    invoke-virtual {p1}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getWaitTime()I

    move-result v0

    .line 7
    invoke-virtual {p1}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getLastTime()I

    move-result p1

    const/16 v1, 0x64

    if-gtz p1, :cond_1

    move p1, v1

    :cond_1
    if-ltz v0, :cond_2

    .line 8
    iget v2, p0, Lcom/beizi/ad/v2/a/b;->o:I

    sub-int/2addr v2, p1

    invoke-static {v0, v2}, Ljava/lang/Math;->min(II)I

    move-result p1

    iput p1, p0, Lcom/beizi/ad/v2/a/b;->p:I

    goto :goto_0

    .line 9
    :cond_2
    iget v0, p0, Lcom/beizi/ad/v2/a/b;->o:I

    sub-int/2addr v0, p1

    iput v0, p0, Lcom/beizi/ad/v2/a/b;->p:I

    .line 10
    :goto_0
    iget p1, p0, Lcom/beizi/ad/v2/a/b;->p:I

    if-gtz p1, :cond_3

    .line 11
    iput v1, p0, Lcom/beizi/ad/v2/a/b;->p:I

    :cond_3
    :goto_1
    return-void
.end method

.method public a(Ljava/lang/String;)V
    .locals 1

    .line 14
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->y:Ljava/lang/String;

    .line 15
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    if-eqz v0, :cond_0

    .line 16
    invoke-virtual {v0, p1}, Lcom/beizi/ad/internal/e;->b(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public a(Ljava/util/Map;)V
    .locals 1

    .line 18
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    if-eqz v0, :cond_1

    if-nez p1, :cond_0

    goto :goto_0

    .line 19
    :cond_0
    invoke-virtual {v0, p1}, Lcom/beizi/ad/internal/f/c;->a(Ljava/util/Map;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public a(Z)V
    .locals 0

    .line 17
    iput-boolean p1, p0, Lcom/beizi/ad/v2/a/b;->d:Z

    return-void
.end method

.method public a(Lcom/beizi/ad/model/e$a;)Z
    .locals 4

    .line 37
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->m:Lcom/beizi/ad/internal/k;

    sget-object v1, Lcom/beizi/ad/internal/k;->b:Lcom/beizi/ad/internal/k;

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-eq v0, v1, :cond_7

    sget-object v1, Lcom/beizi/ad/internal/k;->c:Lcom/beizi/ad/internal/k;

    if-eq v0, v1, :cond_7

    sget-object v1, Lcom/beizi/ad/internal/k;->a:Lcom/beizi/ad/internal/k;

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 38
    :cond_0
    sget-object v1, Lcom/beizi/ad/internal/k;->g:Lcom/beizi/ad/internal/k;

    if-ne v0, v1, :cond_2

    .line 39
    sget-object v0, Lcom/beizi/ad/model/e$a;->g:Lcom/beizi/ad/model/e$a;

    if-eq p1, v0, :cond_1

    return v3

    :cond_1
    return v2

    .line 40
    :cond_2
    sget-object v1, Lcom/beizi/ad/internal/k;->e:Lcom/beizi/ad/internal/k;

    if-ne v0, v1, :cond_4

    .line 41
    sget-object v0, Lcom/beizi/ad/model/e$a;->d:Lcom/beizi/ad/model/e$a;

    if-eq p1, v0, :cond_3

    return v3

    :cond_3
    return v2

    .line 42
    :cond_4
    sget-object v1, Lcom/beizi/ad/internal/k;->f:Lcom/beizi/ad/internal/k;

    if-ne v0, v1, :cond_6

    .line 43
    sget-object v0, Lcom/beizi/ad/model/e$a;->b:Lcom/beizi/ad/model/e$a;

    if-eq p1, v0, :cond_5

    return v3

    :cond_5
    return v2

    :cond_6
    return v3

    .line 44
    :cond_7
    :goto_0
    sget-object v0, Lcom/beizi/ad/model/e$a;->c:Lcom/beizi/ad/model/e$a;

    if-eq p1, v0, :cond_8

    return v3

    :cond_8
    return v2
.end method

.method public b()V
    .locals 6

    .line 2
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->h:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    .line 3
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->h:Z

    .line 4
    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->C:Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    const/4 v2, 0x3

    if-nez v1, :cond_3

    .line 5
    new-instance v1, Lcom/beizi/ad/internal/f/c;

    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->C:Ljava/lang/String;

    iget-object v4, p0, Lcom/beizi/ad/v2/a/b;->b:Lcom/beizi/ad/internal/e;

    .line 6
    invoke-virtual {v4}, Lcom/beizi/ad/internal/e;->i()Lcom/beizi/ad/internal/k;

    move-result-object v4

    const/4 v5, 0x0

    invoke-direct {v1, v3, v5, v4}, Lcom/beizi/ad/internal/f/c;-><init>(Ljava/lang/String;Ljava/util/Map;Lcom/beizi/ad/internal/k;)V

    .line 7
    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->h()Z

    move-result v3

    if-nez v3, :cond_1

    .line 8
    invoke-virtual {p0, v2}, Lcom/beizi/ad/v2/a/b;->b(I)V

    return-void

    .line 9
    :cond_1
    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->x()Lcom/beizi/ad/model/e$a;

    move-result-object v2

    invoke-virtual {p0, v2}, Lcom/beizi/ad/v2/a/b;->a(Lcom/beizi/ad/model/e$a;)Z

    move-result v2

    if-nez v2, :cond_2

    const/16 v0, 0xc

    .line 10
    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/a/b;->b(I)V

    return-void

    .line 11
    :cond_2
    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->aa()Ljava/lang/String;

    move-result-object v2

    iput-object v2, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    .line 12
    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->W()Ljava/lang/String;

    move-result-object v2

    .line 13
    invoke-direct {p0, v0, v2, v1}, Lcom/beizi/ad/v2/a/b;->a(ZLjava/lang/String;Lcom/beizi/ad/internal/f/c;)V

    return-void

    .line 14
    :cond_3
    const-string v0, "S2S"

    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->D:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 15
    invoke-virtual {p0, v2}, Lcom/beizi/ad/v2/a/b;->b(I)V

    return-void

    .line 16
    :cond_4
    invoke-direct {p0}, Lcom/beizi/ad/v2/a/b;->t()V

    .line 17
    invoke-static {}, Lcom/beizi/ad/lance/a/c;->b()Lcom/beizi/ad/lance/a/c;

    move-result-object v0

    invoke-virtual {v0}, Lcom/beizi/ad/lance/a/c;->c()Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    new-instance v1, Lcom/beizi/ad/v2/a/b$2;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/a/b$2;-><init>(Lcom/beizi/ad/v2/a/b;)V

    invoke-interface {v0, v1}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public b(I)V
    .locals 0

    .line 1
    return-void
.end method

.method public b(Ljava/lang/String;)V
    .locals 0

    .line 18
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->e:Ljava/lang/String;

    return-void
.end method

.method public b(Ljava/util/Map;)V
    .locals 1

    .line 19
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    if-eqz v0, :cond_1

    if-nez p1, :cond_0

    goto :goto_0

    .line 20
    :cond_0
    invoke-direct {p0}, Lcom/beizi/ad/v2/a/b;->v()V

    .line 21
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0, p1}, Lcom/beizi/ad/internal/f/c;->b(Ljava/util/Map;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public c()V
    .locals 0

    .line 1
    return-void
.end method

.method public c(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->f:Ljava/lang/String;

    return-void
.end method

.method public d()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->y:Ljava/lang/String;

    return-object v0
.end method

.method public d(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->C:Ljava/lang/String;

    return-void
.end method

.method public e()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    return-object v0
.end method

.method public e(Ljava/lang/String;)V
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b;->D:Ljava/lang/String;

    return-void
.end method

.method public f()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->x:Z

    .line 2
    .line 3
    return v0
.end method

.method public g()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->e:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public h()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->f:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public i()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    .line 2
    .line 3
    return v0
.end method

.method public j()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->d:Z

    .line 2
    .line 3
    return v0
.end method

.method public k()Ljava/util/Map;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    return-object v0

    .line 7
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->Q()Ljava/util/Map;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public l()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    return-object v0

    .line 7
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->R()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public m()Lcom/beizi/ad/internal/f/c;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public n()Z
    .locals 5

    .line 1
    iget v0, p0, Lcom/beizi/ad/v2/a/b;->q:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-gtz v0, :cond_0

    .line 5
    .line 6
    return v1

    .line 7
    :cond_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->s:Z

    .line 8
    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    return v1

    .line 12
    :cond_1
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {p0}, Lcom/beizi/ad/v2/a/b;->d()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-virtual {v0, v2}, Lcom/beizi/ad/internal/a/a;->b(Ljava/lang/String;)Lcom/beizi/ad/internal/a/c;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    iput-object v0, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 25
    .line 26
    if-nez v0, :cond_2

    .line 27
    .line 28
    return v1

    .line 29
    :cond_2
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 34
    .line 35
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->m:Lcom/beizi/ad/internal/k;

    .line 36
    .line 37
    invoke-virtual {v0, v2, v3}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/a/c;Lcom/beizi/ad/internal/k;)Lcom/beizi/ad/internal/f/c;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    if-nez v0, :cond_3

    .line 42
    .line 43
    return v1

    .line 44
    :cond_3
    iget-boolean v2, p0, Lcom/beizi/ad/v2/a/b;->s:Z

    .line 45
    .line 46
    const/4 v3, 0x1

    .line 47
    if-eqz v2, :cond_4

    .line 48
    .line 49
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 54
    .line 55
    iget v4, p0, Lcom/beizi/ad/v2/a/b;->q:I

    .line 56
    .line 57
    invoke-virtual {v0, v2, v3, v4}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/a/c;II)V

    .line 58
    .line 59
    .line 60
    return v1

    .line 61
    :cond_4
    const/4 v2, 0x0

    .line 62
    invoke-direct {p0, v1, v2, v0}, Lcom/beizi/ad/v2/a/b;->a(ZLjava/lang/String;Lcom/beizi/ad/internal/f/c;)V

    .line 63
    .line 64
    .line 65
    return v3
.end method

.method public o()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->t:Z

    .line 2
    .line 3
    return v0
.end method

.method public p()J
    .locals 3

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->t:Z

    .line 2
    .line 3
    const-wide/16 v1, 0x0

    .line 4
    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    return-wide v1

    .line 8
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 9
    .line 10
    if-nez v0, :cond_1

    .line 11
    .line 12
    return-wide v1

    .line 13
    :cond_1
    invoke-virtual {v0}, Lcom/beizi/ad/internal/a/c;->b()J

    .line 14
    .line 15
    .line 16
    move-result-wide v0

    .line 17
    return-wide v0
.end method

.method public q()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/a/b;->v()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lcom/beizi/ad/v2/a/b;->r()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public r()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/16 v1, 0x10

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public s()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/beizi/ad/v2/a/b;->n:I

    .line 2
    .line 3
    if-gtz v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x5

    .line 6
    :cond_0
    return v0
.end method
