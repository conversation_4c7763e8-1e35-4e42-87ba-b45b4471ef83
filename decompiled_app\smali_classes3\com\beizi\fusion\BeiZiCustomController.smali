.class public abstract Lcom/beizi/fusion/BeiZiCustomController;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public forbidSensor()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public getDevOaid()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getLocation()Lcom/beizi/ad/model/BeiZiLocation;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getOaidVersion()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public isCanUseAppList()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public isCanUseGaid()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public isCanUseLocation()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public isCanUseOaid()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public isCanUsePhoneState()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public isCanUseWifiState()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
