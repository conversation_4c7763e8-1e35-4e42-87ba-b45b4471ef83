.class public abstract Ltop/cycdm/cycapp/ui/history/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ltop/cycdm/cycapp/ui/history/a$a;,
        Ltop/cycdm/cycapp/ui/history/a$b;,
        Ltop/cycdm/cycapp/ui/history/a$c;,
        Ltop/cycdm/cycapp/ui/history/a$d;
    }
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ltop/cycdm/cycapp/ui/history/a;-><init>()V

    return-void
.end method
