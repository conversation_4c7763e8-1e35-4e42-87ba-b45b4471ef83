.class final Lcom/kwad/components/ct/detail/ad/presenter/a/b$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/kwad/components/core/webview/jshandler/at$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/a/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$2;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/kwad/components/core/webview/jshandler/at$a;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$2;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 2
    .line 3
    iget p1, p1, Lcom/kwad/components/core/webview/jshandler/at$a;->status:I

    .line 4
    .line 5
    invoke-static {v0, p1}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->a(Lcom/kwad/components/ct/detail/ad/presenter/a/b;I)I

    .line 6
    .line 7
    .line 8
    new-instance p1, Ljava/lang/StringBuilder;

    .line 9
    .line 10
    const-string v0, "position:"

    .line 11
    .line 12
    invoke-direct {p1, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$2;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 16
    .line 17
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->w(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)Lcom/kwad/components/ct/detail/c;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    iget v0, v0, Lcom/kwad/components/ct/detail/c;->Zh:I

    .line 22
    .line 23
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 24
    .line 25
    .line 26
    const-string v0, " load time:"

    .line 27
    .line 28
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 29
    .line 30
    .line 31
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 32
    .line 33
    .line 34
    move-result-wide v0

    .line 35
    iget-object v2, p0, Lcom/kwad/components/ct/detail/ad/presenter/a/b$2;->amZ:Lcom/kwad/components/ct/detail/ad/presenter/a/b;

    .line 36
    .line 37
    invoke-static {v2}, Lcom/kwad/components/ct/detail/ad/presenter/a/b;->x(Lcom/kwad/components/ct/detail/ad/presenter/a/b;)J

    .line 38
    .line 39
    .line 40
    move-result-wide v2

    .line 41
    sub-long/2addr v0, v2

    .line 42
    invoke-virtual {p1, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 43
    .line 44
    .line 45
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    const-string v0, "ActionBarWebCard"

    .line 50
    .line 51
    invoke-static {v0, p1}, Lcom/kwad/sdk/core/d/c;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    return-void
.end method
