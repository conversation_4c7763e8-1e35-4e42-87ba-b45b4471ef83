.class public final Lcom/kwad/components/ct/detail/b/d;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# static fields
.field private static avO:Landroid/view/animation/AccelerateDecelerateInterpolator;


# instance fields
.field private abZ:Landroid/os/Handler;

.field private alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

.field private alR:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/kwad/sdk/widget/swipe/a;",
            ">;"
        }
    .end annotation
.end field

.field private alX:Lcom/kwad/components/ct/detail/e/a;

.field private amm:Landroid/view/View;

.field private amo:Lcom/kwad/components/core/j/a;

.field private apE:Landroidx/recyclerview/widget/RecyclerView$OnScrollListener;

.field private aqv:Z

.field private ate:Lcom/kwad/sdk/widget/swipe/c;

.field private avA:I

.field private avB:Ljava/lang/Float;

.field private avC:I

.field private avD:I

.field private avE:Landroid/view/View;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private avF:Z

.field private avG:Lcom/kwad/components/ct/api/a/a/c;

.field private avH:Z

.field private final avI:Lcom/kwad/sdk/l/a/b;

.field private avJ:Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager$a;

.field private final avK:Lcom/kwad/sdk/widget/swipe/a;

.field private avL:Lcom/kwad/components/ct/api/a/a/b;

.field private avM:Landroid/view/View;

.field private avN:Lcom/kwad/lottie/LottieAnimationView;

.field private avP:Ljava/lang/Runnable;

.field private avQ:Ljava/lang/Runnable;

.field private avl:Landroid/view/View;

.field private avm:Landroid/view/View;

.field private avn:Landroidx/recyclerview/widget/RecyclerView;

.field private avo:Landroid/widget/TextView;

.field private avp:I

.field private avq:Landroid/view/View;

.field private avr:Landroid/widget/ImageView;

.field private avs:Landroid/widget/TextView;

.field private avt:Landroid/widget/TextView;

.field private avu:F

.field private avv:I

.field private avw:I

.field private avx:I

.field private avy:I

.field private avz:I

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mNetworking:Lcom/kwad/sdk/core/network/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/kwad/sdk/core/network/l<",
            "Lcom/kwad/components/ct/request/r;",
            "Lcom/kwad/components/ct/profile/home/<USER>/ProfileResultData;",
            ">;"
        }
    .end annotation
.end field

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Landroid/view/animation/AccelerateDecelerateInterpolator;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/view/animation/AccelerateDecelerateInterpolator;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lcom/kwad/components/ct/detail/b/d;->avO:Landroid/view/animation/AccelerateDecelerateInterpolator;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/high16 v0, 0x3f800000    # 1.0f

    .line 5
    .line 6
    iput v0, p0, Lcom/kwad/components/ct/detail/b/d;->avu:F

    .line 7
    .line 8
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$8;

    .line 9
    .line 10
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$8;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 11
    .line 12
    .line 13
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avI:Lcom/kwad/sdk/l/a/b;

    .line 14
    .line 15
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$9;

    .line 16
    .line 17
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$9;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 18
    .line 19
    .line 20
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avJ:Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager$a;

    .line 21
    .line 22
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$10;

    .line 23
    .line 24
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$10;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 25
    .line 26
    .line 27
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 28
    .line 29
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$11;

    .line 30
    .line 31
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$11;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 32
    .line 33
    .line 34
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->amo:Lcom/kwad/components/core/j/a;

    .line 35
    .line 36
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$12;

    .line 37
    .line 38
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$12;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 39
    .line 40
    .line 41
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avK:Lcom/kwad/sdk/widget/swipe/a;

    .line 42
    .line 43
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$13;

    .line 44
    .line 45
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$13;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 46
    .line 47
    .line 48
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avL:Lcom/kwad/components/ct/api/a/a/b;

    .line 49
    .line 50
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$2;

    .line 51
    .line 52
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$2;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 53
    .line 54
    .line 55
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avP:Ljava/lang/Runnable;

    .line 56
    .line 57
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$3;

    .line 58
    .line 59
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$3;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 60
    .line 61
    .line 62
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avQ:Ljava/lang/Runnable;

    .line 63
    .line 64
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$4;

    .line 65
    .line 66
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$4;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 67
    .line 68
    .line 69
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->apE:Landroidx/recyclerview/widget/RecyclerView$OnScrollListener;

    .line 70
    .line 71
    new-instance v0, Landroid/os/Handler;

    .line 72
    .line 73
    invoke-direct {v0}, Landroid/os/Handler;-><init>()V

    .line 74
    .line 75
    .line 76
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->abZ:Landroid/os/Handler;

    .line 77
    .line 78
    return-void
.end method

.method public static synthetic A(Lcom/kwad/components/ct/detail/b/d;)Landroid/os/Handler;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/d;->abZ:Landroid/os/Handler;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic B(Lcom/kwad/components/ct/detail/b/d;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private BA()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avM:Landroid/view/View;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avN:Lcom/kwad/lottie/LottieAnimationView;

    .line 7
    .line 8
    const/16 v1, 0x8

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method private BB()V
    .locals 10

    .line 1
    iget v0, p0, Lcom/kwad/components/ct/detail/b/d;->avu:F

    .line 2
    .line 3
    const/high16 v1, 0x3f800000    # 1.0f

    .line 4
    .line 5
    cmpl-float v1, v0, v1

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    const/4 v3, 0x0

    .line 9
    if-nez v1, :cond_1

    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avG:Lcom/kwad/components/ct/api/a/a/c;

    .line 12
    .line 13
    invoke-interface {v0}, Lcom/kwad/components/ct/api/a/a/c;->wv()Lcom/kwad/components/ct/api/a/a/a;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-interface {v0, v1}, Lcom/kwad/components/ct/api/a/a/c;->a(Lcom/kwad/components/ct/api/a/a/a;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_0

    .line 22
    .line 23
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 24
    .line 25
    iput v3, v0, Lcom/kwad/sdk/core/response/model/AdTemplate;->mIsLeftSlipStatus:I

    .line 26
    .line 27
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 28
    .line 29
    invoke-virtual {v1, v0, v3, v3}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->a(Lcom/kwad/components/ct/response/model/CtAdTemplate;IZ)V

    .line 30
    .line 31
    .line 32
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 33
    .line 34
    invoke-virtual {v0, v2}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->setEnabled(Z)V

    .line 35
    .line 36
    .line 37
    return-void

    .line 38
    :cond_1
    const/4 v1, 0x0

    .line 39
    cmpl-float v0, v0, v1

    .line 40
    .line 41
    if-nez v0, :cond_5

    .line 42
    .line 43
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avn:Landroidx/recyclerview/widget/RecyclerView;

    .line 44
    .line 45
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    check-cast v0, Lcom/kwad/sdk/lib/widget/a/d;

    .line 50
    .line 51
    invoke-virtual {v0}, Lcom/kwad/sdk/lib/widget/a/d;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    check-cast v0, Lcom/kwad/components/ct/home/<USER>/b;

    .line 56
    .line 57
    invoke-virtual {v0}, Lcom/kwad/components/ct/home/<USER>/b;->Gs()Lcom/kwad/components/ct/home/<USER>/c;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-virtual {v1}, Lcom/kwad/components/ct/home/<USER>/a;->isEmpty()Z

    .line 62
    .line 63
    .line 64
    move-result v4

    .line 65
    if-eqz v4, :cond_2

    .line 66
    .line 67
    goto :goto_1

    .line 68
    :cond_2
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/d;->avG:Lcom/kwad/components/ct/api/a/a/c;

    .line 69
    .line 70
    invoke-interface {v4, v1}, Lcom/kwad/components/ct/api/a/a/c;->a(Lcom/kwad/components/ct/api/a/a/a;)Z

    .line 71
    .line 72
    .line 73
    move-result v4

    .line 74
    if-eqz v4, :cond_3

    .line 75
    .line 76
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 77
    .line 78
    iput v2, v4, Lcom/kwad/sdk/core/response/model/AdTemplate;->mIsLeftSlipStatus:I

    .line 79
    .line 80
    iget-object v5, p0, Lcom/kwad/components/ct/detail/b/d;->avG:Lcom/kwad/components/ct/api/a/a/c;

    .line 81
    .line 82
    invoke-interface {v5, v4}, Lcom/kwad/components/ct/api/a/a/c;->aX(Lcom/kwad/sdk/core/response/model/AdTemplate;)I

    .line 83
    .line 84
    .line 85
    move-result v4

    .line 86
    invoke-virtual {v0, v4}, Lcom/kwad/components/ct/home/<USER>/b;->cd(I)V

    .line 87
    .line 88
    .line 89
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 90
    .line 91
    iget-object v4, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 92
    .line 93
    invoke-virtual {v0, v4, v2, v3}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->a(Lcom/kwad/components/ct/response/model/CtAdTemplate;IZ)V

    .line 94
    .line 95
    .line 96
    goto :goto_0

    .line 97
    :cond_3
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 98
    .line 99
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;->getAdapter()Lcom/kwad/components/ct/detail/viewpager/b;

    .line 100
    .line 101
    .line 102
    move-result-object v4

    .line 103
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avG:Lcom/kwad/components/ct/api/a/a/c;

    .line 104
    .line 105
    invoke-interface {v0}, Lcom/kwad/components/ct/api/a/a/c;->wu()Ljava/util/List;

    .line 106
    .line 107
    .line 108
    move-result-object v5

    .line 109
    iget-object v6, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 110
    .line 111
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avG:Lcom/kwad/components/ct/api/a/a/c;

    .line 112
    .line 113
    invoke-interface {v0, v6}, Lcom/kwad/components/ct/api/a/a/c;->aX(Lcom/kwad/sdk/core/response/model/AdTemplate;)I

    .line 114
    .line 115
    .line 116
    move-result v8

    .line 117
    const/4 v9, 0x0

    .line 118
    const/4 v7, 0x1

    .line 119
    invoke-virtual/range {v4 .. v9}, Lcom/kwad/components/ct/detail/viewpager/b;->a(Ljava/util/List;Lcom/kwad/components/ct/response/model/CtAdTemplate;IIZ)V

    .line 120
    .line 121
    .line 122
    :goto_0
    invoke-virtual {v1}, Lcom/kwad/components/ct/home/<USER>/a;->wu()Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 127
    .line 128
    .line 129
    move-result v0

    .line 130
    if-gt v0, v2, :cond_4

    .line 131
    .line 132
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 133
    .line 134
    invoke-virtual {v0, v3}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->setEnabled(Z)V

    .line 135
    .line 136
    .line 137
    return-void

    .line 138
    :cond_4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->alI:Lcom/kwad/components/ct/detail/viewpager/SlidePlayViewPager;

    .line 139
    .line 140
    invoke-virtual {v0, v2}, Lcom/kwad/components/ct/detail/viewpager/SlidePlayTouchViewPager;->setEnabled(Z)V

    .line 141
    .line 142
    .line 143
    :cond_5
    :goto_1
    return-void
.end method

.method private BC()V
    .locals 6

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/response/model/CtAdTemplate;->photoInfo:Lcom/kwad/components/ct/response/model/CtPhotoInfo;

    .line 4
    .line 5
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/c;->e(Lcom/kwad/components/ct/response/model/CtPhotoInfo;)J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/d;->avt:Landroid/widget/TextView;

    .line 10
    .line 11
    iget v3, p0, Lcom/kwad/components/ct/detail/b/d;->avp:I

    .line 12
    .line 13
    invoke-virtual {v2, v3}, Landroid/view/View;->getTag(I)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    check-cast v2, Ljava/lang/String;

    .line 18
    .line 19
    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    if-nez v3, :cond_0

    .line 24
    .line 25
    invoke-static {v0, v1}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    if-eqz v2, :cond_0

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    iget-boolean v2, p0, Lcom/kwad/components/ct/detail/b/d;->aqv:Z

    .line 37
    .line 38
    if-nez v2, :cond_3

    .line 39
    .line 40
    iget-boolean v2, p0, Lcom/kwad/components/ct/detail/b/d;->avF:Z

    .line 41
    .line 42
    if-nez v2, :cond_1

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 46
    .line 47
    iget-object v2, v2, Lcom/kwad/sdk/core/response/model/AdTemplate;->mAdScene:Lcom/kwad/sdk/internal/api/SceneImpl;

    .line 48
    .line 49
    if-nez v2, :cond_2

    .line 50
    .line 51
    goto :goto_0

    .line 52
    :cond_2
    const/4 v3, 0x1

    .line 53
    iput-boolean v3, p0, Lcom/kwad/components/ct/detail/b/d;->aqv:Z

    .line 54
    .line 55
    new-instance v3, Lcom/kwad/components/core/request/model/ImpInfo;

    .line 56
    .line 57
    invoke-direct {v3, v2}, Lcom/kwad/components/core/request/model/ImpInfo;-><init>(Lcom/kwad/sdk/internal/api/SceneImpl;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {v2}, Lcom/kwad/sdk/internal/api/SceneImpl;->getPageScene()I

    .line 61
    .line 62
    .line 63
    move-result v2

    .line 64
    int-to-long v4, v2

    .line 65
    iput-wide v4, v3, Lcom/kwad/components/core/request/model/ImpInfo;->pageScene:J

    .line 66
    .line 67
    new-instance v2, Lcom/kwad/components/ct/detail/b/d$5;

    .line 68
    .line 69
    invoke-direct {v2, p0, v3, v0, v1}, Lcom/kwad/components/ct/detail/b/d$5;-><init>(Lcom/kwad/components/ct/detail/b/d;Lcom/kwad/components/core/request/model/ImpInfo;J)V

    .line 70
    .line 71
    .line 72
    iput-object v2, p0, Lcom/kwad/components/ct/detail/b/d;->mNetworking:Lcom/kwad/sdk/core/network/l;

    .line 73
    .line 74
    new-instance v0, Lcom/kwad/components/ct/detail/b/d$6;

    .line 75
    .line 76
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/d$6;-><init>(Lcom/kwad/components/ct/detail/b/d;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2, v0}, Lcom/kwad/sdk/core/network/l;->request(Lcom/kwad/sdk/core/network/g;)V

    .line 80
    .line 81
    .line 82
    :cond_3
    :goto_0
    return-void
.end method

.method private BD()V
    .locals 6

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/kwad/components/ct/response/model/CtAdTemplate;->photoInfo:Lcom/kwad/components/ct/response/model/CtPhotoInfo;

    .line 4
    .line 5
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/c;->e(Lcom/kwad/components/ct/response/model/CtPhotoInfo;)J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/d;->avq:Landroid/view/View;

    .line 10
    .line 11
    iget v3, p0, Lcom/kwad/components/ct/detail/b/d;->avp:I

    .line 12
    .line 13
    invoke-virtual {v2, v3}, Landroid/view/View;->getTag(I)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    check-cast v2, Ljava/lang/String;

    .line 18
    .line 19
    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    if-nez v3, :cond_0

    .line 24
    .line 25
    invoke-static {v0, v1}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    if-eqz v2, :cond_0

    .line 34
    .line 35
    return-void

    .line 36
    :cond_0
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 37
    .line 38
    invoke-static {v2}, Lcom/kwad/sdk/core/response/b/e;->bf(Lcom/kwad/sdk/core/response/model/AdTemplate;)Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    iget-object v3, p0, Lcom/kwad/components/ct/detail/b/d;->avr:Landroid/widget/ImageView;

    .line 43
    .line 44
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getContext()Landroid/content/Context;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    invoke-virtual {v4}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    sget v5, Lcom/kwad/sdk/R$drawable;->ksad_photo_default_author_icon_2:I

    .line 53
    .line 54
    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 55
    .line 56
    .line 57
    move-result-object v4

    .line 58
    invoke-static {v3, v2, v4}, Lcom/kwad/sdk/core/imageloader/KSImageLoader;->loadCircleIconWithoutStroke(Landroid/widget/ImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V

    .line 59
    .line 60
    .line 61
    iget-object v2, p0, Lcom/kwad/components/ct/detail/b/d;->avq:Landroid/view/View;

    .line 62
    .line 63
    iget v3, p0, Lcom/kwad/components/ct/detail/b/d;->avp:I

    .line 64
    .line 65
    invoke-static {v0, v1}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    invoke-virtual {v2, v3, v0}, Landroid/view/View;->setTag(ILjava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    return-void
.end method

.method private BE()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->mNetworking:Lcom/kwad/sdk/core/network/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/kwad/sdk/core/network/l;->cancel()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method private Bn()V
    .locals 4

    .line 1
    invoke-static {}, Lcom/kwad/components/ct/e/b;->Jc()Lcom/kwad/components/ct/e/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/d;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 6
    .line 7
    const/4 v2, 0x5

    .line 8
    const/4 v3, 0x3

    .line 9
    invoke-virtual {v0, v1, v2, v3}, Lcom/kwad/components/ct/e/b;->c(Lcom/kwad/components/ct/response/model/CtAdTemplate;II)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method private Bw()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/d;->avn:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/kwad/sdk/lib/widget/a/d;

    .line 8
    .line 9
    invoke-virtual {v0}, Lcom/kwad/sdk/lib/widget/a/d;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lcom/kwad/components/ct/home/<USER>/b;

