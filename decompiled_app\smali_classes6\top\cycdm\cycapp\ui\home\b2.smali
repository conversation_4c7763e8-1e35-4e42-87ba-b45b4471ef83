.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(IIIII)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 3
    iput p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 4
    iput p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 5
    iput p4, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 6
    iput p5, p0, Lt<PERSON>/cycdm/cycapp/ui/home/<USER>

    return-void
.end method

.method public synthetic constructor <init>(IIIIIILkotlin/jvm/internal/n;)V
    .locals 1

    and-int/lit8 p7, p6, 0x1

    const/4 v0, 0x0

    if-eqz p7, :cond_0

    move p1, v0

    :cond_0
    and-int/lit8 p7, p6, 0x2

    if-eqz p7, :cond_1

    move p2, v0

    :cond_1
    and-int/lit8 p7, p6, 0x4

    if-eqz p7, :cond_2

    move p3, v0

    :cond_2
    and-int/lit8 p7, p6, 0x8

    if-eqz p7, :cond_3

    move p4, v0

    :cond_3
    and-int/lit8 p6, p6, 0x10

    if-eqz p6, :cond_4

    move p6, v0

    :goto_0
    move p5, p4

    move p4, p3

    move p3, p2

    move p2, p1

    move-object p1, p0

    goto :goto_1

    :cond_4
    move p6, p5

    goto :goto_0

    .line 7
    :goto_1
    invoke-direct/range {p1 .. p6}, Ltop/cycdm/cycapp/ui/home/<USER>

    return-void
.end method

.method public static synthetic b(Ltop/cycdm/cycapp/ui/home/<USER>/lang/Object;)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 0

    .line 1
    and-int/lit8 p7, p6, 0x1

    if-eqz p7, :cond_0

    iget p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    :cond_0
    and-int/lit8 p7, p6, 0x2

    if-eqz p7, :cond_1

    iget p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    :cond_1
    and-int/lit8 p7, p6, 0x4

    if-eqz p7, :cond_2

    iget p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    :cond_2
    and-int/lit8 p7, p6, 0x8

    if-eqz p7, :cond_3

    iget p4, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    :cond_3
    and-int/lit8 p6, p6, 0x10

    if-eqz p6, :cond_4

    iget p5, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    :cond_4
    move p6, p4

    move p7, p5

    move p4, p2

    move p5, p3

    move-object p2, p0

    move p3, p1

    invoke-virtual/range {p2 .. p7}, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a(IIIII)Ltop/cycdm/cycapp/ui/home/<USER>
    .locals 6

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    move v1, p1

    move v2, p2

    move v3, p3

    move v4, p4

    move v5, p5

    invoke-direct/range {v0 .. v5}, Ltop/cycdm/cycapp/ui/home/<USER>

    return-object v0
.end method

.method public final c()I
    .locals 1

    .line 1
    iget v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    return v0
.end method

.method public final d()I
    .locals 1

    .line 1
    iget v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    return v0
.end method

.method public final e()I
    .locals 1

    .line 1
    iget v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Ltop/cycdm/cycapp/ui/home/<USER>

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Ltop/cycdm/cycapp/ui/home/<USER>

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget v3, p1, Ltop/cycdm/cycapp/ui/home/<USER>

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget v3, p1, Ltop/cycdm/cycapp/ui/home/<USER>

    if-eq v1, v3, :cond_3

    return v2

    :cond_3
    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget v3, p1, Ltop/cycdm/cycapp/ui/home/<USER>

    if-eq v1, v3, :cond_4

    return v2

    :cond_4
    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget v3, p1, Ltop/cycdm/cycapp/ui/home/<USER>

    if-eq v1, v3, :cond_5

    return v2

    :cond_5
    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    iget p1, p1, Ltop/cycdm/cycapp/ui/home/<USER>

    if-eq v1, p1, :cond_6

    return v2

    :cond_6
    return v0
.end method

.method public final f()I
    .locals 1

    .line 1
    iget v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    return v0
.end method

.method public final g()I
    .locals 1

    .line 1
    iget v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-static {v0}, Ljava/lang/Integer;->hashCode(I)I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-static {v1}, Ljava/lang/Integer;->hashCode(I)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-static {v1}, Ljava/lang/Integer;->hashCode(I)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-static {v1}, Ljava/lang/Integer;->hashCode(I)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-static {v1}, Ljava/lang/Integer;->hashCode(I)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "VideoQueryData(class="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", area="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", lang="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", year="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", order="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
