.class Lcom/beizi/ad/v2/f/b$3;
.super Landroid/webkit/WebViewClient;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/f/b;->x()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/f/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/f/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-direct {p0}, Landroid/webkit/WebViewClient;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onPageFinished(Landroid/webkit/WebView;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2}, Landroid/webkit/WebViewClient;->onPageFinished(Landroid/webkit/WebView;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    :try_start_0
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 5
    .line 6
    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->c(Lcom/beizi/ad/v2/f/b;)Landroid/view/ViewGroup;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    if-nez p1, :cond_0

    .line 11
    .line 12
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 13
    .line 14
    const/16 p2, 0xa

    .line 15
    .line 16
    invoke-virtual {p1, p2}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 17
    .line 18
    .line 19
    return-void

    .line 20
    :catch_0
    move-exception p1

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 23
    .line 24
    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->p(Lcom/beizi/ad/v2/f/b;)Landroid/webkit/WebView;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    if-nez p1, :cond_1

    .line 29
    .line 30
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 31
    .line 32
    const/16 p2, 0x9

    .line 33
    .line 34
    invoke-virtual {p1, p2}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 35
    .line 36
    .line 37
    return-void

    .line 38
    :cond_1
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 39
    .line 40
    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->c(Lcom/beizi/ad/v2/f/b;)Landroid/view/ViewGroup;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iget-object p2, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 45
    .line 46
    invoke-static {p2}, Lcom/beizi/ad/v2/f/b;->p(Lcom/beizi/ad/v2/f/b;)Landroid/webkit/WebView;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    invoke-virtual {p1, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 54
    .line 55
    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->m(Lcom/beizi/ad/v2/f/b;)V

    .line 56
    .line 57
    .line 58
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 59
    .line 60
    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->n(Lcom/beizi/ad/v2/f/b;)V

    .line 61
    .line 62
    .line 63
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 64
    .line 65
    invoke-static {p1}, Lcom/beizi/ad/v2/f/b;->o(Lcom/beizi/ad/v2/f/b;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 66
    .line 67
    .line 68
    return-void

    .line 69
    :goto_0
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 70
    .line 71
    .line 72
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 73
    .line 74
    const/4 p2, 0x2

    .line 75
    invoke-virtual {p1, p2}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 76
    .line 77
    .line 78
    return-void
.end method

.method public onReceivedError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceError;)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2, p3}, Landroid/webkit/WebViewClient;->onReceivedError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceError;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 5
    .line 6
    const/4 p2, 0x2

    .line 7
    invoke-virtual {p1, p2}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public onReceivedSslError(Landroid/webkit/WebView;Landroid/webkit/SslErrorHandler;Landroid/net/http/SslError;)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2, p3}, Landroid/webkit/WebViewClient;->onReceivedSslError(Landroid/webkit/WebView;Landroid/webkit/SslErrorHandler;Landroid/net/http/SslError;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/beizi/ad/v2/f/b$3;->a:Lcom/beizi/ad/v2/f/b;

    .line 5
    .line 6
    const/4 p2, 0x2

    .line 7
    invoke-virtual {p1, p2}, Lcom/beizi/ad/v2/f/b;->b(I)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
