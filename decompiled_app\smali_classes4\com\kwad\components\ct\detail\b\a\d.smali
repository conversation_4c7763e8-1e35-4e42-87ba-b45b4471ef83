.class public final Lcom/kwad/components/ct/detail/b/a/d;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private awk:Landroid/widget/RelativeLayout;

.field private awl:Landroid/view/View;

.field private awm:Z

.field private fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/kwad/components/ct/detail/b/a/d;->awm:Z

    .line 6
    .line 7
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/a/d;)Lcom/kwad/sdk/core/view/AdBaseFrameLayout;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a/d;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    return-object p0
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/a/d;Z)Z
    .locals 0

    const/4 p1, 0x1

    .line 2
    iput-boolean p1, p0, Lcom/kwad/components/ct/detail/b/a/d;->awm:Z

    return p1
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/a/d;)Landroid/view/View;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a/d;->awl:Landroid/view/View;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/a/d;)Landroid/widget/RelativeLayout;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a/d;->awk:Landroid/widget/RelativeLayout;

    .line 2
    .line 3
    return-object p0
.end method

.method private j(Landroid/app/Activity;)V
    .locals 3

    .line 1
    invoke-static {p1}, Lcom/kwad/sdk/c/a/a;->q(Landroid/app/Activity;)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {p1}, Lcom/kwad/sdk/c/a/a;->aQ(Landroid/content/Context;)Landroid/graphics/Point;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a/d;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 10
    .line 11
    new-instance v2, Lcom/kwad/components/ct/detail/b/a/d$1;

    .line 12
    .line 13
    invoke-direct {v2, p0, p1, v0}, Lcom/kwad/components/ct/detail/b/a/d$1;-><init>(Lcom/kwad/components/ct/detail/b/a/d;Landroid/graphics/Point;I)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v1, v2}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 17
    .line 18
    .line 19
    return-void
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/kwad/sdk/api/core/fragment/KsFragment;->getActivity()Landroid/app/Activity;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-static {v0}, Lcom/kwad/sdk/c/a/a;->r(Landroid/app/Activity;)Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    iget-boolean v1, p0, Lcom/kwad/components/ct/detail/b/a/d;->awm:Z

    .line 19
    .line 20
    if-nez v1, :cond_0

    .line 21
    .line 22
    invoke-direct {p0, v0}, Lcom/kwad/components/ct/detail/b/a/d;->j(Landroid/app/Activity;)V

    .line 23
    .line 24
    .line 25
    :cond_0
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_root_container:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/d;->fW:Lcom/kwad/sdk/core/view/AdBaseFrameLayout;

    .line 13
    .line 14
    sget v0, Lcom/kwad/sdk/R$id;->ksad_video_bottom_container:I

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    check-cast v0, Landroid/widget/RelativeLayout;

    .line 21
    .line 22
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/d;->awk:Landroid/widget/RelativeLayout;

    .line 23
    .line 24
    sget v0, Lcom/kwad/sdk/R$id;->ksad_bottom_shadow:I

    .line 25
    .line 26
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a/d;->awl:Landroid/view/View;

    .line 31
    .line 32
    return-void
.end method

.method public final onUnbind()V
    .locals 0

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
