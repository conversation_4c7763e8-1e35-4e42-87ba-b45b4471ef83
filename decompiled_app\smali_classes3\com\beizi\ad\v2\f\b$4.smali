.class Lcom/beizi/ad/v2/f/b$4;
.super Landroid/os/CountDownTimer;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/f/b;->t()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/f/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/f/b;JJ)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/f/b$4;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-direct {p0, p2, p3, p4, p5}, Landroid/os/CountDownTimer;-><init>(JJ)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onFinish()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$4;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->h(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$4;->a:Lcom/beizi/ad/v2/f/b;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->h(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lcom/beizi/ad/a;->c()V

    .line 16
    .line 17
    .line 18
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$4;->a:Lcom/beizi/ad/v2/f/b;

    .line 19
    .line 20
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->q(Lcom/beizi/ad/v2/f/b;)Landroid/os/CountDownTimer;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    if-eqz v0, :cond_1

    .line 25
    .line 26
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$4;->a:Lcom/beizi/ad/v2/f/b;

    .line 27
    .line 28
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->q(Lcom/beizi/ad/v2/f/b;)Landroid/os/CountDownTimer;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {v0}, Landroid/os/CountDownTimer;->cancel()V

    .line 33
    .line 34
    .line 35
    :cond_1
    return-void
.end method

.method public onTick(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$4;->a:Lcom/beizi/ad/v2/f/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->h(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/beizi/ad/v2/f/b$4;->a:Lcom/beizi/ad/v2/f/b;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/beizi/ad/v2/f/b;->h(Lcom/beizi/ad/v2/f/b;)Lcom/beizi/ad/a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0, p1, p2}, Lcom/beizi/ad/a;->a(J)V

    .line 16
    .line 17
    .line 18
    :cond_0
    return-void
.end method
