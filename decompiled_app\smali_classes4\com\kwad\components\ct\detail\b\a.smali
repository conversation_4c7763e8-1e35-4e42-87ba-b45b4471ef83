.class public final Lcom/kwad/components/ct/detail/b/a;
.super Lcom/kwad/components/ct/detail/b;
.source "SourceFile"


# instance fields
.field private amo:Lcom/kwad/components/core/j/a;

.field private dn:Landroid/widget/ImageView;

.field private dp:Ljava/lang/Runnable;

.field private mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

.field private mVideoPlayStateListener:Lcom/kwad/components/core/video/n;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/kwad/components/ct/detail/b;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/kwad/components/ct/detail/b/a$1;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/a$1;-><init>(Lcom/kwad/components/ct/detail/b/a;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a;->dp:Ljava/lang/Runnable;

    .line 10
    .line 11
    new-instance v0, Lcom/kwad/components/ct/detail/b/a$2;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/a$2;-><init>(Lcom/kwad/components/ct/detail/b/a;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a;->amo:Lcom/kwad/components/core/j/a;

    .line 17
    .line 18
    new-instance v0, Lcom/kwad/components/ct/detail/b/a$3;

    .line 19
    .line 20
    invoke-direct {v0, p0}, Lcom/kwad/components/ct/detail/b/a$3;-><init>(Lcom/kwad/components/ct/detail/b/a;)V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 24
    .line 25
    return-void
.end method

.method public static synthetic a(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/response/model/CtAdTemplate;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lcom/kwad/components/ct/detail/b/a;)Landroid/widget/ImageView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b/a;->dn:Landroid/widget/ImageView;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lcom/kwad/components/ct/detail/b/a;)Lcom/kwad/components/ct/detail/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final T()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/ct/detail/b;->T()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 5
    .line 6
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 7
    .line 8
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 9
    .line 10
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getRootView()Landroid/view/View;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->dp:Ljava/lang/Runnable;

    .line 15
    .line 16
    invoke-virtual {v0, v1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 20
    .line 21
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 22
    .line 23
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->amo:Lcom/kwad/components/core/j/a;

    .line 24
    .line 25
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 29
    .line 30
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 31
    .line 32
    if-eqz v0, :cond_0

    .line 33
    .line 34
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 35
    .line 36
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 37
    .line 38
    .line 39
    :cond_0
    return-void
.end method

.method public final onCreate()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onCreate()V

    .line 2
    .line 3
    .line 4
    sget v0, Lcom/kwad/sdk/R$id;->ksad_video_first_frame:I

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lcom/kwad/sdk/mvp/Presenter;->findViewById(I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Landroid/widget/ImageView;

    .line 11
    .line 12
    iput-object v0, p0, Lcom/kwad/components/ct/detail/b/a;->dn:Landroid/widget/ImageView;

    .line 13
    .line 14
    return-void
.end method

.method public final onDestroy()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onDestroy()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getRootView()Landroid/view/View;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->dp:Ljava/lang/Runnable;

    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 13
    .line 14
    .line 15
    :cond_0
    return-void
.end method

.method public final onUnbind()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/sdk/mvp/Presenter;->onUnbind()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lcom/kwad/sdk/mvp/Presenter;->getRootView()Landroid/view/View;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->dp:Ljava/lang/Runnable;

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 14
    .line 15
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alO:Ljava/util/List;

    .line 16
    .line 17
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->amo:Lcom/kwad/components/core/j/a;

    .line 18
    .line 19
    invoke-interface {v0, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 23
    .line 24
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 25
    .line 26
    if-eqz v0, :cond_0

    .line 27
    .line 28
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->mVideoPlayStateListener:Lcom/kwad/components/core/video/n;

    .line 29
    .line 30
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 31
    .line 32
    .line 33
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b;->alN:Lcom/kwad/components/ct/detail/c;

    .line 34
    .line 35
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alW:Lcom/kwad/sdk/api/core/fragment/KsFragment;

    .line 36
    .line 37
    if-eqz v0, :cond_1

    .line 38
    .line 39
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->dn:Landroid/widget/ImageView;

    .line 40
    .line 41
    if-eqz v1, :cond_1

    .line 42
    .line 43
    invoke-static {v0}, Lcom/kwad/sdk/glide/c;->h(Lcom/kwad/sdk/api/core/fragment/KsFragment;)Lcom/kwad/sdk/glide/g;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/a;->dn:Landroid/widget/ImageView;

    .line 48
    .line 49
    invoke-virtual {v0, v1}, Lcom/kwad/sdk/glide/g;->ac(Landroid/view/View;)V

    .line 50
    .line 51
    .line 52
    const-string v0, "DetailFirstFramePresenter"

    .line 53
    .line 54
    const-string v1, "[WEBP]Glide clear(mFirstFrameView);"

    .line 55
    .line 56
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 57
    .line 58
    .line 59
    :cond_1
    return-void
.end method
