.class Landroidx/media3/container/ObuParser$NotYetImplementedException;
.super Ljava/lang/Exception;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/container/ObuParser;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "NotYetImplementedException"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/container/ObuParser$1;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Landroidx/media3/container/ObuParser$NotYetImplementedException;-><init>()V

    return-void
.end method
