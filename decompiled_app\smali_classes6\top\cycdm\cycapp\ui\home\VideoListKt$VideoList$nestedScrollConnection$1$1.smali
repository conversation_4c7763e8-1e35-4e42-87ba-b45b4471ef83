.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/compose/ui/input/nestedscroll/NestedScrollConnection;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/model/NavInfoInner;Landroidx/compose/runtime/Composer;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u001f\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "top/cycdm/cycapp/ui/home/<USER>",
        "Landroidx/compose/ui/input/nestedscroll/NestedScrollConnection;",
        "onPreScroll",
        "Landroidx/compose/ui/geometry/Offset;",
        "available",
        "source",
        "Landroidx/compose/ui/input/nestedscroll/NestedScrollSource;",
        "onPreScroll-OzD1aCk",
        "(JI)J",
        "app_adRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nVideoList.kt\nKotlin\n*S Kotlin\n*F\n+ 1 VideoList.kt\ntop/cycdm/cycapp/ui/home/<USER>/compose/ui/geometry/Offset\n+ 3 InlineClassHelper.kt\nandroidx/compose/ui/util/InlineClassHelperKt\n+ 4 InlineClassHelper.jvm.kt\nandroidx/compose/ui/util/InlineClassHelper_jvmKt\n+ 5 Offset.kt\nandroidx/compose/ui/geometry/OffsetKt\n*L\n1#1,219:1\n69#2:220\n70#3:221\n53#3,3:224\n22#4:222\n30#5:223\n*S KotlinDebug\n*F\n+ 1 VideoList.kt\ntop/cycdm/cycapp/ui/home/<USER>"
    }
.end annotation


# instance fields
.field final synthetic $toolbarHeight$delegate:Landroidx/compose/runtime/MutableIntState;

.field final synthetic $toolbarOffsetHeightPx:Landroidx/compose/runtime/MutableFloatState;

.field final synthetic $toolbarPaddingHeight:F


# direct methods
.method public constructor <init>(FLandroidx/compose/runtime/MutableFloatState;Landroidx/compose/runtime/MutableIntState;)V
    .locals 0

    .line 1
    iput p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 2
    .line 3
    iput-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;

    .line 4
    .line 5
    iput-object p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;

    .line 6
    .line 7
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public onPreScroll-OzD1aCk(JI)J
    .locals 4

    .line 1
    iget-object p3, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;

    .line 2
    .line 3
    invoke-static {p3}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableIntState;)I

    .line 4
    .line 5
    .line 6
    move-result p3

    .line 7
    int-to-float p3, p3

    .line 8
    iget v0, p0, Ltop/cycdm/cycapp/ui/home/<USER>

    .line 9
    .line 10
    add-float/2addr p3, v0

    .line 11
    const-wide v0, 0xffffffffL

    .line 12
    .line 13
    .line 14
    .line 15
    .line 16
    and-long/2addr p1, v0

    .line 17
    long-to-int p1, p1

    .line 18
    invoke-static {p1}, Ljava/lang/Float;->intBitsToFloat(I)F

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    iget-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;

    .line 23
    .line 24
    invoke-interface {p2}, Landroidx/compose/runtime/MutableFloatState;->getFloatValue()F

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    add-float/2addr p2, p1

    .line 29
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;

    .line 30
    .line 31
    invoke-interface {p1}, Landroidx/compose/runtime/MutableFloatState;->getFloatValue()F

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;

    .line 36
    .line 37
    neg-float p3, p3

    .line 38
    const/high16 v3, -0x80000000

    .line 39
    .line 40
    invoke-static {p2, p3, v3}, Lx5/n;->m(FFF)F

    .line 41
    .line 42
    .line 43
    move-result p2

    .line 44
    invoke-interface {v2, p2}, Landroidx/compose/runtime/MutableFloatState;->setFloatValue(F)V

    .line 45
    .line 46
    .line 47
    iget-object p2, p0, Ltop/cycdm/cycapp/ui/home/<USER>/compose/runtime/MutableFloatState;

    .line 48
    .line 49
    invoke-interface {p2}, Landroidx/compose/runtime/MutableFloatState;->getFloatValue()F

    .line 50
    .line 51
    .line 52
    move-result p2

    .line 53
    sub-float/2addr p2, p1

    .line 54
    const/4 p1, 0x0

    .line 55
    invoke-static {p1}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 56
    .line 57
    .line 58
    move-result p1

    .line 59
    int-to-long v2, p1

    .line 60
    invoke-static {p2}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 61
    .line 62
    .line 63
    move-result p1

    .line 64
    int-to-long p1, p1

    .line 65
    const/16 p3, 0x20

    .line 66
    .line 67
    shl-long/2addr v2, p3

    .line 68
    and-long/2addr p1, v0

    .line 69
    or-long/2addr p1, v2

    .line 70
    invoke-static {p1, p2}, Landroidx/compose/ui/geometry/Offset;->constructor-impl(J)J

    .line 71
    .line 72
    .line 73
    move-result-wide p1

    .line 74
    return-wide p1
.end method
