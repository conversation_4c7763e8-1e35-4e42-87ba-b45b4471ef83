.class public Lcom/beizi/ad/v2/b/b;
.super Lcom/beizi/ad/v2/a/b;
.source "SourceFile"


# static fields
.field public static C:Lcom/beizi/ad/v2/b/b;


# instance fields
.field private D:Lcom/beizi/ad/a;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 1
    sget-object v0, Lcom/beizi/ad/internal/k;->e:Lcom/beizi/ad/internal/k;

    .line 2
    .line 3
    invoke-direct {p0, p1, v0}, Lcom/beizi/ad/v2/a/b;-><init>(Landroid/content/Context;Lcom/beizi/ad/internal/k;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/b/b;)Lcom/beizi/ad/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    return-object p0
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/b/b;Z)Z
    .locals 0

    .line 2
    iput-boolean p1, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    return p1
.end method

.method public static synthetic b(Lcom/beizi/ad/v2/b/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/b/b;->w()V

    return-void
.end method

.method private w()V
    .locals 5

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->N()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 11
    .line 12
    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->L()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    iget-object v3, p0, Lcom/beizi/ad/v2/a/b;->c:Lcom/beizi/ad/internal/f/c;

    .line 17
    .line 18
    invoke-virtual {v3}, Lcom/beizi/ad/internal/f/c;->M()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v3

    .line 22
    goto :goto_0

    .line 23
    :catch_0
    move-exception v0

    .line 24
    goto :goto_1

    .line 25
    :cond_0
    const/4 v2, 0x0

    .line 26
    move v0, v1

    .line 27
    move-object v3, v2

    .line 28
    :goto_0
    const/4 v4, 0x3

    .line 29
    if-nez v0, :cond_2

    .line 30
    .line 31
    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 38
    .line 39
    if-eqz v0, :cond_4

    .line 40
    .line 41
    invoke-virtual {v0, v4}, Lcom/beizi/ad/a;->a(I)V

    .line 42
    .line 43
    .line 44
    return-void

    .line 45
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 46
    .line 47
    if-eqz v0, :cond_4

    .line 48
    .line 49
    const/4 v0, 0x1

    .line 50
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    .line 51
    .line 52
    const-string v0, "BeiZisAd"

    .line 53
    .line 54
    const-string v1, "enter BeiZi ad load"

    .line 55
    .line 56
    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 57
    .line 58
    .line 59
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 60
    .line 61
    invoke-virtual {v0}, Lcom/beizi/ad/a;->a()V

    .line 62
    .line 63
    .line 64
    return-void

    .line 65
    :cond_2
    invoke-static {v3}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    .line 66
    .line 67
    .line 68
    move-result v0

    .line 69
    if-eqz v0, :cond_3

    .line 70
    .line 71
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 72
    .line 73
    if-eqz v0, :cond_4

    .line 74
    .line 75
    invoke-virtual {v0, v4}, Lcom/beizi/ad/a;->a(I)V

    .line 76
    .line 77
    .line 78
    return-void

    .line 79
    :cond_3
    invoke-static {}, Lcom/beizi/ad/internal/h/u;->a()Lcom/beizi/ad/internal/h/u;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    iget-object v2, p0, Lcom/beizi/ad/v2/a/b;->a:Landroid/content/Context;

    .line 84
    .line 85
    new-instance v4, Lcom/beizi/ad/v2/b/b$1;

    .line 86
    .line 87
    invoke-direct {v4, p0}, Lcom/beizi/ad/v2/b/b$1;-><init>(Lcom/beizi/ad/v2/b/b;)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v0, v2, v3, v1, v4}, Lcom/beizi/ad/internal/h/u;->a(Landroid/content/Context;Ljava/lang/String;ZLcom/beizi/ad/internal/h/u$a;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 91
    .line 92
    .line 93
    return-void

    .line 94
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 95
    .line 96
    .line 97
    :cond_4
    return-void
.end method


# virtual methods
.method public a(Landroid/content/Context;)V
    .locals 2

    .line 4
    const-string v0, "BeiZisAd"

    const-string v1, "showInterstitial"

    invoke-static {v0, v1}, Lcom/beizi/ad/lance/a/m;->c(Ljava/lang/String;Ljava/lang/String;)V

    .line 5
    sput-object p0, Lcom/beizi/ad/v2/b/b;->C:Lcom/beizi/ad/v2/b/b;

    .line 6
    new-instance v0, Landroid/content/Intent;

    const-class v1, Lcom/beizi/ad/v2/activity/BeiZiNewInterstitialActivity;

    invoke-direct {v0, p1, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    .line 7
    invoke-virtual {p1, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    return-void
.end method

.method public a(Lcom/beizi/ad/a;)V
    .locals 0

    .line 3
    iput-object p1, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    return-void
.end method

.method public a(Lcom/beizi/ad/internal/f/c;)V
    .locals 1

    .line 8
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->e()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/a/b;->b(Ljava/lang/String;)V

    .line 9
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->f()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/beizi/ad/v2/a/b;->c(Ljava/lang/String;)V

    .line 10
    invoke-virtual {p1}, Lcom/beizi/ad/internal/f/c;->P()Z

    move-result p1

    invoke-virtual {p0, p1}, Lcom/beizi/ad/v2/a/b;->a(Z)V

    .line 11
    iget-object p1, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    if-eqz p1, :cond_0

    .line 12
    new-instance v0, Lcom/beizi/ad/v2/b/b$3;

    invoke-direct {v0, p0}, Lcom/beizi/ad/v2/b/b$3;-><init>(Lcom/beizi/ad/v2/b/b;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_0
    return-void
.end method

.method public b(I)V
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->g:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    .line 3
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->j:Z

    .line 4
    iget-object v0, p0, Lcom/beizi/ad/v2/a/b;->B:Landroid/os/Handler;

    if-eqz v0, :cond_1

    .line 5
    new-instance v1, Lcom/beizi/ad/v2/b/b$2;

    invoke-direct {v1, p0, p1}, Lcom/beizi/ad/v2/b/b$2;-><init>(Lcom/beizi/ad/v2/b/b;I)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public f(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lcom/beizi/ad/a;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public q()V
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/beizi/ad/v2/a/b;->q()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    sput-object v0, Lcom/beizi/ad/v2/b/b;->C:Lcom/beizi/ad/v2/b/b;

    .line 6
    .line 7
    return-void
.end method

.method public t()V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->t:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x1

    .line 6
    iput-boolean v0, p0, Lcom/beizi/ad/v2/a/b;->u:Z

    .line 7
    .line 8
    invoke-static {}, Lcom/beizi/ad/internal/a/a;->a()Lcom/beizi/ad/internal/a/a;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iget-object v1, p0, Lcom/beizi/ad/v2/a/b;->r:Lcom/beizi/ad/internal/a/c;

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Lcom/beizi/ad/internal/a/a;->a(Lcom/beizi/ad/internal/a/c;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    const-string v0, "BeiZisAd"

    .line 22
    .line 23
    const-string v1, "enter BeiZi ad show"

    .line 24
    .line 25
    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 29
    .line 30
    invoke-virtual {v0}, Lcom/beizi/ad/a;->b()V

    .line 31
    .line 32
    .line 33
    :cond_1
    return-void
.end method

.method public u()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/beizi/ad/a;->c()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public v()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/beizi/ad/v2/b/b;->D:Lcom/beizi/ad/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/beizi/ad/a;->e()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method
