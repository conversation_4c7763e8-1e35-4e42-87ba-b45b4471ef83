.class public final Ltop/cycdm/cycapp/ui/email/x;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:Ltop/cycdm/cycapp/utils/h;

.field public final b:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/x;->a:Ltop/cycdm/cycapp/utils/h;

    .line 3
    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/x;->b:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;ILkotlin/jvm/internal/n;)V
    .locals 0

    and-int/lit8 p4, p3, 0x1

    if-eqz p4, :cond_0

    .line 4
    sget-object p1, Ltop/cycdm/cycapp/utils/h$d;->a:Ltop/cycdm/cycapp/utils/h$d;

    :cond_0
    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_1

    .line 5
    const-string p2, ""

    .line 6
    :cond_1
    invoke-direct {p0, p1, p2}, Ltop/cycdm/cycapp/ui/email/x;-><init>(Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic b(Ltop/cycdm/cycapp/ui/email/x;Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;ILjava/lang/Object;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 0

    .line 1
    and-int/lit8 p4, p3, 0x1

    if-eqz p4, :cond_0

    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/x;->a:Ltop/cycdm/cycapp/utils/h;

    :cond_0
    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_1

    iget-object p2, p0, Ltop/cycdm/cycapp/ui/email/x;->b:Ljava/lang/String;

    :cond_1
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/email/x;->a(Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;)Ltop/cycdm/cycapp/ui/email/x;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a(Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;)Ltop/cycdm/cycapp/ui/email/x;
    .locals 1

    .line 1
    new-instance v0, Ltop/cycdm/cycapp/ui/email/x;

    invoke-direct {v0, p1, p2}, Ltop/cycdm/cycapp/ui/email/x;-><init>(Ltop/cycdm/cycapp/utils/h;Ljava/lang/String;)V

    return-object v0
.end method

.method public final c()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/x;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Ltop/cycdm/cycapp/utils/h;
    .locals 1

    .line 1
    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/x;->a:Ltop/cycdm/cycapp/utils/h;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Ltop/cycdm/cycapp/ui/email/x;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Ltop/cycdm/cycapp/ui/email/x;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/x;->a:Ltop/cycdm/cycapp/utils/h;

    iget-object v3, p1, Ltop/cycdm/cycapp/ui/email/x;->a:Ltop/cycdm/cycapp/utils/h;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/x;->b:Ljava/lang/String;

    iget-object p1, p1, Ltop/cycdm/cycapp/ui/email/x;->b:Ljava/lang/String;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_3

    return v2

    :cond_3
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Ltop/cycdm/cycapp/ui/email/x;->a:Ltop/cycdm/cycapp/utils/h;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/x;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "EmailState(sendSession="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/x;->a:Ltop/cycdm/cycapp/utils/h;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", count="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/x;->b:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
