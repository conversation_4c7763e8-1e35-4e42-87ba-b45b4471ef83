.class final Lcom/kwad/components/ct/detail/b/b/a$4;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/b/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awv:Lcom/kwad/components/ct/detail/b/b/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayCompleted()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->h(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 7
    .line 8
    .line 9
    const-string v0, "DetailLoadingPresenter"

    .line 10
    .line 11
    const-string v1, "onVideoPlayCompleted"

    .line 12
    .line 13
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final onMediaPlayError(II)V
    .locals 0

    .line 1
    const-string p1, "DetailLoadingPresenter"

    .line 2
    .line 3
    const-string p2, "onVideoPlayError"

    .line 4
    .line 5
    invoke-static {p1, p2}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 9
    .line 10
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/b/a;->h(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 11
    .line 12
    .line 13
    iget-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 14
    .line 15
    invoke-static {p1}, Lcom/kwad/components/ct/detail/b/b/a;->p(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    const-string p2, "\u7f51\u7edc\u9519\u8bef"

    .line 20
    .line 21
    invoke-static {p1, p2}, Lcom/kwad/sdk/utils/ab;->ab(Landroid/content/Context;Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final onMediaPlayStart()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlayStart()V

    .line 2
    .line 3
    .line 4
    const-string v0, "DetailLoadingPresenter"

    .line 5
    .line 6
    const-string v1, "onVideoPlayStart"

    .line 7
    .line 8
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->n(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/os/Handler;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 18
    .line 19
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/b/a;->m(Lcom/kwad/components/ct/detail/b/b/a;)Ljava/lang/Runnable;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 24
    .line 25
    .line 26
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 27
    .line 28
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->d(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/view/ViewGroup;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    const/16 v1, 0x8

    .line 33
    .line 34
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final onMediaPlaying()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPlaying()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->n(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/os/Handler;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 11
    .line 12
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/b/a;->m(Lcom/kwad/components/ct/detail/b/b/a;)Ljava/lang/Runnable;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 17
    .line 18
    .line 19
    const-string v0, "DetailLoadingPresenter"

    .line 20
    .line 21
    const-string v1, "onVideoPlaying"

    .line 22
    .line 23
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 27
    .line 28
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->h(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 32
    .line 33
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->d(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/view/ViewGroup;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    const/16 v1, 0x8

    .line 38
    .line 39
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public final onMediaPreparing()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onMediaPreparing()V

    .line 2
    .line 3
    .line 4
    const-string v0, "DetailLoadingPresenter"

    .line 5
    .line 6
    const-string v1, "onVideoPreparing"

    .line 7
    .line 8
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->g(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final onVideoPlayBufferingPaused()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onVideoPlayBufferingPaused()V

    .line 2
    .line 3
    .line 4
    const-string v0, "DetailLoadingPresenter"

    .line 5
    .line 6
    const-string v1, "onVideoPlayBufferingPaused"

    .line 7
    .line 8
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->o(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final onVideoPlayBufferingPlaying()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/video/o;->onVideoPlayBufferingPlaying()V

    .line 2
    .line 3
    .line 4
    const-string v0, "DetailLoadingPresenter"

    .line 5
    .line 6
    const-string v1, "onVideoPlayBufferingPlaying"

    .line 7
    .line 8
    invoke-static {v0, v1}, Lcom/kwad/sdk/core/d/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$4;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->o(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method
