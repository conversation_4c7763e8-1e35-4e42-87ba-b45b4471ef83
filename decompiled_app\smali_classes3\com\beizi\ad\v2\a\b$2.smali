.class Lcom/beizi/ad/v2/a/b$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/beizi/ad/v2/a/b;->b()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/beizi/ad/v2/a/b;


# direct methods
.method public constructor <init>(Lcom/beizi/ad/v2/a/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/a/b$2;->a:Lcom/beizi/ad/v2/a/b;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    .line 1
    new-instance v0, Lcom/beizi/ad/v2/d/b;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/beizi/ad/v2/d/b;-><init>()V

    .line 4
    .line 5
    .line 6
    iget-object v1, p0, Lcom/beizi/ad/v2/a/b$2;->a:Lcom/beizi/ad/v2/a/b;

    .line 7
    .line 8
    iget-object v2, v1, Lcom/beizi/ad/v2/a/b;->y:Ljava/lang/String;

    .line 9
    .line 10
    iget-object v1, v1, Lcom/beizi/ad/v2/a/b;->l:Ljava/lang/String;

    .line 11
    .line 12
    new-instance v3, Lcom/beizi/ad/v2/a/b$2$1;

    .line 13
    .line 14
    invoke-direct {v3, p0}, Lcom/beizi/ad/v2/a/b$2$1;-><init>(Lcom/beizi/ad/v2/a/b$2;)V

    .line 15
    .line 16
    .line 17
    const/4 v4, 0x0

    .line 18
    invoke-virtual {v0, v2, v1, v4, v3}, Lcom/beizi/ad/v2/d/b;->a(Ljava/lang/String;Ljava/lang/String;ZLcom/beizi/ad/v2/d/a;)V

    .line 19
    .line 20
    .line 21
    return-void
.end method
