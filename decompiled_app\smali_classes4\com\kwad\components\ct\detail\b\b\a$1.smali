.class final Lcom/kwad/components/ct/detail/b/b/a$1;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/b/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic awv:Lcom/kwad/components/ct/detail/b/b/a;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/b/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pQ()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->a(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/content/Context;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {v0}, Lcom/kwad/sdk/utils/an;->isNetworkConnected(Landroid/content/Context;)Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 17
    .line 18
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->b(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 23
    .line 24
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/e/a;->isPlaying()Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-nez v0, :cond_0

    .line 29
    .line 30
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 31
    .line 32
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->c(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 37
    .line 38
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->d(Lcom/kwad/components/ct/detail/b/b/a;)Landroid/view/ViewGroup;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    const/16 v1, 0x8

    .line 43
    .line 44
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 45
    .line 46
    .line 47
    :goto_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 48
    .line 49
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->e(Lcom/kwad/components/ct/detail/b/b/a;)Lcom/kwad/components/ct/detail/c;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->alX:Lcom/kwad/components/ct/detail/e/a;

    .line 54
    .line 55
    invoke-virtual {v0}, Lcom/kwad/components/ct/detail/e/a;->isPreparing()Z

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    if-eqz v0, :cond_1

    .line 60
    .line 61
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 62
    .line 63
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->f(Lcom/kwad/components/ct/detail/b/b/a;)Z

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    if-nez v0, :cond_1

    .line 68
    .line 69
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 70
    .line 71
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->g(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 72
    .line 73
    .line 74
    :cond_1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 75
    .line 76
    const/4 v1, 0x1

    .line 77
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/b/a;->a(Lcom/kwad/components/ct/detail/b/b/a;Z)Z

    .line 78
    .line 79
    .line 80
    return-void
.end method

.method public final pR()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/kwad/components/core/j/b;->pR()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 5
    .line 6
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/b/a;->h(Lcom/kwad/components/ct/detail/b/b/a;)V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/b/a$1;->awv:Lcom/kwad/components/ct/detail/b/b/a;

    .line 10
    .line 11
    const/4 v1, 0x0

    .line 12
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/b/a;->a(Lcom/kwad/components/ct/detail/b/b/a;Z)Z

    .line 13
    .line 14
    .line 15
    return-void
.end method
