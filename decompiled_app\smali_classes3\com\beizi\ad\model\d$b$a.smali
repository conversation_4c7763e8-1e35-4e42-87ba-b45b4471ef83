.class public final Lcom/beizi/ad/model/d$b$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/beizi/ad/model/d$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field private a:Ljava/lang/String;

.field private b:Ljava/lang/String;

.field private c:Ljava/lang/String;

.field private d:J


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(J)Lcom/beizi/ad/model/d$b$a;
    .locals 0

    .line 2
    iput-wide p1, p0, Lcom/beizi/ad/model/d$b$a;->d:J

    return-object p0
.end method

.method public a(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/model/d$b$a;->a:Ljava/lang/String;

    return-object p0
.end method

.method public a()Lcom/beizi/ad/model/d$b;
    .locals 3

    .line 3
    new-instance v0, Lcom/beizi/ad/model/d$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/beizi/ad/model/d$b;-><init>(Lcom/beizi/ad/model/d$1;)V

    .line 4
    iget-object v1, p0, Lcom/beizi/ad/model/d$b$a;->a:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/beizi/ad/model/d$b;->a(Lcom/beizi/ad/model/d$b;Ljava/lang/String;)Ljava/lang/String;

    .line 5
    iget-object v1, p0, Lcom/beizi/ad/model/d$b$a;->b:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/beizi/ad/model/d$b;->b(Lcom/beizi/ad/model/d$b;Ljava/lang/String;)Ljava/lang/String;

    .line 6
    iget-object v1, p0, Lcom/beizi/ad/model/d$b$a;->c:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/beizi/ad/model/d$b;->c(Lcom/beizi/ad/model/d$b;Ljava/lang/String;)Ljava/lang/String;

    .line 7
    iget-wide v1, p0, Lcom/beizi/ad/model/d$b$a;->d:J

    invoke-static {v0, v1, v2}, Lcom/beizi/ad/model/d$b;->a(Lcom/beizi/ad/model/d$b;J)J

    return-object v0
.end method

.method public b(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/model/d$b$a;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public c(Ljava/lang/String;)Lcom/beizi/ad/model/d$b$a;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/model/d$b$a;->c:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method
