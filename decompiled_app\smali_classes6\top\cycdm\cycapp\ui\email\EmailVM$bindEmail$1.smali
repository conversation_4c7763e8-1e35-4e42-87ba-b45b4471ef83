.class final Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/email/EmailVM;->bindEmail(Ljava/lang/String;Ljava/lang/String;)Lkotlinx/coroutines/w1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Ltop/cycdm/cycapp/ui/email/x;",
        "Ltop/cycdm/cycapp/ui/email/b;",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.email.EmailVM$bindEmail$1"
    f = "EmailVM.kt"
    i = {
        0x3,
        0x4,
        0x4
    }
    l = {
        0x53,
        0x57,
        0x5b,
        0x5e,
        0x5f,
        0x61,
        0x66,
        0x6b
    }
    m = "invokeSuspend"
    n = {
        "$this$intent",
        "$this$intent",
        "token"
    }
    s = {
        "L$0",
        "L$0",
        "L$1"
    }
.end annotation


# instance fields
.field final synthetic $code:Ljava/lang/String;

.field final synthetic $email:Ljava/lang/String;

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ltop/cycdm/cycapp/ui/email/EmailVM;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ltop/cycdm/cycapp/ui/email/EmailVM;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$code:Ljava/lang/String;

    iput-object p2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iput-object p3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$email:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$code:Ljava/lang/String;

    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    iget-object v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$email:Ljava/lang/String;

    invoke-direct {v0, v1, v2, v3, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;-><init>(Ljava/lang/String;Ltop/cycdm/cycapp/ui/email/EmailVM;Ljava/lang/String;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 6
    .line 7
    packed-switch v1, :pswitch_data_0

    .line 8
    .line 9
    .line 10
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 11
    .line 12
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 13
    .line 14
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 15
    .line 16
    .line 17
    throw p1

    .line 18
    :pswitch_0
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    goto/16 :goto_6

    .line 22
    .line 23
    :pswitch_1
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    goto/16 :goto_5

    .line 27
    .line 28
    :pswitch_2
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    goto/16 :goto_9

    .line 32
    .line 33
    :pswitch_3
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$1:Ljava/lang/Object;

    .line 34
    .line 35
    check-cast v1, Ljava/lang/String;

    .line 36
    .line 37
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 38
    .line 39
    check-cast v2, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 40
    .line 41
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    :cond_0
    move-object v7, v2

    .line 45
    move-object v2, v1

    .line 46
    goto/16 :goto_4

    .line 47
    .line 48
    :pswitch_4
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 49
    .line 50
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 51
    .line 52
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    move-object v2, v1

    .line 56
    goto/16 :goto_3

    .line 57
    .line 58
    :pswitch_5
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    goto :goto_2

    .line 62
    :pswitch_6
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    goto :goto_1

    .line 66
    :pswitch_7
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 67
    .line 68
    .line 69
    goto :goto_0

    .line 70
    :pswitch_8
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 74
    .line 75
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 76
    .line 77
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$code:Ljava/lang/String;

    .line 78
    .line 79
    invoke-static {v1}, Lkotlin/text/g0;->v0(Ljava/lang/CharSequence;)Z

    .line 80
    .line 81
    .line 82
    move-result v1

    .line 83
    if-eqz v1, :cond_2

    .line 84
    .line 85
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 86
    .line 87
    const/4 v2, 0x1

    .line 88
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 89
    .line 90
    const-string v2, "\u9a8c\u8bc1\u7801\u8f93\u5165\u4e3a\u7a7a"

    .line 91
    .line 92
    invoke-static {v1, p1, v2, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    if-ne p1, v0, :cond_1

    .line 97
    .line 98
    goto/16 :goto_8

    .line 99
    .line 100
    :cond_1
    :goto_0
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 101
    .line 102
    return-object p1

    .line 103
    :cond_2
    sget-object v1, Landroidx/core/util/PatternsCompat;->EMAIL_ADDRESS:Ljava/util/regex/Pattern;

    .line 104
    .line 105
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$email:Ljava/lang/String;

    .line 106
    .line 107
    invoke-virtual {v1, v2}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    invoke-virtual {v1}, Ljava/util/regex/Matcher;->matches()Z

    .line 112
    .line 113
    .line 114
    move-result v1

    .line 115
    if-nez v1, :cond_4

    .line 116
    .line 117
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 118
    .line 119
    const/4 v2, 0x2

    .line 120
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 121
    .line 122
    const-string v2, "\u90ae\u7bb1\u683c\u5f0f\u4e0d\u6b63\u786e"

    .line 123
    .line 124
    invoke-static {v1, p1, v2, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object p1

    .line 128
    if-ne p1, v0, :cond_3

    .line 129
    .line 130
    goto/16 :goto_8

    .line 131
    .line 132
    :cond_3
    :goto_1
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 133
    .line 134
    return-object p1

    .line 135
    :cond_4
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$email:Ljava/lang/String;

    .line 136
    .line 137
    invoke-static {v1}, Lkotlin/text/g0;->v0(Ljava/lang/CharSequence;)Z

    .line 138
    .line 139
    .line 140
    move-result v1

    .line 141
    if-eqz v1, :cond_6

    .line 142
    .line 143
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 144
    .line 145
    const/4 v2, 0x3

    .line 146
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 147
    .line 148
    const-string v2, "\u90ae\u7bb1\u4e3a\u7a7a"

    .line 149
    .line 150
    invoke-static {v1, p1, v2, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    if-ne p1, v0, :cond_5

    .line 155
    .line 156
    goto/16 :goto_8

    .line 157
    .line 158
    :cond_5
    :goto_2
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 159
    .line 160
    return-object p1

    .line 161
    :cond_6
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 162
    .line 163
    invoke-static {v1}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/g;

    .line 164
    .line 165
    .line 166
    move-result-object v1

    .line 167
    invoke-interface {v1}, Lg8/g;->a()Lkotlinx/coroutines/flow/d;

    .line 168
    .line 169
    .line 170
    move-result-object v1

    .line 171
    iput-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 172
    .line 173
    const/4 v2, 0x4

    .line 174
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 175
    .line 176
    invoke-static {v1, p0}, Lkotlinx/coroutines/flow/f;->B(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 177
    .line 178
    .line 179
    move-result-object v1

    .line 180
    if-ne v1, v0, :cond_7

    .line 181
    .line 182
    goto/16 :goto_8

    .line 183
    .line 184
    :cond_7
    move-object v2, p1

    .line 185
    move-object p1, v1

    .line 186
    :goto_3
    move-object v1, p1

    .line 187
    check-cast v1, Ljava/lang/String;

    .line 188
    .line 189
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 190
    .line 191
    invoke-static {p1}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/g;

    .line 192
    .line 193
    .line 194
    move-result-object p1

    .line 195
    invoke-interface {p1}, Lg8/g;->getId()Lkotlinx/coroutines/flow/d;

    .line 196
    .line 197
    .line 198
    move-result-object p1

    .line 199
    iput-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 200
    .line 201
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$1:Ljava/lang/Object;

    .line 202
    .line 203
    const/4 v3, 0x5

    .line 204
    iput v3, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 205
    .line 206
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/f;->B(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    if-ne p1, v0, :cond_0

    .line 211
    .line 212
    goto/16 :goto_8

    .line 213
    .line 214
    :goto_4
    check-cast p1, Ljava/lang/Number;

    .line 215
    .line 216
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 217
    .line 218
    .line 219
    move-result v3

    .line 220
    const-string p1, ""

    .line 221
    .line 222
    invoke-static {v2, p1}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 223
    .line 224
    .line 225
    move-result p1

    .line 226
    const/4 v8, 0x0

    .line 227
    if-nez p1, :cond_c

    .line 228
    .line 229
    const/4 p1, -0x1

    .line 230
    if-ne v3, p1, :cond_8

    .line 231
    .line 232
    goto :goto_7

    .line 233
    :cond_8
    invoke-virtual {v7}, Lorg/orbitmvi/orbit/syntax/simple/b;->b()Ljava/lang/Object;

    .line 234
    .line 235
    .line 236
    move-result-object p1

    .line 237
    check-cast p1, Ltop/cycdm/cycapp/ui/email/x;

    .line 238
    .line 239
    invoke-virtual {p1}, Ltop/cycdm/cycapp/ui/email/x;->d()Ltop/cycdm/cycapp/utils/h;

    .line 240
    .line 241
    .line 242
    move-result-object p1

    .line 243
    instance-of v1, p1, Ltop/cycdm/cycapp/utils/h$c;

    .line 244
    .line 245
    if-nez v1, :cond_a

    .line 246
    .line 247
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 248
    .line 249
    iput-object v8, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 250
    .line 251
    iput-object v8, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$1:Ljava/lang/Object;

    .line 252
    .line 253
    const/4 v1, 0x7

    .line 254
    iput v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 255
    .line 256
    const-string v1, "\u8bf7\u91cd\u65b0\u53d1\u9001\u9a8c\u8bc1\u7801"

    .line 257
    .line 258
    invoke-static {p1, v7, v1, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 259
    .line 260
    .line 261
    move-result-object p1

    .line 262
    if-ne p1, v0, :cond_9

    .line 263
    .line 264
    goto :goto_8

    .line 265
    :cond_9
    :goto_5
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 266
    .line 267
    return-object p1

    .line 268
    :cond_a
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 269
    .line 270
    invoke-static {v1}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$getUserRep$p(Ltop/cycdm/cycapp/ui/email/EmailVM;)Lg8/h;

    .line 271
    .line 272
    .line 273
    move-result-object v1

    .line 274
    iget-object v4, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$email:Ljava/lang/String;

    .line 275
    .line 276
    iget-object v5, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->$code:Ljava/lang/String;

    .line 277
    .line 278
    check-cast p1, Ltop/cycdm/cycapp/utils/h$c;

    .line 279
    .line 280
    invoke-virtual {p1}, Ltop/cycdm/cycapp/utils/h$c;->a()Ljava/lang/Object;

    .line 281
    .line 282
    .line 283
    move-result-object p1

    .line 284
    move-object v6, p1

    .line 285
    check-cast v6, Ljava/lang/String;

    .line 286
    .line 287
    invoke-interface/range {v1 .. v6}, Lg8/h;->p(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lkotlinx/coroutines/flow/d;

    .line 288
    .line 289
    .line 290
    move-result-object p1

    .line 291
    new-instance v1, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1$1;

    .line 292
    .line 293
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 294
    .line 295
    invoke-direct {v1, v2, v7, v8}, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1$1;-><init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    .line 296
    .line 297
    .line 298
    invoke-static {p1, v1}, Lkotlinx/coroutines/flow/f;->i(Lkotlinx/coroutines/flow/d;Lkotlin/jvm/functions/Function3;)Lkotlinx/coroutines/flow/d;

    .line 299
    .line 300
    .line 301
    move-result-object p1

    .line 302
    new-instance v1, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1$2;

    .line 303
    .line 304
    iget-object v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 305
    .line 306
    invoke-direct {v1, v2, v7}, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1$2;-><init>(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;)V

    .line 307
    .line 308
    .line 309
    iput-object v8, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 310
    .line 311
    iput-object v8, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$1:Ljava/lang/Object;

    .line 312
    .line 313
    const/16 v2, 0x8

    .line 314
    .line 315
    iput v2, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 316
    .line 317
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/d;->collect(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 318
    .line 319
    .line 320
    move-result-object p1

    .line 321
    if-ne p1, v0, :cond_b

    .line 322
    .line 323
    goto :goto_8

    .line 324
    :cond_b
    :goto_6
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 325
    .line 326
    return-object p1

    .line 327
    :cond_c
    :goto_7
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->this$0:Ltop/cycdm/cycapp/ui/email/EmailVM;

    .line 328
    .line 329
    iput-object v8, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$0:Ljava/lang/Object;

    .line 330
    .line 331
    iput-object v8, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->L$1:Ljava/lang/Object;

    .line 332
    .line 333
    const/4 v1, 0x6

    .line 334
    iput v1, p0, Ltop/cycdm/cycapp/ui/email/EmailVM$bindEmail$1;->label:I

    .line 335
    .line 336
    const-string v1, "\u8bf7\u5148\u767b\u9646\u8d26\u53f7"

    .line 337
    .line 338
    invoke-static {p1, v7, v1, p0}, Ltop/cycdm/cycapp/ui/email/EmailVM;->access$sendSnackBar(Ltop/cycdm/cycapp/ui/email/EmailVM;Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 339
    .line 340
    .line 341
    move-result-object p1

    .line 342
    if-ne p1, v0, :cond_d

    .line 343
    .line 344
    :goto_8
    return-object v0

    .line 345
    :cond_d
    :goto_9
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 346
    .line 347
    return-object p1

    .line 348
    nop

    .line 349
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
