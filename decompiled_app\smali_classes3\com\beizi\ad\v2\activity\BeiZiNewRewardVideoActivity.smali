.class public Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;
.super Landroid/app/Activity;
.source "SourceFile"


# instance fields
.field private A:Landroid/widget/TextView;

.field private B:Landroid/widget/TextView;

.field private C:Landroid/widget/TextView;

.field private D:Landroid/widget/TextView;

.field private E:Landroid/widget/TextView;

.field private F:Landroid/widget/TextView;

.field private G:Landroid/widget/TextView;

.field private H:Landroid/widget/TextView;

.field private I:Landroid/widget/TextView;

.field private J:Landroid/widget/TextView;

.field private K:Landroid/widget/TextView;

.field private L:Landroid/widget/TextView;

.field private M:Landroid/widget/TextView;

.field private N:Landroid/widget/TextView;

.field private O:Landroid/widget/TextView;

.field private P:Landroid/widget/FrameLayout;

.field private Q:Landroid/widget/FrameLayout;

.field private R:Lcom/beizi/ad/internal/view/CustomRoundImageView;

.field private S:Lcom/beizi/ad/internal/view/CustomRoundImageView;

.field private T:Lcom/beizi/ad/internal/view/CustomRoundImageView;

.field private U:Landroid/widget/VideoView;

.field private V:Landroid/widget/ProgressBar;

.field private W:Z

.field private X:Z

.field private Y:Z

.field private Z:I

.field private a:Lcom/beizi/ad/v2/e/b;

.field private aA:I

.field private aB:I

.field private aC:I

.field private aD:I

.field private aE:I

.field private aF:I

.field private aG:I

.field private aH:I

.field private aI:Z

.field private aJ:Z

.field private aK:Z

.field private aL:Z

.field private aM:Ljava/lang/String;

.field private aN:I

.field private aO:Z

.field private aP:Z

.field private aQ:Z

.field private aR:Z

.field private aS:Z

.field private aT:Z

.field private aU:Landroid/os/Handler;

.field private aa:I

.field private ab:Z

.field private ac:Landroid/media/MediaPlayer;

.field private ad:Z

.field private ae:I

.field private af:Z

.field private ag:I

.field private ah:Z

.field private ai:Ljava/lang/String;

.field private aj:Ljava/lang/String;

.field private ak:Ljava/lang/String;

.field private al:Ljava/lang/String;

.field private am:Ljava/lang/String;

.field private an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

.field private ao:Lcom/beizi/ad/a/a/c;

.field private ap:Lcom/beizi/ad/a/a/e;

.field private aq:Lcom/beizi/ad/a/a/d;

.field private ar:Lcom/beizi/ad/a/a/a;

.field private as:Lcom/beizi/ad/a/a/b;

.field private at:Z

.field private au:Z

.field private av:Ljava/lang/String;

.field private aw:Ljava/lang/String;

.field private ax:Ljava/lang/String;

.field private ay:J

.field private az:Z

.field private b:Lcom/beizi/ad/internal/f/c;

.field private c:Landroid/widget/RelativeLayout;

.field private d:Landroid/widget/RelativeLayout;

.field private e:Landroid/widget/RelativeLayout;

.field private f:Landroid/widget/LinearLayout;

.field private g:Landroid/widget/LinearLayout;

.field private h:Landroid/widget/LinearLayout;

.field private i:Landroid/widget/LinearLayout;

.field private j:Landroid/widget/LinearLayout;

.field private k:Landroid/widget/LinearLayout;

.field private l:Landroid/widget/LinearLayout;

.field private m:Landroid/widget/LinearLayout;

.field private n:Landroid/widget/LinearLayout;

.field private o:Landroid/view/View;

.field private p:Landroid/widget/ImageView;

.field private q:Landroid/widget/ImageView;

.field private r:Landroid/widget/ImageView;

.field private s:Landroid/widget/ImageView;

.field private t:Landroid/widget/ImageView;

.field private u:Landroid/widget/ImageView;

.field private v:Landroid/widget/TextView;

.field private w:Landroid/widget/TextView;

.field private x:Landroid/widget/TextView;

.field private y:Landroid/widget/TextView;

.field private z:Landroid/widget/TextView;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->W:Z

    .line 6
    .line 7
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Y:Z

    .line 8
    .line 9
    const/16 v1, 0x1e

    .line 10
    .line 11
    iput v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    .line 12
    .line 13
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ad:Z

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    iput-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ah:Z

    .line 17
    .line 18
    iput-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->at:Z

    .line 19
    .line 20
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->az:Z

    .line 21
    .line 22
    iput v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    .line 23
    .line 24
    const/4 v0, 0x2

    .line 25
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aC:I

    .line 26
    .line 27
    const/4 v0, 0x5

    .line 28
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aD:I

    .line 29
    .line 30
    const/16 v0, 0xf

    .line 31
    .line 32
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aE:I

    .line 33
    .line 34
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    .line 35
    .line 36
    const/16 v0, 0xa

    .line 37
    .line 38
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aG:I

    .line 39
    .line 40
    new-instance v0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$1;

    .line 41
    .line 42
    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-direct {v0, p0, v1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$1;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Landroid/os/Looper;)V

    .line 47
    .line 48
    .line 49
    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aU:Landroid/os/Handler;

    .line 50
    .line 51
    return-void
.end method

.method public static synthetic A(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/widget/TextView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->w:Landroid/widget/TextView;

    return-object p0
.end method

.method private A()V
    .locals 4

    .line 2
    :try_start_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ah:Z

    if-eqz v0, :cond_0

    goto/16 :goto_2

    .line 3
    :cond_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ad:Z

    if-nez v0, :cond_1

    goto/16 :goto_2

    .line 4
    :cond_1
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aP:Z

    if-eqz v0, :cond_2

    goto/16 :goto_2

    .line 5
    :cond_2
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aQ:Z

    if-eqz v0, :cond_3

    goto/16 :goto_2

    .line 6
    :cond_3
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aE:I

    if-lez v0, :cond_4

    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    if-nez v0, :cond_4

    goto/16 :goto_2

    :catch_0
    move-exception v0

    goto/16 :goto_1

    .line 7
    :cond_4
    iget-wide v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-gtz v0, :cond_5

    goto/16 :goto_2

    .line 8
    :cond_5
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    if-eq v0, v1, :cond_6

    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aC:I

    if-ne v0, v1, :cond_e

    .line 9
    :cond_6
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    sub-long/2addr v0, v2

    .line 10
    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    iget v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    if-ne v2, v3, :cond_8

    .line 11
    iget-boolean v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aK:Z

    if-nez v2, :cond_7

    goto :goto_2

    .line 12
    :cond_7
    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    mul-int/lit16 v2, v2, 0x3e8

    int-to-long v2, v2

    cmp-long v2, v0, v2

    if-lez v2, :cond_9

    goto :goto_0

    .line 13
    :cond_8
    iget v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aC:I

    if-ne v2, v3, :cond_9

    .line 14
    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    mul-int/lit16 v2, v2, 0x3e8

    int-to-long v2, v2

    cmp-long v2, v0, v2

    if-lez v2, :cond_9

    .line 15
    :goto_0
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p()V

    .line 16
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->z()V

    .line 17
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->f()V

    return-void

    :cond_9
    const/16 v2, -0x3e7

    .line 18
    iput v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aH:I

    .line 19
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->H:Landroid/widget/TextView;

    if-eqz v2, :cond_a

    .line 20
    const-string v3, "\u6d4f\u89c8\u65f6\u95f4\u4e0d\u8db3\uff0c\u65e0\u6cd5\u63d0\u524d\u83b7\u53d6\u5956\u52b1"

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 21
    :cond_a
    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    mul-int/lit16 v2, v2, 0x3e8

    int-to-long v2, v2

    sub-long/2addr v2, v0

    long-to-int v0, v2

    div-int/lit16 v0, v0, 0x3e8

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    if-nez v0, :cond_b

    const/4 v0, 0x1

    .line 22
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    .line 23
    :cond_b
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->D:Landroid/widget/TextView;

    if-eqz v0, :cond_c

    .line 24
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, "\u79d2"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 25
    :cond_c
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->E:Landroid/widget/TextView;

    if-eqz v0, :cond_d

    .line 26
    const-string v1, "\u7ee7\u7eed\u4f53\u9a8c\u5185\u5bb9"

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 27
    :cond_d
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->J:Landroid/widget/TextView;

    if-eqz v0, :cond_e

    .line 28
    const-string v1, "\u7ee7\u7eed\u4f53\u9a8c\uff0c\u63d0\u524d\u83b7\u53d6\u5956\u52b1"

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 29
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_e
    :goto_2
    return-void
.end method

.method private B()V
    .locals 3

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->e:Landroid/widget/RelativeLayout;

    if-nez v0, :cond_0

    goto :goto_1

    .line 3
    :cond_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aL:Z

    if-eqz v0, :cond_1

    .line 4
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->f()V

    return-void

    :catch_0
    move-exception v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x1

    .line 5
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aP:Z

    .line 6
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->C()V

    .line 7
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->e:Landroid/widget/RelativeLayout;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 8
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->L:Landroid/widget/TextView;

    if-eqz v0, :cond_2

    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->v:Landroid/widget/TextView;

    if-eqz v1, :cond_2

    .line 9
    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aa:I

    if-lez v1, :cond_2

    .line 10
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "\u4ec5\u9700"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aa:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, "\u79d2\u5373\u53ef\u83b7\u5f97\u5956\u52b1"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 11
    :cond_2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->M:Landroid/widget/TextView;

    if-eqz v0, :cond_3

    .line 12
    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$15;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$15;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 13
    :cond_3
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->N:Landroid/widget/TextView;

    if-eqz v0, :cond_4

    .line 14
    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$16;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$16;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    :cond_4
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->e:Landroid/widget/RelativeLayout;

    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$17;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$17;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 16
    :goto_0
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_1
    return-void
.end method

.method public static synthetic B(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->n()V

    return-void
.end method

.method private C()V
    .locals 2

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    if-eqz v0, :cond_0

    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ab:Z

    if-eqz v1, :cond_0

    .line 3
    invoke-virtual {v0}, Landroid/widget/VideoView;->getCurrentPosition()I

    move-result v0

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ae:I

    .line 4
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i()V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    .line 5
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ap:Lcom/beizi/ad/a/a/e;

    if-eqz v0, :cond_1

    .line 6
    invoke-virtual {v0}, Lcom/beizi/ad/a/a/e;->b()V

    .line 7
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ar:Lcom/beizi/ad/a/a/a;

    if-eqz v0, :cond_2

    .line 8
    invoke-virtual {v0}, Lcom/beizi/ad/a/a/a;->b()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 9
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_2
    return-void
.end method

.method public static synthetic C(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aO:Z

    return p0
.end method

.method private D()V
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aU:Landroid/os/Handler;

    if-eqz v0, :cond_0

    const/16 v1, 0x271a

    .line 3
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    :cond_0
    const/4 v0, 0x0

    .line 4
    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aU:Landroid/os/Handler;

    return-void
.end method

.method public static synthetic D(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->B()V

    return-void
.end method

.method public static synthetic E(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/widget/ImageView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    return-object p0
.end method

.method private E()V
    .locals 3

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->m()Lcom/beizi/ad/internal/f/c;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v1}, Lcom/beizi/ad/v2/a/b;->e()Ljava/lang/String;

    move-result-object v1

    .line 5
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    invoke-virtual {v0, v2, v1}, Lcom/beizi/ad/internal/f/c;->b(Landroid/view/View;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 6
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public static synthetic F(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aE:I

    return p0
.end method

.method private F()V
    .locals 3

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->m()Lcom/beizi/ad/internal/f/c;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v1}, Lcom/beizi/ad/v2/a/b;->e()Ljava/lang/String;

    move-result-object v1

    .line 5
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    invoke-virtual {v0, v2, v1}, Lcom/beizi/ad/internal/f/c;->f(Landroid/view/View;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 6
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public static synthetic G(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->z()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic H(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->az:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic I(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/widget/ImageView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->r:Landroid/widget/ImageView;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic J(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/widget/RelativeLayout;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->e:Landroid/widget/RelativeLayout;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Landroid/media/MediaPlayer;)Landroid/media/MediaPlayer;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ac:Landroid/media/MediaPlayer;

    return-object p1
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/os/Handler;
    .locals 0

    .line 2
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aU:Landroid/os/Handler;

    return-object p0
.end method

.method private a()V
    .locals 1

    .line 8
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_container_rl:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/RelativeLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    .line 9
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_video_vv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/VideoView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    .line 10
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_progress_bar:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ProgressBar;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->V:Landroid/widget/ProgressBar;

    .line 11
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_complaint_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->O:Landroid/widget/TextView;

    .line 12
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_voice_iv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    .line 13
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_voice_outline_view:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->o:Landroid/view/View;

    .line 14
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_get_rewards_container_ll:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->f:Landroid/widget/LinearLayout;

    .line 15
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_gift_iv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->q:Landroid/widget/ImageView;

    .line 16
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_get_rewards_countdown_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->v:Landroid/widget/TextView;

    .line 17
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_get_rewards_close_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->w:Landroid/widget/TextView;

    .line 18
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_app_interaction_container_ll:I

    .line 19
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->g:Landroid/widget/LinearLayout;

    .line 20
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_app_icon_iv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/beizi/ad/internal/view/CustomRoundImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->R:Lcom/beizi/ad/internal/view/CustomRoundImageView;

    .line 21
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_app_title_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->x:Landroid/widget/TextView;

    .line 22
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_app_subtitle_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->y:Landroid/widget/TextView;

    .line 23
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_logo_container_fl:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/FrameLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->P:Landroid/widget/FrameLayout;

    .line 24
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_interaction_container_ll:I

    .line 25
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h:Landroid/widget/LinearLayout;

    .line 26
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_interaction_iv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->r:Landroid/widget/ImageView;

    .line 27
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_interaction_title_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->z:Landroid/widget/TextView;

    .line 28
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_app_download_info_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->K:Landroid/widget/TextView;

    .line 29
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_container_rl:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/RelativeLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d:Landroid/widget/RelativeLayout;

    .line 30
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_image_iv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->s:Landroid/widget/ImageView;

    .line 31
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_app_icon_iv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/beizi/ad/internal/view/CustomRoundImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->S:Lcom/beizi/ad/internal/view/CustomRoundImageView;

    .line 32
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_app_title_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->A:Landroid/widget/TextView;

    .line 33
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_app_subtitle_tv:I

    .line 34
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->B:Landroid/widget/TextView;

    .line 35
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_interaction_container_ll:I

    .line 36
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i:Landroid/widget/LinearLayout;

    .line 37
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_interaction_iv:I

    .line 38
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->t:Landroid/widget/ImageView;

    .line 39
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_interaction_title_tv:I

    .line 40
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->C:Landroid/widget/TextView;

    .line 41
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_ad_detainment_logo_container_fl:I

    .line 42
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/FrameLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Q:Landroid/widget/FrameLayout;

    .line 43
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_container_ll:I

    .line 44
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->j:Landroid/widget/LinearLayout;

    .line 45
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_content_container_ll:I

    .line 46
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->k:Landroid/widget/LinearLayout;

    .line 47
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_tip_sub_title_tv:I

    .line 48
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->E:Landroid/widget/TextView;

    .line 49
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_in_background_time_tv:I

    .line 50
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->D:Landroid/widget/TextView;

    .line 51
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_app_info_ll:I

    .line 52
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->l:Landroid/widget/LinearLayout;

    .line 53
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_app_icon_iv:I

    .line 54
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/beizi/ad/internal/view/CustomRoundImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->T:Lcom/beizi/ad/internal/view/CustomRoundImageView;

    .line 55
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_app_title_tv:I

    .line 56
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->F:Landroid/widget/TextView;

    .line 57
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_app_subtitle_tv:I

    .line 58
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->G:Landroid/widget/TextView;

    .line 59
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_interaction_container_ll:I

    .line 60
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->m:Landroid/widget/LinearLayout;

    .line 61
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_interaction_title_tv:I

    .line 62
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->J:Landroid/widget/TextView;

    .line 63
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_interaction_iv:I

    .line 64
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->u:Landroid/widget/ImageView;

    .line 65
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_privilege_dialog_countdown_tv:I

    .line 66
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->H:Landroid/widget/TextView;

    .line 67
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_cancel_privilege_dialog_tv:I

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->I:Landroid/widget/TextView;

    .line 68
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_quit_dialog_container_rl:I

    .line 69
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/RelativeLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->e:Landroid/widget/RelativeLayout;

    .line 70
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_quit_dialog_content_container_ll:I

    .line 71
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->n:Landroid/widget/LinearLayout;

    .line 72
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_quit_dialog_get_reward_tv:I

    .line 73
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->L:Landroid/widget/TextView;

    .line 74
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_quit_dialog_continue_play_tv:I

    .line 75
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->M:Landroid/widget/TextView;

    .line 76
    sget v0, Lcom/sjm/sjmdaly/R$id;->beizi_reward_video_quit_dialog_close_ad_tv:I

    .line 77
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->N:Landroid/widget/TextView;

    return-void
.end method

.method private a(I)V
    .locals 2

    .line 98
    :try_start_0
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->j()Ljava/lang/String;

    move-result-object v0

    .line 99
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_1

    .line 100
    :cond_0
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    invoke-virtual {v1, v0}, Landroid/widget/VideoView;->setVideoPath(Ljava/lang/String;)V

    if-nez p1, :cond_1

    .line 101
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    invoke-virtual {p1}, Landroid/view/View;->requestFocus()Z

    return-void

    :catch_0
    move-exception p1

    goto :goto_0

    .line 102
    :cond_1
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    invoke-virtual {p1}, Landroid/widget/VideoView;->start()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 103
    :goto_0
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_1
    return-void
.end method

.method private a(II)V
    .locals 6

    int-to-double v0, p2

    int-to-double p1, p1

    const-wide/high16 v2, 0x3fd0000000000000L    # 0.25

    mul-double/2addr v2, p1

    cmpl-double v2, v0, v2

    const/4 v3, 0x1

    if-ltz v2, :cond_0

    .line 144
    iget-boolean v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aR:Z

    if-nez v2, :cond_0

    .line 145
    iput-boolean v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aR:Z

    const/16 v2, 0x19

    .line 146
    invoke-direct {p0, v2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d(I)V

    :cond_0
    const-wide/high16 v4, 0x3fe0000000000000L    # 0.5

    mul-double/2addr v4, p1

    cmpl-double v2, v0, v4

    if-ltz v2, :cond_1

    .line 147
    iget-boolean v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aS:Z

    if-nez v2, :cond_1

    .line 148
    iput-boolean v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aS:Z

    const/16 v2, 0x32

    .line 149
    invoke-direct {p0, v2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d(I)V

    :cond_1
    const-wide/high16 v4, 0x3fe8000000000000L    # 0.75

    mul-double/2addr p1, v4

    cmpl-double p1, v0, p1

    if-ltz p1, :cond_2

    .line 150
    iget-boolean p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aT:Z

    if-nez p1, :cond_2

    .line 151
    iput-boolean v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aT:Z

    const/16 p1, 0x4b

    .line 152
    invoke-direct {p0, p1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d(I)V

    :cond_2
    return-void
.end method

.method private a(Landroid/view/View;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    .line 143
    :cond_0
    new-instance v0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$14;

    invoke-direct {v0, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$14;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {p1, v0}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    return-void
.end method

.method private a(Landroid/widget/ImageView;)V
    .locals 3

    if-nez p1, :cond_0

    goto :goto_2

    .line 128
    :cond_0
    :try_start_0
    const-string v0, "shake"

    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ax:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    .line 129
    invoke-virtual {p1, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 130
    sget v0, Lcom/sjm/sjmdaly/R$mipmap;->beizi_interaction_icon_shake:I

    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 131
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aw:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 132
    invoke-static {p0}, Lcom/beizi/ad/internal/h/i;->a(Landroid/content/Context;)Lcom/beizi/ad/internal/h/i;

    move-result-object v0

    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aw:Ljava/lang/String;

    new-instance v2, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$11;

    invoke-direct {v2, p0, p1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$11;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Landroid/widget/ImageView;)V

    invoke-virtual {v0, v1, v2}, Lcom/beizi/ad/internal/h/i;->b(Ljava/lang/String;Lcom/beizi/ad/internal/h/i$a;)V

    goto :goto_0

    :catch_0
    move-exception p1

    goto :goto_1

    .line 133
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ap:Lcom/beizi/ad/a/a/e;

    if-eqz v0, :cond_4

    .line 134
    invoke-virtual {v0, p1}, Lcom/beizi/ad/a/a/e;->a(Landroid/widget/ImageView;)V

    return-void

    .line 135
    :cond_2
    const-string v0, "eulerAngle"

    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ax:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 136
    invoke-virtual {p1, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 137
    sget v0, Lcom/sjm/sjmdaly/R$mipmap;->beizi_interaction_icon_euler_angle:I

    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 138
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aw:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 139
    invoke-static {p0}, Lcom/beizi/ad/internal/h/i;->a(Landroid/content/Context;)Lcom/beizi/ad/internal/h/i;

    move-result-object v0

    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aw:Ljava/lang/String;

    new-instance v2, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$13;

    invoke-direct {v2, p0, p1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$13;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Landroid/widget/ImageView;)V

    invoke-virtual {v0, v1, v2}, Lcom/beizi/ad/internal/h/i;->b(Ljava/lang/String;Lcom/beizi/ad/internal/h/i$a;)V

    .line 140
    :cond_3
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ar:Lcom/beizi/ad/a/a/a;

    if-eqz v0, :cond_4

    .line 141
    invoke-virtual {v0, p1}, Lcom/beizi/ad/a/a/a;->a(Landroid/widget/ImageView;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 142
    :goto_1
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_4
    :goto_2
    return-void
.end method

.method private a(Landroid/widget/TextView;Landroid/widget/TextView;)V
    .locals 3

    const/16 v0, 0x8

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 89
    :try_start_0
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aj:Ljava/lang/String;

    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_0

    .line 90
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 91
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aj:Ljava/lang/String;

    invoke-virtual {p1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    :catch_0
    move-exception p1

    goto :goto_1

    .line 92
    :cond_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    :cond_1
    :goto_0
    if-eqz p2, :cond_3

    .line 93
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ak:Ljava/lang/String;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_2

    .line 94
    invoke-virtual {p2, v1}, Landroid/view/View;->setVisibility(I)V

    .line 95
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ak:Ljava/lang/String;

    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void

    .line 96
    :cond_2
    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 97
    :goto_1
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_3
    return-void
.end method

.method private a(Lcom/beizi/ad/internal/view/CustomRoundImageView;I)V
    .locals 3

    .line 78
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    if-nez p1, :cond_1

    goto :goto_1

    .line 79
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ai:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_2

    const/16 p2, 0x8

    .line 80
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    return-void

    :catch_0
    move-exception p1

    goto :goto_0

    :cond_2
    int-to-float p2, p2

    .line 81
    invoke-static {p0, p2}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result p2

    const/4 v0, 0x0

    .line 82
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 83
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout$LayoutParams;

    .line 84
    iput p2, v0, Landroid/widget/LinearLayout$LayoutParams;->width:I

    .line 85
    iput p2, v0, Landroid/widget/LinearLayout$LayoutParams;->height:I

    .line 86
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    const/4 v0, 0x0

    .line 87
    invoke-static {v0}, Lcom/beizi/ad/internal/h/i;->a(Landroid/content/Context;)Lcom/beizi/ad/internal/h/i;

    move-result-object v0

    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ai:Ljava/lang/String;

    new-instance v2, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$19;

    invoke-direct {v2, p0, p1, p2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$19;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Lcom/beizi/ad/internal/view/CustomRoundImageView;I)V

    invoke-virtual {v0, v1, v2}, Lcom/beizi/ad/internal/h/i;->a(Ljava/lang/String;Lcom/beizi/ad/internal/h/i$a;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 88
    :goto_0
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_1
    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;I)V
    .locals 0

    .line 3
    invoke-direct {p0, p1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c(I)V

    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;II)V
    .locals 0

    .line 4
    invoke-direct {p0, p1, p2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(II)V

    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Landroid/widget/ImageView;)V
    .locals 0

    .line 5
    invoke-direct {p0, p1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/ImageView;)V

    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 0

    .line 6
    invoke-direct/range {p0 .. p9}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method

.method private a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 3

    .line 104
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-nez v0, :cond_0

    goto/16 :goto_2

    .line 105
    :cond_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aP:Z

    if-eqz v0, :cond_1

    goto/16 :goto_2

    .line 106
    :cond_1
    invoke-virtual {p0}, Landroid/app/Activity;->hasWindowFocus()Z

    move-result v0

    if-nez v0, :cond_2

    goto/16 :goto_2

    .line 107
    :cond_2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->m()Lcom/beizi/ad/internal/f/c;

    move-result-object v0

    if-nez v0, :cond_3

    goto :goto_2

    .line 108
    :cond_3
    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    if-ne v1, v2, :cond_4

    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aK:Z

    if-eqz v1, :cond_4

    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aE:I

    if-lez v1, :cond_4

    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aL:Z

    if-nez v1, :cond_4

    .line 109
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->x()V

    goto :goto_0

    :catch_0
    move-exception v0

    move-object p1, v0

    goto :goto_1

    .line 110
    :cond_4
    :goto_0
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v1}, Lcom/beizi/ad/v2/a/b;->e()Ljava/lang/String;

    move-result-object v1

    move-object v2, p3

    .line 111
    new-instance p3, Lcom/beizi/ad/model/c;

    invoke-direct {p3}, Lcom/beizi/ad/model/c;-><init>()V

    .line 112
    invoke-virtual {p3, p1}, Lcom/beizi/ad/model/c;->a(Ljava/lang/String;)V

    .line 113
    invoke-virtual {p3, p5}, Lcom/beizi/ad/model/c;->e(Ljava/lang/String;)V

    .line 114
    invoke-virtual {p3, p2}, Lcom/beizi/ad/model/c;->b(Ljava/lang/String;)V

    .line 115
    invoke-virtual {p3, p6}, Lcom/beizi/ad/model/c;->f(Ljava/lang/String;)V

    .line 116
    invoke-virtual {p3, v2}, Lcom/beizi/ad/model/c;->c(Ljava/lang/String;)V

    .line 117
    invoke-virtual {p3, p7}, Lcom/beizi/ad/model/c;->g(Ljava/lang/String;)V

    .line 118
    invoke-virtual {p3, p4}, Lcom/beizi/ad/model/c;->d(Ljava/lang/String;)V

    .line 119
    invoke-virtual {p3, p8}, Lcom/beizi/ad/model/c;->h(Ljava/lang/String;)V

    const/4 v2, 0x1

    .line 120
    invoke-virtual {v0, v2}, Lcom/beizi/ad/internal/f/c;->a(Z)V

    .line 121
    iget-object p2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    .line 122
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p4

    invoke-static {p4, p5}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    move-result-object p4

    .line 123
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p5

    const-wide/16 p7, 0xa

    add-long/2addr p5, p7

    invoke-static {p5, p6}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    move-result-object p5

    iget-boolean p6, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ad:Z

    move p8, p9

    move-object p1, v0

    move-object p7, v1

    .line 124
    invoke-virtual/range {p1 .. p8}, Lcom/beizi/ad/internal/f/c;->a(Landroid/view/View;Lcom/beizi/ad/model/c;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;I)V

    .line 125
    iput-boolean v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ad:Z

    .line 126
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {p1}, Lcom/beizi/ad/v2/e/b;->x()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 127
    :goto_1
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_2
    return-void
.end method

.method public static synthetic a(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Z)Z
    .locals 0

    .line 7
    iput-boolean p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aO:Z

    return p1
.end method

.method public static synthetic b(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;I)I
    .locals 0

    .line 1
    iput p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aa:I

    return p1
.end method

.method private b()V
    .locals 3

    .line 4
    :try_start_0
    sget-object v0, Lcom/beizi/ad/v2/e/b;->C:Lcom/beizi/ad/v2/e/b;

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    .line 5
    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->a()Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->j()Z

    move-result v0

    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->au:Z

    .line 7
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v0}, Lcom/beizi/ad/v2/e/b;->t()Z

    move-result v0

    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->X:Z

    .line 8
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    if-nez v0, :cond_0

    goto/16 :goto_3

    .line 9
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getTemplate()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    .line 10
    iput-boolean v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->W:Z

    goto :goto_0

    :catch_0
    move-exception v0

    goto/16 :goto_2

    .line 11
    :cond_1
    iput-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->W:Z

    .line 12
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->m()Lcom/beizi/ad/internal/f/c;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    if-nez v0, :cond_2

    goto/16 :goto_3

    .line 13
    :cond_2
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->f()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->am:Ljava/lang/String;

    .line 14
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->N()Z

    move-result v0

    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ab:Z

    if-eqz v0, :cond_3

    .line 15
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->M()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->al:Ljava/lang/String;

    goto :goto_1

    .line 16
    :cond_3
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->L()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->al:Ljava/lang/String;

    .line 17
    :goto_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->Y()I

    move-result v0

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aN:I

    .line 18
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->O()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ai:Ljava/lang/String;

    .line 19
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->n()Z

    move-result v0

    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->af:Z

    .line 20
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->s()I

    move-result v0

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ag:I

    .line 21
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->J()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aj:Ljava/lang/String;

    .line 22
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->K()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ak:Ljava/lang/String;

    .line 23
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->m()Z

    move-result v0

    xor-int/2addr v0, v2

    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Y:Z

    .line 24
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->r()I

    move-result v0

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    if-gtz v0, :cond_4

    const/16 v0, 0x1e

    .line 25
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    .line 26
    :cond_4
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aN:I

    if-le v0, v1, :cond_5

    .line 27
    iput v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    .line 28
    :cond_5
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->X()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aM:Ljava/lang/String;

    .line 29
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    .line 30
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->Z()Lcom/beizi/ad/model/b$k;

    move-result-object v0

    if-eqz v0, :cond_6

    .line 31
    invoke-virtual {v0}, Lcom/beizi/ad/model/b$k;->a()I

    move-result v1

    iput v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    .line 32
    invoke-virtual {v0}, Lcom/beizi/ad/model/b$k;->b()I

    move-result v1

    iput v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aD:I

    .line 33
    invoke-virtual {v0}, Lcom/beizi/ad/model/b$k;->c()I

    move-result v1

    iput v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aG:I

    .line 34
    iput v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aH:I

    .line 35
    invoke-virtual {v0}, Lcom/beizi/ad/model/b$k;->d()I

    move-result v0

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aE:I

    .line 36
    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    .line 37
    :cond_6
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aa:I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 38
    :goto_2
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_3
    return-void
.end method

.method private b(I)V
    .locals 4

    .line 39
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->v:Landroid/widget/TextView;

    .line 40
    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aa:I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const-string v2, "\u5df2\u83b7\u5f97\u5956\u52b1"

    if-gtz v1, :cond_0

    .line 41
    :try_start_1
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 42
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p()V

    return-void

    :catch_0
    move-exception p1

    goto :goto_0

    .line 43
    :cond_0
    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aK:Z

    if-nez v1, :cond_5

    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aL:Z

    if-eqz v1, :cond_1

    goto :goto_1

    .line 44
    :cond_1
    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    iget v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    if-ne v1, v3, :cond_3

    .line 45
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->q:Landroid/widget/ImageView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    if-lez p1, :cond_2

    .line 46
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->v:Landroid/widget/TextView;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p1, "\u79d2\u540e\u70b9\u51fb\u5e7f\u544a\u53ef\u9886\u53d6\u5956\u52b1"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void

    :cond_2
    const/4 p1, 0x1

    .line 47
    iput-boolean p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aK:Z

    .line 48
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->v:Landroid/widget/TextView;

    const-string v0, "\u7acb\u5373\u9886\u53d6"

    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void

    :cond_3
    if-lez p1, :cond_4

    .line 49
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p1, "\u79d2\u540e\u53ef\u9886\u53d6\u5956\u52b1"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void

    .line 50
    :cond_4
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 51
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p()V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    return-void

    .line 52
    :goto_0
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_5
    :goto_1
    return-void
.end method

.method public static synthetic b(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->y()V

    return-void
.end method

.method public static synthetic b(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Z)Z
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->at:Z

    return p1
.end method

.method public static synthetic c(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/widget/VideoView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    return-object p0
.end method

.method private c()V
    .locals 10

    const/high16 v0, 0x41700000    # 15.0f

    .line 4
    :try_start_0
    invoke-static {p0, v0}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v0

    const/high16 v1, 0x41f00000    # 30.0f

    .line 5
    invoke-static {p0, v1}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v1

    .line 6
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    if-eqz v2, :cond_0

    iget-boolean v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ab:Z

    if-eqz v3, :cond_0

    .line 7
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v2

    check-cast v2, Landroid/widget/RelativeLayout$LayoutParams;

    .line 8
    iput v1, v2, Landroid/widget/RelativeLayout$LayoutParams;->width:I

    .line 9
    iput v1, v2, Landroid/widget/RelativeLayout$LayoutParams;->height:I

    .line 10
    iget-object v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    invoke-virtual {v3, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto/16 :goto_1

    .line 11
    :cond_0
    :goto_0
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->o:Landroid/view/View;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const-string v3, "#66FFFFFF"

    const/4 v4, 0x1

    const/4 v5, 0x0

    if-eqz v2, :cond_1

    .line 12
    :try_start_1
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v2

    check-cast v2, Landroid/widget/RelativeLayout$LayoutParams;

    .line 13
    iput v1, v2, Landroid/widget/RelativeLayout$LayoutParams;->width:I

    .line 14
    iput v1, v2, Landroid/widget/RelativeLayout$LayoutParams;->height:I

    .line 15
    iget-object v6, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->o:Landroid/view/View;

    invoke-virtual {v6, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 16
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->o:Landroid/view/View;

    div-int/lit8 v6, v1, 0x2

    invoke-static {v2, v5, v4, v3, v6}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 17
    :cond_1
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->f:Landroid/widget/LinearLayout;

    if-eqz v2, :cond_2

    .line 18
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v2

    check-cast v2, Landroid/widget/RelativeLayout$LayoutParams;

    const/4 v6, -0x2

    .line 19
    iput v6, v2, Landroid/widget/RelativeLayout$LayoutParams;->width:I

    .line 20
    iput v1, v2, Landroid/widget/RelativeLayout$LayoutParams;->height:I

    .line 21
    iget-object v6, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->f:Landroid/widget/LinearLayout;

    invoke-virtual {v6, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 22
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->f:Landroid/widget/LinearLayout;

    const-string v6, "#66000000"

    invoke-static {v2, v6, v4, v3, v0}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 23
    :cond_2
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h:Landroid/widget/LinearLayout;

    const/high16 v3, 0x42300000    # 44.0f

    const/4 v4, -0x1

    if-eqz v2, :cond_3

    .line 24
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v2

    check-cast v2, Landroid/widget/LinearLayout$LayoutParams;

    .line 25
    iput v4, v2, Landroid/widget/LinearLayout$LayoutParams;->width:I

    .line 26
    invoke-static {p0, v3}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v6

    iput v6, v2, Landroid/widget/LinearLayout$LayoutParams;->height:I

    .line 27
    iget-object v6, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h:Landroid/widget/LinearLayout;

    invoke-virtual {v6, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 28
    :cond_3
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d:Landroid/widget/RelativeLayout;

    if-eqz v2, :cond_4

    .line 29
    invoke-static {p0}, Lcom/beizi/ad/lance/a/q;->i(Landroid/content/Context;)I

    move-result v2

    .line 30
    invoke-static {p0}, Lcom/beizi/ad/lance/a/q;->j(Landroid/content/Context;)I

    move-result v6

    .line 31
    iget-object v7, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d:Landroid/widget/RelativeLayout;

    .line 32
    invoke-virtual {v7}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v7

    check-cast v7, Landroid/widget/RelativeLayout$LayoutParams;

    .line 33
    iput v2, v7, Landroid/widget/RelativeLayout$LayoutParams;->width:I

    .line 34
    iput v6, v7, Landroid/widget/RelativeLayout$LayoutParams;->height:I

    .line 35
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d:Landroid/widget/RelativeLayout;

    invoke-virtual {v2, v7}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 36
    :cond_4
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i:Landroid/widget/LinearLayout;

    const/high16 v6, 0x42480000    # 50.0f

    if-eqz v2, :cond_5

    .line 37
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v2

    check-cast v2, Landroid/widget/RelativeLayout$LayoutParams;

    .line 38
    iput v4, v2, Landroid/widget/RelativeLayout$LayoutParams;->width:I

    .line 39
    invoke-static {p0, v6}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v7

    .line 40
    iput v7, v2, Landroid/widget/RelativeLayout$LayoutParams;->height:I

    .line 41
    iget-object v7, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i:Landroid/widget/LinearLayout;

    invoke-virtual {v7, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 42
    :cond_5
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->k:Landroid/widget/LinearLayout;
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    const-string v7, "#FFFFFF"

    const/4 v8, 0x0

    if-eqz v2, :cond_6

    .line 43
    :try_start_2
    invoke-static {v2, v7, v8, v5, v0}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 44
    :cond_6
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->l:Landroid/widget/LinearLayout;

    if-eqz v2, :cond_7

    .line 45
    const-string v9, "#EBF1FF"

    div-int/lit8 v1, v1, 0x3

    invoke-static {v2, v9, v8, v5, v1}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 46
    :cond_7
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->m:Landroid/widget/LinearLayout;

    if-eqz v1, :cond_8

    .line 47
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v1

    check-cast v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 48
    iput v4, v1, Landroid/widget/LinearLayout$LayoutParams;->width:I

    .line 49
    invoke-static {p0, v6}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v2

    .line 50
    iput v2, v1, Landroid/widget/LinearLayout$LayoutParams;->height:I

    .line 51
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->m:Landroid/widget/LinearLayout;

    invoke-virtual {v2, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 52
    :cond_8
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->I:Landroid/widget/TextView;

    if-eqz v1, :cond_9

    .line 53
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v1

    check-cast v1, Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v2, 0x42f00000    # 120.0f

    .line 54
    invoke-static {p0, v2}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v2

    const/high16 v6, 0x420c0000    # 35.0f

    .line 55
    invoke-static {p0, v6}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v6

    .line 56
    iput v2, v1, Landroid/widget/LinearLayout$LayoutParams;->width:I

    .line 57
    iput v6, v1, Landroid/widget/LinearLayout$LayoutParams;->height:I

    .line 58
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->I:Landroid/widget/TextView;

    invoke-virtual {v2, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 59
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->I:Landroid/widget/TextView;

    const-string v2, "#4DFFFFFF"

    div-int/lit8 v6, v6, 0x2

    invoke-static {v1, v2, v8, v5, v6}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 60
    :cond_9
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->n:Landroid/widget/LinearLayout;

    if-eqz v1, :cond_a

    .line 61
    invoke-static {v1, v7, v8, v5, v0}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 62
    :cond_a
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->M:Landroid/widget/TextView;

    if-eqz v0, :cond_b

    .line 63
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout$LayoutParams;

    .line 64
    iput v4, v0, Landroid/widget/LinearLayout$LayoutParams;->width:I

    .line 65
    invoke-static {p0, v3}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v1

    .line 66
    iput v1, v0, Landroid/widget/LinearLayout$LayoutParams;->height:I

    .line 67
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->M:Landroid/widget/TextView;

    const-string v2, "#3976FF"

    div-int/lit8 v1, v1, 0x2

    invoke-static {v0, v2, v8, v5, v1}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0

    return-void

    .line 68
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_b
    return-void
.end method

.method private c(I)V
    .locals 1

    .line 69
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->V:Landroid/widget/ProgressBar;

    if-nez v0, :cond_0

    goto :goto_0

    .line 70
    :cond_0
    div-int/lit16 p1, p1, 0x3e8

    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setProgress(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    .line 71
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public static synthetic c(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;I)V
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b(I)V

    return-void
.end method

.method public static synthetic c(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Z)Z
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Y:Z

    return p1
.end method

.method public static synthetic d(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    return p0
.end method

.method public static synthetic d(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;I)I
    .locals 0

    .line 2
    iput p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ae:I

    return p1
.end method

.method private d()V
    .locals 5

    const/4 v0, 0x0

    const/4 v1, 0x1

    const/4 v2, 0x0

    .line 4
    :try_start_0
    iget-object v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    if-nez v3, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    invoke-virtual {v3}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getComplain()Lcom/beizi/fusion/model/AdSpacesBean$ComplainBean;

    move-result-object v3

    if-nez v3, :cond_1

    goto :goto_0

    .line 6
    :cond_1
    invoke-virtual {v3}, Lcom/beizi/fusion/model/AdSpacesBean$ComplainBean;->getOpen()I

    move-result v3

    if-eq v3, v1, :cond_2

    goto :goto_0

    .line 7
    :cond_2
    iget-object v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->O:Landroid/widget/TextView;

    if-nez v3, :cond_3

    goto :goto_0

    .line 8
    :cond_3
    invoke-virtual {v3, v0}, Landroid/view/View;->setVisibility(I)V

    .line 9
    iget-object v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->O:Landroid/widget/TextView;

    new-instance v4, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$12;

    invoke-direct {v4, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$12;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v3, v4}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    const/high16 v3, 0x40a00000    # 5.0f

    .line 10
    invoke-static {p0, v3}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v3

    int-to-float v3, v3

    const/16 v4, 0x8

    .line 11
    new-array v4, v4, [F

    aput v2, v4, v0

    aput v2, v4, v1

    const/4 v0, 0x2

    aput v3, v4, v0

    const/4 v0, 0x3

    aput v3, v4, v0

    const/4 v0, 0x4

    aput v3, v4, v0

    const/4 v0, 0x5

    aput v3, v4, v0

    const/4 v0, 0x6

    aput v2, v4, v0

    const/4 v0, 0x7

    aput v2, v4, v0

    .line 12
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->O:Landroid/widget/TextView;

    const-string v2, "#66000000"

    const-string v3, "#66FFFFFF"

    invoke-static {v0, v2, v1, v3, v4}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;[F)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 13
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method private d(I)V
    .locals 3

    .line 14
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 15
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->m()Lcom/beizi/ad/internal/f/c;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 16
    :cond_1
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v1}, Lcom/beizi/ad/v2/a/b;->e()Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0x19

    if-ne p1, v2, :cond_2

    .line 17
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    invoke-virtual {v0, p1, v1}, Lcom/beizi/ad/internal/f/c;->c(Landroid/view/View;Ljava/lang/String;)V

    return-void

    :cond_2
    const/16 v2, 0x32

    if-ne p1, v2, :cond_3

    .line 18
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    invoke-virtual {v0, p1, v1}, Lcom/beizi/ad/internal/f/c;->d(Landroid/view/View;Ljava/lang/String;)V

    return-void

    :cond_3
    const/16 v2, 0x4b

    if-ne p1, v2, :cond_4

    .line 19
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    invoke-virtual {v0, p1, v1}, Lcom/beizi/ad/internal/f/c;->e(Landroid/view/View;Ljava/lang/String;)V

    :cond_4
    :goto_0
    return-void
.end method

.method public static synthetic d(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Z)Z
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aP:Z

    return p1
.end method

.method public static synthetic e(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    return p0
.end method

.method public static synthetic e(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;I)I
    .locals 0

    .line 2
    iput p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    return p1
.end method

.method private e()V
    .locals 2

    .line 3
    :try_start_0
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->C()V

    .line 4
    new-instance v0, Lcom/beizi/ad/internal/view/a/a$a;

    invoke-direct {v0, p0}, Lcom/beizi/ad/internal/view/a/a$a;-><init>(Landroid/content/Context;)V

    .line 5
    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$18;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$18;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Lcom/beizi/ad/internal/view/a/a$a;->a(Lcom/beizi/ad/internal/view/a/a$b;)Lcom/beizi/ad/internal/view/a/a$a;

    .line 6
    invoke-virtual {v0}, Lcom/beizi/ad/internal/view/a/a$a;->a()Lcom/beizi/ad/internal/view/a/a;

    move-result-object v0

    .line 7
    invoke-virtual {v0}, Landroid/app/Dialog;->show()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 8
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    return-void
.end method

.method public static synthetic f(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aD:I

    return p0
.end method

.method private f()V
    .locals 4

    const/4 v0, 0x1

    .line 3
    :try_start_0
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aQ:Z

    .line 4
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i()V

    .line 5
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    const/16 v1, 0x8

    if-eqz v0, :cond_0

    .line 6
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto/16 :goto_1

    .line 7
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->o:Landroid/view/View;

    if-eqz v0, :cond_1

    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 9
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->g:Landroid/widget/LinearLayout;

    if-eqz v0, :cond_2

    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 11
    :cond_2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->v:Landroid/widget/TextView;

    if-eqz v0, :cond_3

    .line 12
    const-string v2, "\u5df2\u83b7\u5f97\u5956\u52b1"

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 13
    :cond_3
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->w:Landroid/widget/TextView;

    if-eqz v0, :cond_4

    .line 14
    const-string v2, "\u5173\u95ed"

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 15
    :cond_4
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->g:Landroid/widget/LinearLayout;

    if-eqz v0, :cond_5

    .line 16
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 17
    :cond_5
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->V:Landroid/widget/ProgressBar;

    if-eqz v0, :cond_6

    .line 18
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 19
    :cond_6
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d:Landroid/widget/RelativeLayout;

    const/4 v1, 0x0

    if-eqz v0, :cond_7

    .line 20
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 21
    :cond_7
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->D()V

    .line 22
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->s:Landroid/widget/ImageView;

    if-eqz v0, :cond_8

    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aM:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_8

    .line 23
    invoke-static {p0}, Lcom/beizi/ad/internal/h/i;->a(Landroid/content/Context;)Lcom/beizi/ad/internal/h/i;

    move-result-object v0

    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aM:Ljava/lang/String;

    new-instance v3, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$20;

    invoke-direct {v3, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$20;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v2, v3}, Lcom/beizi/ad/internal/h/i;->a(Ljava/lang/String;Lcom/beizi/ad/internal/h/i$a;)V

    .line 24
    :cond_8
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->S:Lcom/beizi/ad/internal/view/CustomRoundImageView;

    const/16 v2, 0x59

    invoke-direct {p0, v0, v2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Lcom/beizi/ad/internal/view/CustomRoundImageView;I)V

    .line 25
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->A:Landroid/widget/TextView;

    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->B:Landroid/widget/TextView;

    invoke-direct {p0, v0, v2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 26
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->av:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_9

    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->C:Landroid/widget/TextView;

    if-eqz v0, :cond_9

    .line 27
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->av:Ljava/lang/String;

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 28
    :cond_9
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->t:Landroid/widget/ImageView;

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/ImageView;)V

    .line 29
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i:Landroid/widget/LinearLayout;

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/view/View;)V

    .line 30
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Q:Landroid/widget/FrameLayout;

    if-eqz v0, :cond_a

    .line 31
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 32
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->l()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 33
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_a
    return-void
.end method

.method public static synthetic f(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;I)V
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(I)V

    return-void
.end method

.method public static synthetic g(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    return p0
.end method

.method public static synthetic g(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;I)I
    .locals 0

    .line 2
    iput p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    return p1
.end method

.method private g()V
    .locals 3

    .line 3
    :try_start_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ab:Z

    if-eqz v0, :cond_5

    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    if-eqz v0, :cond_5

    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->al:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto/16 :goto_4

    .line 4
    :cond_0
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    if-ne v0, v1, :cond_1

    .line 5
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aD:I

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b(I)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_3

    .line 6
    :cond_1
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Z:I

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b(I)V

    .line 7
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    const/4 v1, 0x0

    if-eqz v0, :cond_3

    .line 8
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 9
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Y:Z

    if-eqz v0, :cond_2

    .line 10
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    sget v2, Lcom/sjm/sjmdaly/R$drawable;->voice_on:I

    invoke-virtual {v0, v2}, Landroid/widget/ImageView;->setImageResource(I)V

    goto :goto_1

    .line 11
    :cond_2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    sget v2, Lcom/sjm/sjmdaly/R$drawable;->voice_off:I

    invoke-virtual {v0, v2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 12
    :cond_3
    :goto_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 13
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->X:Z
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const-string v2, "BeiZisAd"

    if-eqz v0, :cond_4

    .line 14
    :try_start_1
    const-string v0, "isVideoCacheSuccess: true"

    invoke-static {v2, v0}, Lcom/beizi/ad/lance/a/m;->a(Ljava/lang/String;Ljava/lang/String;)V

    .line 15
    invoke-direct {p0, v1}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(I)V

    goto :goto_2

    .line 16
    :cond_4
    const-string v0, "isVideoCacheSuccess: false"

    invoke-static {v2, v0}, Lcom/beizi/ad/lance/a/m;->a(Ljava/lang/String;Ljava/lang/String;)V

    .line 17
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->al:Ljava/lang/String;

    invoke-virtual {v0, v1}, Landroid/widget/VideoView;->setVideoPath(Ljava/lang/String;)V

    .line 18
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    invoke-virtual {v0}, Landroid/view/View;->requestFocus()Z

    .line 19
    :goto_2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$21;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$21;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/VideoView;->setOnCompletionListener(Landroid/media/MediaPlayer$OnCompletionListener;)V

    .line 20
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$22;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$22;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/VideoView;->setOnPreparedListener(Landroid/media/MediaPlayer$OnPreparedListener;)V

    .line 21
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$23;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$23;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/VideoView;->setOnErrorListener(Landroid/media/MediaPlayer$OnErrorListener;)V

    .line 22
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$24;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$24;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/VideoView;->setOnInfoListener(Landroid/media/MediaPlayer$OnInfoListener;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    return-void

    .line 23
    :goto_3
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_5
    :goto_4
    return-void
.end method

.method public static synthetic h(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aC:I

    return p0
.end method

.method private h()V
    .locals 4

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    if-eqz v0, :cond_3

    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ab:Z

    if-nez v0, :cond_0

    goto :goto_2

    .line 3
    :cond_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    if-nez v0, :cond_3

    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aP:Z

    if-nez v0, :cond_3

    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aQ:Z

    if-eqz v0, :cond_1

    goto :goto_2

    .line 4
    :cond_1
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ae:I

    if-lez v0, :cond_2

    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ac:Landroid/media/MediaPlayer;

    if-eqz v1, :cond_2

    .line 5
    sget v2, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v3, 0x1a

    if-lt v2, v3, :cond_2

    int-to-long v2, v0

    const/4 v0, 0x3

    .line 6
    invoke-static {v1, v2, v3, v0}, Lcom/beizi/ad/internal/activity/d;->a(Landroid/media/MediaPlayer;JI)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    .line 7
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    invoke-virtual {v0}, Landroid/widget/VideoView;->start()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 8
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_3
    :goto_2
    return-void
.end method

.method private i()V
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    if-nez v0, :cond_0

    return-void

    .line 3
    :cond_0
    invoke-virtual {v0}, Landroid/widget/VideoView;->pause()V

    return-void
.end method

.method public static synthetic i(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aI:Z

    return p0
.end method

.method private j()Ljava/lang/String;
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/v2/e/b;->u()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic j(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->x()V

    return-void
.end method

.method private k()V
    .locals 7

    .line 2
    :try_start_0
    new-instance v0, Landroid/widget/LinearLayout;

    invoke-direct {v0, p0}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 3
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    const/16 v2, 0x11

    const/4 v3, -0x2

    invoke-direct {v1, v3, v3, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 4
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->A()Lcom/beizi/ad/internal/f/c$a;

    move-result-object v1

    .line 5
    invoke-static {p0, v1}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;Lcom/beizi/ad/internal/f/c$a;)Landroid/widget/FrameLayout;

    move-result-object v1

    const/4 v4, 0x0

    .line 6
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 7
    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v6, 0x41880000    # 17.0f

    invoke-direct {v5, v3, v3, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v0, v1, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 8
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->z()Lcom/beizi/ad/internal/f/c$a;

    move-result-object v1

    .line 9
    invoke-static {p0, v1}, Lcom/beizi/ad/internal/h/v;->b(Landroid/content/Context;Lcom/beizi/ad/internal/f/c$a;)Landroid/widget/FrameLayout;

    move-result-object v1

    .line 10
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 11
    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v5, v3, v3, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v0, v1, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 12
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v5

    check-cast v5, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v6, 0x5

    .line 13
    invoke-virtual {v5, v6, v4, v4, v4}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 14
    iput v2, v5, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 15
    invoke-virtual {v1, v5}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 16
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->P:Landroid/widget/FrameLayout;

    if-eqz v1, :cond_0

    .line 17
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    const/16 v2, 0x55

    invoke-direct {v1, v3, v3, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    const/high16 v2, 0x41000000    # 8.0f

    .line 18
    invoke-static {p0, v2}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v2

    .line 19
    invoke-virtual {v1, v4, v4, v2, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 20
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->P:Landroid/widget/FrameLayout;

    invoke-virtual {v2, v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 21
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_0
    return-void
.end method

.method public static synthetic k(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->e()V

    return-void
.end method

.method public static synthetic l(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Lcom/beizi/ad/v2/e/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    return-object p0
.end method

.method private l()V
    .locals 7

    .line 2
    :try_start_0
    new-instance v0, Landroid/widget/LinearLayout;

    invoke-direct {v0, p0}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 3
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    const/16 v2, 0x11

    const/4 v3, -0x2

    invoke-direct {v1, v3, v3, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 4
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->A()Lcom/beizi/ad/internal/f/c$a;

    move-result-object v1

    .line 5
    invoke-static {p0, v1}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;Lcom/beizi/ad/internal/f/c$a;)Landroid/widget/FrameLayout;

    move-result-object v1

    const/4 v4, 0x0

    .line 6
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 7
    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v6, 0x41880000    # 17.0f

    invoke-direct {v5, v3, v3, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v0, v1, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 8
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->z()Lcom/beizi/ad/internal/f/c$a;

    move-result-object v1

    .line 9
    invoke-static {p0, v1}, Lcom/beizi/ad/internal/h/v;->b(Landroid/content/Context;Lcom/beizi/ad/internal/f/c$a;)Landroid/widget/FrameLayout;

    move-result-object v1

    .line 10
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 11
    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v5, v3, v3, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v0, v1, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 12
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v5

    check-cast v5, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v6, 0x5

    .line 13
    invoke-virtual {v5, v6, v4, v4, v4}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 14
    iput v2, v5, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 15
    invoke-virtual {v1, v5}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 16
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Q:Landroid/widget/FrameLayout;

    if-eqz v1, :cond_0

    .line 17
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    const/16 v2, 0x55

    invoke-direct {v1, v3, v3, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    const/high16 v2, 0x41400000    # 12.0f

    .line 18
    invoke-static {p0, v2}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v2

    const/high16 v3, 0x41f00000    # 30.0f

    .line 19
    invoke-static {p0, v3}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v3

    .line 20
    invoke-virtual {v1, v4, v4, v2, v3}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 21
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Q:Landroid/widget/FrameLayout;

    invoke-virtual {v2, v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 22
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_0
    return-void
.end method

.method private m()V
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->w:Landroid/widget/TextView;

    if-eqz v0, :cond_0

    .line 3
    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$2;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$2;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p:Landroid/widget/ImageView;

    if-eqz v0, :cond_1

    .line 5
    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$3;

    invoke-direct {v1, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$3;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_1
    return-void
.end method

.method public static synthetic m(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->r()V

    return-void
.end method

.method private n()V
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aU:Landroid/os/Handler;

    if-eqz v0, :cond_0

    const/4 v1, 0x0

    .line 3
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-eqz v0, :cond_1

    .line 5
    invoke-virtual {v0}, Lcom/beizi/ad/v2/e/b;->w()V

    .line 6
    :cond_1
    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method public static synthetic n(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h()V

    return-void
.end method

.method public static synthetic o(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/widget/ImageView;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->s:Landroid/widget/ImageView;

    return-object p0
.end method

.method private o()V
    .locals 3

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-nez v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->m()Lcom/beizi/ad/internal/f/c;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v1}, Lcom/beizi/ad/v2/a/b;->e()Ljava/lang/String;

    move-result-object v1

    .line 5
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    invoke-virtual {v0, v2, v1}, Lcom/beizi/ad/internal/f/c;->a(Landroid/view/View;Ljava/lang/String;)V

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    invoke-virtual {v0}, Lcom/beizi/ad/v2/e/b;->v()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 7
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public static synthetic p(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/widget/ProgressBar;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->V:Landroid/widget/ProgressBar;

    return-object p0
.end method

.method private p()V
    .locals 3

    .line 2
    :try_start_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aL:Z

    if-eqz v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    invoke-virtual {v0}, Lcom/beizi/ad/v2/a/b;->m()Lcom/beizi/ad/internal/f/c;

    move-result-object v0

    if-nez v0, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x1

    .line 5
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aL:Z

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a:Lcom/beizi/ad/v2/e/b;

    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lcom/beizi/ad/v2/e/b;->a(Ljava/lang/String;I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 7
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method private q()V
    .locals 8

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    if-nez v0, :cond_0

    goto/16 :goto_1

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->I()I

    move-result v0

    const/4 v1, 0x2

    if-eq v0, v1, :cond_1

    const/4 v1, 0x5

    if-ne v0, v1, :cond_3

    .line 4
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->K:Landroid/widget/TextView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 5
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->B()Ljava/lang/String;

    move-result-object v3

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v0}, Lcom/beizi/ad/internal/f/c;->D()Ljava/lang/String;

    move-result-object v0

    .line 7
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v1}, Lcom/beizi/ad/internal/f/c;->C()Ljava/lang/String;

    move-result-object v1

    .line 8
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->F()Ljava/lang/String;

    move-result-object v2

    .line 9
    iget-object v4, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v4}, Lcom/beizi/ad/internal/f/c;->E()Ljava/lang/String;

    move-result-object v4

    .line 10
    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v5

    if-nez v5, :cond_2

    move-object v5, v2

    goto :goto_0

    :cond_2
    move-object v5, v4

    .line 11
    :goto_0
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->G()Ljava/lang/String;

    move-result-object v4

    .line 12
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b:Lcom/beizi/ad/internal/f/c;

    invoke-virtual {v2}, Lcom/beizi/ad/internal/f/c;->H()Ljava/lang/String;

    move-result-object v6

    .line 13
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "\u5e94\u7528\u540d\u79f0\uff1a"

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v7, " | \u5f00\u53d1\u8005\uff1a"

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, " | \u5e94\u7528\u7248\u672c\uff1a"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, " | <u>\u6743\u9650\u8be6\u60c5</u> | <u>\u9690\u79c1\u534f\u8bae</u> | <u>\u529f\u80fd\u4ecb\u7ecd</u>"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 14
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->K:Landroid/widget/TextView;

    invoke-static {v0}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v0

    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 15
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->K:Landroid/widget/TextView;

    new-instance v1, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$4;

    move-object v2, p0

    invoke-direct/range {v1 .. v6}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$4;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 16
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_3
    :goto_1
    return-void
.end method

.method public static synthetic q(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->p()V

    return-void
.end method

.method private r()V
    .locals 1

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ap:Lcom/beizi/ad/a/a/e;

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {v0}, Lcom/beizi/ad/a/a/e;->a()V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    .line 4
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ar:Lcom/beizi/ad/a/a/a;

    if-eqz v0, :cond_1

    .line 5
    invoke-virtual {v0}, Lcom/beizi/ad/a/a/a;->a()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 6
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_1
    return-void
.end method

.method public static synthetic r(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->f()V

    return-void
.end method

.method private s()V
    .locals 2

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    if-nez v0, :cond_0

    goto :goto_2

    .line 3
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h:Landroid/widget/LinearLayout;

    if-eqz v0, :cond_1

    const/4 v1, 0x0

    .line 4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    .line 5
    :cond_1
    :goto_0
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->w()V

    .line 6
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->t()V

    .line 7
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->u()V

    .line 8
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->v()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 9
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_2
    return-void
.end method

.method public static synthetic s(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->F()V

    return-void
.end method

.method private t()V
    .locals 6

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    if-nez v0, :cond_0

    goto/16 :goto_2

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getRegionalClickView()Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 4
    const-string v1, "#3976FF"

    if-nez v0, :cond_1

    .line 5
    :try_start_1
    new-instance v0, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;

    invoke-direct {v0}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;-><init>()V

    const-wide/high16 v2, 0x3ff0000000000000L    # 1.0

    .line 6
    invoke-virtual {v0, v2, v3}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;->setBackgroundAlpha(D)V

    .line 7
    invoke-virtual {v0, v1}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;->setBackgroundColor(Ljava/lang/String;)V

    .line 8
    const-string v2, "\u70b9\u51fb\u8df3\u8f6c\u7f51\u9875\u6216\u7b2c\u4e09\u65b9\u5e94\u7528"

    invoke-virtual {v0, v2}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;->setTitle(Ljava/lang/String;)V

    .line 9
    const-string v2, "#FFFFFF"

    invoke-virtual {v0, v2}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;->setTitleColor(Ljava/lang/String;)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    .line 10
    :cond_1
    :goto_0
    invoke-virtual {v0}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;->getTitle()Ljava/lang/String;

    move-result-object v2

    iput-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->av:Ljava/lang/String;

    .line 11
    new-instance v2, Lcom/beizi/ad/a/a/c;

    iget-object v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->am:Ljava/lang/String;

    iget-boolean v4, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->au:Z

    invoke-direct {v2, p0, v0, v3, v4}, Lcom/beizi/ad/a/a/c;-><init>(Landroid/content/Context;Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$RegionalClickViewBean;Ljava/lang/String;Z)V

    iput-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ao:Lcom/beizi/ad/a/a/c;

    .line 12
    invoke-virtual {v2}, Lcom/beizi/ad/a/a/c;->a()Ljava/lang/String;

    move-result-object v0

    .line 13
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_2

    move-object v0, v1

    .line 14
    :cond_2
    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h:Landroid/widget/LinearLayout;

    const/4 v3, 0x0

    const/4 v4, 0x0

    if-eqz v2, :cond_3

    const/high16 v5, 0x40a00000    # 5.0f

    .line 15
    invoke-static {p0, v5}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v5

    .line 16
    invoke-static {v2, v0, v4, v3, v5}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 17
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ao:Lcom/beizi/ad/a/a/c;

    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h:Landroid/widget/LinearLayout;

    iget-object v5, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->z:Landroid/widget/TextView;

    invoke-virtual {v0, v2, v5}, Lcom/beizi/ad/a/a/c;->a(Landroid/widget/LinearLayout;Landroid/widget/TextView;)V

    .line 18
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ao:Lcom/beizi/ad/a/a/c;

    new-instance v2, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$5;

    invoke-direct {v2, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$5;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v2}, Lcom/beizi/ad/a/a/c;->a(Lcom/beizi/ad/a/a/c$a;)V

    .line 19
    :cond_3
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i:Landroid/widget/LinearLayout;

    const/high16 v2, 0x41c80000    # 25.0f

    if-eqz v0, :cond_4

    .line 20
    invoke-static {p0, v2}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v5

    .line 21
    invoke-static {v0, v1, v4, v3, v5}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V

    .line 22
    :cond_4
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->m:Landroid/widget/LinearLayout;

    if-eqz v0, :cond_5

    .line 23
    const-string v1, "#2A8BEF"

    .line 24
    invoke-static {p0, v2}, Lcom/beizi/ad/internal/h/v;->a(Landroid/content/Context;F)I

    move-result v2

    .line 25
    invoke-static {v0, v1, v4, v3, v2}, Lcom/beizi/ad/internal/h/o;->a(Landroid/view/View;Ljava/lang/String;ILjava/lang/String;I)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    return-void

    .line 26
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_5
    :goto_2
    return-void
.end method

.method public static synthetic t(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->Y:Z

    return p0
.end method

.method public static synthetic u(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Landroid/media/MediaPlayer;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ac:Landroid/media/MediaPlayer;

    return-object p0
.end method

.method private u()V
    .locals 4

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    if-nez v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    invoke-static {}, Lcom/beizi/fusion/c/b;->a()Lcom/beizi/fusion/c/b;

    move-result-object v0

    invoke-virtual {v0}, Lcom/beizi/fusion/c/b;->n()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    invoke-virtual {v0}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getShakeView()Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$ShakeViewBean;

    move-result-object v0

    if-nez v0, :cond_2

    goto :goto_0

    .line 5
    :cond_2
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    invoke-virtual {v1}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getSpaceId()Ljava/lang/String;

    move-result-object v1

    .line 6
    new-instance v2, Lcom/beizi/ad/a/a/e;

    iget-object v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->am:Ljava/lang/String;

    invoke-direct {v2, p0, v0, v1, v3}, Lcom/beizi/ad/a/a/e;-><init>(Landroid/content/Context;Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$ShakeViewBean;Ljava/lang/String;Ljava/lang/String;)V

    iput-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ap:Lcom/beizi/ad/a/a/e;

    .line 7
    new-instance v0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$6;

    invoke-direct {v0, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$6;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v2, v0}, Lcom/beizi/ad/a/a/e;->a(Lcom/beizi/ad/a/a/e$a;)V

    .line 8
    const-string v0, "shake"

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ax:Ljava/lang/String;

    .line 9
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ap:Lcom/beizi/ad/a/a/e;

    invoke-virtual {v0}, Lcom/beizi/ad/a/a/e;->c()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aw:Ljava/lang/String;

    .line 10
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->r:Landroid/widget/ImageView;

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/ImageView;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 11
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method private v()V
    .locals 4

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    if-nez v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    invoke-static {}, Lcom/beizi/fusion/c/b;->a()Lcom/beizi/fusion/c/b;

    move-result-object v0

    invoke-virtual {v0}, Lcom/beizi/fusion/c/b;->n()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    .line 5
    invoke-virtual {v0}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getEulerAngleRule()Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$EulerAngleViewBean;

    move-result-object v0

    if-nez v0, :cond_2

    goto :goto_0

    .line 6
    :cond_2
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    invoke-virtual {v1}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getSpaceId()Ljava/lang/String;

    move-result-object v1

    .line 7
    new-instance v2, Lcom/beizi/ad/a/a/a;

    iget-object v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->am:Ljava/lang/String;

    invoke-direct {v2, p0, v0, v1, v3}, Lcom/beizi/ad/a/a/a;-><init>(Landroid/content/Context;Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$EulerAngleViewBean;Ljava/lang/String;Ljava/lang/String;)V

    iput-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ar:Lcom/beizi/ad/a/a/a;

    .line 8
    new-instance v0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$7;

    invoke-direct {v0, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$7;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v2, v0}, Lcom/beizi/ad/a/a/a;->a(Lcom/beizi/ad/a/a/a$a;)V

    .line 9
    const-string v0, "eulerAngle"

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ax:Ljava/lang/String;

    .line 10
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ar:Lcom/beizi/ad/a/a/a;

    invoke-virtual {v0}, Lcom/beizi/ad/a/a/a;->c()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aw:Ljava/lang/String;

    .line 11
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->r:Landroid/widget/ImageView;

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/ImageView;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public static synthetic v(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->at:Z

    return p0
.end method

.method private w()V
    .locals 3

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->an:Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;

    if-nez v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean;->getFullScreenClick()Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$FullScreenClickBean;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    new-instance v1, Lcom/beizi/ad/a/a/b;

    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->am:Ljava/lang/String;

    invoke-direct {v1, p0, v0, v2}, Lcom/beizi/ad/a/a/b;-><init>(Landroid/content/Context;Lcom/beizi/fusion/model/AdSpacesBean$BuyerBean$FullScreenClickBean;Ljava/lang/String;)V

    iput-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->as:Lcom/beizi/ad/a/a/b;

    .line 5
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c:Landroid/widget/RelativeLayout;

    new-instance v2, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$8;

    invoke-direct {v2, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$8;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v1, v0, v2}, Lcom/beizi/ad/a/a/b;->a(Landroid/view/View;Lcom/beizi/ad/a/a/b$a;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 6
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public static synthetic w(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->k()V

    return-void
.end method

.method private x()V
    .locals 4

    .line 2
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->j:Landroid/widget/LinearLayout;

    if-nez v0, :cond_0

    goto/16 :goto_3

    :cond_0
    const/4 v1, 0x1

    .line 3
    iput-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aI:Z

    .line 4
    iput-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    const/4 v1, 0x0

    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 6
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->j:Landroid/widget/LinearLayout;

    new-instance v2, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$9;

    invoke-direct {v2, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$9;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 7
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->E:Landroid/widget/TextView;

    if-eqz v0, :cond_1

    .line 8
    const-string v2, "\u6253\u5f00\u5e76\u4f53\u9a8c\u5185\u5bb9"

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto/16 :goto_2

    .line 9
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->D:Landroid/widget/TextView;

    if-eqz v0, :cond_2

    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    if-lez v2, :cond_2

    .line 10
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    iget v3, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aF:I

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, "\u79d2"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 11
    :cond_2
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->i()V

    .line 12
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->l:Landroid/widget/LinearLayout;

    if-eqz v0, :cond_4

    .line 13
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ai:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aj:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ak:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 14
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->l:Landroid/widget/LinearLayout;

    const/16 v2, 0x8

    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    goto :goto_1

    .line 15
    :cond_3
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->l:Landroid/widget/LinearLayout;

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 16
    :cond_4
    :goto_1
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->T:Lcom/beizi/ad/internal/view/CustomRoundImageView;

    const/16 v2, 0x2f

    invoke-direct {p0, v0, v2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Lcom/beizi/ad/internal/view/CustomRoundImageView;I)V

    .line 17
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->F:Landroid/widget/TextView;

    iget-object v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->G:Landroid/widget/TextView;

    invoke-direct {p0, v0, v2}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 18
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->u:Landroid/widget/ImageView;

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/ImageView;)V

    .line 19
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->m:Landroid/widget/LinearLayout;

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/view/View;)V

    .line 20
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->I:Landroid/widget/TextView;

    if-eqz v0, :cond_5

    .line 21
    new-instance v2, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$10;

    invoke-direct {v2, p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity$10;-><init>(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V

    invoke-virtual {v0, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 22
    :cond_5
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    if-ne v0, v2, :cond_6

    .line 23
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->H:Landroid/widget/TextView;

    if-eqz v0, :cond_7

    .line 24
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 25
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->H:Landroid/widget/TextView;

    const-string v1, "\u6d4f\u89c8\u65f6\u95f4\u4e0d\u8db3\uff0c\u65e0\u6cd5\u63d0\u524d\u83b7\u53d6\u5956\u52b1"

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void

    .line 26
    :cond_6
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->H:Landroid/widget/TextView;

    if-eqz v0, :cond_7

    .line 27
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 28
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->H:Landroid/widget/TextView;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aH:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, "\u79d2\u540e\u7ee7\u7eed\u64ad\u653e\u89c6\u9891"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 29
    :goto_2
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_7
    :goto_3
    return-void
.end method

.method public static synthetic x(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->s()V

    return-void
.end method

.method private y()V
    .locals 3

    .line 2
    :try_start_0
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aC:I

    if-eq v0, v1, :cond_0

    goto :goto_2

    .line 3
    :cond_0
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    if-eqz v0, :cond_5

    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->az:Z

    if-eqz v0, :cond_5

    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aP:Z

    if-nez v0, :cond_5

    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aH:I

    const/16 v1, -0x3e7

    if-ne v0, v1, :cond_1

    goto :goto_2

    :cond_1
    if-lez v0, :cond_3

    .line 4
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->H:Landroid/widget/TextView;

    if-eqz v0, :cond_2

    .line 5
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aH:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, "\u79d2\u540e\u7ee7\u7eed\u64ad\u653e\u89c6\u9891"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    .line 6
    :cond_2
    :goto_0
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aH:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aH:I

    return-void

    :cond_3
    if-nez v0, :cond_5

    const/4 v0, 0x0

    .line 7
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    .line 8
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->z()V

    .line 9
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->az:Z

    if-eqz v0, :cond_4

    .line 10
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->h()V

    .line 11
    :cond_4
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->r:Landroid/widget/ImageView;

    invoke-direct {p0, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/ImageView;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 12
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_5
    :goto_2
    return-void
.end method

.method public static synthetic y(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->o()V

    return-void
.end method

.method private z()V
    .locals 2

    const/4 v0, 0x0

    .line 2
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    .line 3
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->j:Landroid/widget/LinearLayout;

    if-eqz v0, :cond_0

    const/16 v1, 0x8

    .line 4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    :cond_0
    return-void
.end method

.method public static synthetic z(Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->E()V

    return-void
.end method


# virtual methods
.method public onCreate(Landroid/os/Bundle;)V
    .locals 1
    .param p1    # Landroid/os/Bundle;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    :try_start_0
    sget p1, Lcom/sjm/sjmdaly/R$layout;->activity_beizi_reward_video:I

    .line 5
    .line 6
    invoke-virtual {p0, p1}, Landroid/app/Activity;->setContentView(I)V

    .line 7
    .line 8
    .line 9
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a()V

    .line 10
    .line 11
    .line 12
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->b()V

    .line 13
    .line 14
    .line 15
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->c()V

    .line 16
    .line 17
    .line 18
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->d()V

    .line 19
    .line 20
    .line 21
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->R:Lcom/beizi/ad/internal/view/CustomRoundImageView;

    .line 22
    .line 23
    const/16 v0, 0x37

    .line 24
    .line 25
    invoke-direct {p0, p1, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Lcom/beizi/ad/internal/view/CustomRoundImageView;I)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->x:Landroid/widget/TextView;

    .line 29
    .line 30
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->y:Landroid/widget/TextView;

    .line 31
    .line 32
    invoke-direct {p0, p1, v0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->a(Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 33
    .line 34
    .line 35
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->g()V

    .line 36
    .line 37
    .line 38
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->m()V

    .line 39
    .line 40
    .line 41
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->q()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 42
    .line 43
    .line 44
    return-void

    .line 45
    :catch_0
    move-exception p1

    .line 46
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method public onDestroy()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroid/app/Activity;->onDestroy()V

    .line 2
    .line 3
    .line 4
    :try_start_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ao:Lcom/beizi/ad/a/a/c;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/beizi/ad/a/a/c;->b()V

    .line 9
    .line 10
    .line 11
    goto :goto_0

    .line 12
    :catch_0
    move-exception v0

    .line 13
    goto :goto_1

    .line 14
    :cond_0
    :goto_0
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ao:Lcom/beizi/ad/a/a/c;

    .line 16
    .line 17
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ap:Lcom/beizi/ad/a/a/e;

    .line 18
    .line 19
    if-eqz v1, :cond_1

    .line 20
    .line 21
    invoke-virtual {v1}, Lcom/beizi/ad/a/a/e;->d()V

    .line 22
    .line 23
    .line 24
    :cond_1
    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ap:Lcom/beizi/ad/a/a/e;

    .line 25
    .line 26
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ar:Lcom/beizi/ad/a/a/a;

    .line 27
    .line 28
    if-eqz v1, :cond_2

    .line 29
    .line 30
    invoke-virtual {v1}, Lcom/beizi/ad/a/a/a;->d()V

    .line 31
    .line 32
    .line 33
    :cond_2
    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ar:Lcom/beizi/ad/a/a/a;

    .line 34
    .line 35
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aq:Lcom/beizi/ad/a/a/d;

    .line 36
    .line 37
    if-eqz v1, :cond_3

    .line 38
    .line 39
    invoke-virtual {v1}, Lcom/beizi/ad/a/a/d;->a()V

    .line 40
    .line 41
    .line 42
    :cond_3
    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aq:Lcom/beizi/ad/a/a/d;

    .line 43
    .line 44
    iget-object v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->as:Lcom/beizi/ad/a/a/b;

    .line 45
    .line 46
    if-eqz v1, :cond_4

    .line 47
    .line 48
    invoke-virtual {v1}, Lcom/beizi/ad/a/a/b;->b()V

    .line 49
    .line 50
    .line 51
    :cond_4
    iput-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->as:Lcom/beizi/ad/a/a/b;

    .line 52
    .line 53
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->D()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 54
    .line 55
    .line 56
    return-void

    .line 57
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 58
    .line 59
    .line 60
    return-void
.end method

.method public onKeyDown(ILandroid/view/KeyEvent;)Z
    .locals 1

    .line 1
    const/4 v0, 0x4

    .line 2
    if-eq p1, v0, :cond_1

    .line 3
    .line 4
    const/4 v0, 0x3

    .line 5
    if-ne p1, v0, :cond_0

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    invoke-super {p0, p1, p2}, Landroid/app/Activity;->onKeyDown(ILandroid/view/KeyEvent;)Z

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    return p1

    .line 13
    :cond_1
    :goto_0
    const/4 p1, 0x1

    .line 14
    return p1
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroid/app/Activity;->onPause()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->az:Z

    .line 6
    .line 7
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->C()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public onResume()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroid/app/Activity;->onResume()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x1

    .line 5
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->az:Z

    .line 6
    .line 7
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->r()V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0}, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->A()V

    .line 11
    .line 12
    .line 13
    const-wide/16 v0, 0x0

    .line 14
    .line 15
    iput-wide v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    .line 16
    .line 17
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aL:Z

    .line 18
    .line 19
    if-eqz v0, :cond_0

    .line 20
    .line 21
    return-void

    .line 22
    :cond_0
    iget-object v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->U:Landroid/widget/VideoView;

    .line 23
    .line 24
    if-eqz v0, :cond_1

    .line 25
    .line 26
    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ab:Z

    .line 27
    .line 28
    if-eqz v1, :cond_1

    .line 29
    .line 30
    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aO:Z

    .line 31
    .line 32
    if-nez v1, :cond_1

    .line 33
    .line 34
    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aP:Z

    .line 35
    .line 36
    if-nez v1, :cond_1

    .line 37
    .line 38
    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    .line 39
    .line 40
    if-nez v1, :cond_1

    .line 41
    .line 42
    iget-boolean v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aQ:Z

    .line 43
    .line 44
    if-nez v1, :cond_1

    .line 45
    .line 46
    invoke-virtual {v0}, Landroid/widget/VideoView;->resume()V

    .line 47
    .line 48
    .line 49
    :cond_1
    const/4 v0, 0x0

    .line 50
    iput-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ah:Z

    .line 51
    .line 52
    return-void
.end method

.method public onStop()V
    .locals 4

    .line 1
    invoke-super {p0}, Landroid/app/Activity;->onStop()V

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aA:I

    .line 5
    .line 6
    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aB:I

    .line 7
    .line 8
    const-wide/16 v2, 0x0

    .line 9
    .line 10
    if-ne v0, v1, :cond_2

    .line 11
    .line 12
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aK:Z

    .line 13
    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ad:Z

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    iget v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aE:I

    .line 21
    .line 22
    if-ltz v0, :cond_0

    .line 23
    .line 24
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 25
    .line 26
    .line 27
    move-result-wide v0

    .line 28
    iput-wide v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    .line 29
    .line 30
    :cond_0
    return-void

    .line 31
    :cond_1
    iput-wide v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    .line 32
    .line 33
    return-void

    .line 34
    :cond_2
    iget v1, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aC:I

    .line 35
    .line 36
    if-ne v0, v1, :cond_4

    .line 37
    .line 38
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->aJ:Z

    .line 39
    .line 40
    if-eqz v0, :cond_3

    .line 41
    .line 42
    iget-boolean v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ad:Z

    .line 43
    .line 44
    if-eqz v0, :cond_3

    .line 45
    .line 46
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 47
    .line 48
    .line 49
    move-result-wide v0

    .line 50
    iput-wide v0, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    .line 51
    .line 52
    return-void

    .line 53
    :cond_3
    iput-wide v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    .line 54
    .line 55
    return-void

    .line 56
    :cond_4
    iput-wide v2, p0, Lcom/beizi/ad/v2/activity/BeiZiNewRewardVideoActivity;->ay:J

    .line 57
    .line 58
    return-void
.end method
