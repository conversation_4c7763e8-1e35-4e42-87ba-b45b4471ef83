.class public final Ltop/cycdm/cycapp/ui/home/<USER>
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function3;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltop/cycdm/cycapp/ui/home/<USER>
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# static fields
.field public static final a:Ltop/cycdm/cycapp/ui/home/<USER>


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ltop/cycdm/cycapp/ui/home/<USER>

    invoke-direct {v0}, Ltop/cycdm/cycapp/ui/home/<USER>

    sput-object v0, Ltop/cycdm/cycapp/ui/home/<USER>/cycdm/cycapp/ui/home/<USER>

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/compose/foundation/layout/RowScope;Landroidx/compose/runtime/Composer;I)V
    .locals 27

    .line 1
    move-object/from16 v5, p2

    .line 2
    .line 3
    move/from16 v0, p3

    .line 4
    .line 5
    and-int/lit8 v1, v0, 0x11

    .line 6
    .line 7
    const/16 v2, 0x10

    .line 8
    .line 9
    if-ne v1, v2, :cond_1

    .line 10
    .line 11
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->getSkipping()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-nez v1, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-interface {v5}, Landroidx/compose/runtime/Composer;->skipToGroupEnd()V

    .line 19
    .line 20
    .line 21
    return-void

    .line 22
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    if-eqz v1, :cond_2

    .line 27
    .line 28
    const/4 v1, -0x1

    .line 29
    const-string v2, "top.cycdm.cycapp.ui.home.ComposableSingletons$PreferKt.lambda$-758725454.<anonymous> (Prefer.kt:168)"

    .line 30
    .line 31
    const v3, -0x2d393b4e

    .line 32
    .line 33
    .line 34
    invoke-static {v3, v0, v1, v2}, Landroidx/compose/runtime/ComposerKt;->traceEventStart(IIILjava/lang/String;)V

    .line 35
    .line 36
    .line 37
    :cond_2
    const/4 v0, 0x0

    .line 38
    invoke-static {v5, v0}, Lw7/f;->i(Landroidx/compose/runtime/Composer;I)Lw7/a;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v1}, Lw7/a;->l()J

    .line 43
    .line 44
    .line 45
    move-result-wide v2

    .line 46
    const/16 v1, 0xe

    .line 47
    .line 48
    invoke-static {v1}, Landroidx/compose/ui/unit/TextUnitKt;->getSp(I)J

    .line 49
    .line 50
    .line 51
    move-result-wide v6

    .line 52
    const/16 v23, 0x0

    .line 53
    .line 54
    const v24, 0x1fff2

    .line 55
    .line 56
    .line 57
    move v1, v0

    .line 58
    const-string v0, "\u66f4\u591a\u699c\u5355"

    .line 59
    .line 60
    move v4, v1

    .line 61
    const/4 v1, 0x0

    .line 62
    move-wide/from16 v25, v6

    .line 63
    .line 64
    move v7, v4

    .line 65
    move-wide/from16 v4, v25

    .line 66
    .line 67
    const/4 v6, 0x0

    .line 68
    move v8, v7

    .line 69
    const/4 v7, 0x0

    .line 70
    move v9, v8

    .line 71
    const/4 v8, 0x0

    .line 72
    move v11, v9

    .line 73
    const-wide/16 v9, 0x0

    .line 74
    .line 75
    move v12, v11

    .line 76
    const/4 v11, 0x0

    .line 77
    move v13, v12

    .line 78
    const/4 v12, 0x0

    .line 79
    move v15, v13

    .line 80
    const-wide/16 v13, 0x0

    .line 81
    .line 82
    move/from16 v16, v15

    .line 83
    .line 84
    const/4 v15, 0x0

    .line 85
    move/from16 v17, v16

    .line 86
    .line 87
    const/16 v16, 0x0

    .line 88
    .line 89
    move/from16 v18, v17

    .line 90
    .line 91
    const/16 v17, 0x0

    .line 92
    .line 93
    move/from16 v19, v18

    .line 94
    .line 95
    const/16 v18, 0x0

    .line 96
    .line 97
    move/from16 v20, v19

    .line 98
    .line 99
    const/16 v19, 0x0

    .line 100
    .line 101
    move/from16 v21, v20

    .line 102
    .line 103
    const/16 v20, 0x0

    .line 104
    .line 105
    const/16 v22, 0xc06

    .line 106
    .line 107
    move-object/from16 v21, p2

    .line 108
    .line 109
    invoke-static/range {v0 .. v24}, Landroidx/compose/material3/TextKt;->Text--4IGK_g(Ljava/lang/String;Landroidx/compose/ui/Modifier;JJLandroidx/compose/ui/text/font/FontStyle;Landroidx/compose/ui/text/font/FontWeight;Landroidx/compose/ui/text/font/FontFamily;JLandroidx/compose/ui/text/style/TextDecoration;Landroidx/compose/ui/text/style/TextAlign;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/TextStyle;Landroidx/compose/runtime/Composer;III)V

    .line 110
    .line 111
    .line 112
    move-object/from16 v5, v21

    .line 113
    .line 114
    sget v0, Ltop/cycdm/cycapp/R$drawable;->ic_info_more:I

    .line 115
    .line 116
    const/4 v1, 0x0

    .line 117
    invoke-static {v0, v5, v1}, Landroidx/compose/ui/res/PainterResources_androidKt;->painterResource(ILandroidx/compose/runtime/Composer;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 118
    .line 119
    .line 120
    move-result-object v0

    .line 121
    const/16 v6, 0x30

    .line 122
    .line 123
    const/16 v7, 0xc

    .line 124
    .line 125
    const/4 v1, 0x0

    .line 126
    const/4 v2, 0x0

    .line 127
    const-wide/16 v3, 0x0

    .line 128
    .line 129
    invoke-static/range {v0 .. v7}, Landroidx/compose/material3/IconKt;->Icon-ww6aTOc(Landroidx/compose/ui/graphics/painter/Painter;Ljava/lang/String;Landroidx/compose/ui/Modifier;JLandroidx/compose/runtime/Composer;II)V

    .line 130
    .line 131
    .line 132
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->isTraceInProgress()Z

    .line 133
    .line 134
    .line 135
    move-result v0

    .line 136
    if-eqz v0, :cond_3

    .line 137
    .line 138
    invoke-static {}, Landroidx/compose/runtime/ComposerKt;->traceEventEnd()V

    .line 139
    .line 140
    .line 141
    :cond_3
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/layout/RowScope;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/Composer;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, Ltop/cycdm/cycapp/ui/home/<USER>/compose/foundation/layout/RowScope;Landroidx/compose/runtime/Composer;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 15
    .line 16
    return-object p1
.end method
