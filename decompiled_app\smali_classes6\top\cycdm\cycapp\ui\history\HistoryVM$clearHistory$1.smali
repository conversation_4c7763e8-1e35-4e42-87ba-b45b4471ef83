.class final Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ltop/cycdm/cycapp/ui/history/HistoryVM;->clearHistory()Lkotlinx/coroutines/w1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Lkotlin/coroutines/e;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lorg/orbitmvi/orbit/syntax/simple/b;",
        "Ltop/cycdm/cycapp/ui/history/w;",
        "Ltop/cycdm/cycapp/ui/history/a;",
        "Lkotlin/t;",
        "<anonymous>",
        "(Lorg/orbitmvi/orbit/syntax/simple/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "top.cycdm.cycapp.ui.history.HistoryVM$clearHistory$1"
    f = "HistoryVM.kt"
    i = {
        0x0
    }
    l = {
        0x2d,
        0x2f,
        0x37
    }
    m = "invokeSuspend"
    n = {
        "$this$intent"
    }
    s = {
        "L$0"
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;


# direct methods
.method public constructor <init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltop/cycdm/cycapp/ui/history/HistoryVM;",
            "Lkotlin/coroutines/e;",
            ")V"
        }
    .end annotation

    iput-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e;",
            ")",
            "Lkotlin/coroutines/e;"
        }
    .end annotation

    new-instance v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;

    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    invoke-direct {v0, v1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;-><init>(Ltop/cycdm/cycapp/ui/history/HistoryVM;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/orbitmvi/orbit/syntax/simple/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/orbitmvi/orbit/syntax/simple/b;",
            "Lkotlin/coroutines/e;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;

    sget-object p2, Lkotlin/t;->a:Lkotlin/t;

    invoke-virtual {p1, p2}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    if-eq v1, v4, :cond_2

    .line 13
    .line 14
    if-eq v1, v3, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    goto :goto_1

    .line 22
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 23
    .line 24
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 25
    .line 26
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    goto/16 :goto_4

    .line 34
    .line 35
    :cond_2
    iget-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->L$0:Ljava/lang/Object;

    .line 36
    .line 37
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 38
    .line 39
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_3
    invoke-static {p1}, Lkotlin/i;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->L$0:Ljava/lang/Object;

    .line 47
    .line 48
    move-object v1, p1

    .line 49
    check-cast v1, Lorg/orbitmvi/orbit/syntax/simple/b;

    .line 50
    .line 51
    iget-object p1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 52
    .line 53
    invoke-static {p1}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->access$getUserDataRep$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lg8/g;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    invoke-interface {p1}, Lg8/g;->c()Lkotlinx/coroutines/flow/d;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iput-object v1, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->L$0:Ljava/lang/Object;

    .line 62
    .line 63
    iput v4, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->label:I

    .line 64
    .line 65
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/f;->B(Lkotlinx/coroutines/flow/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    if-ne p1, v0, :cond_4

    .line 70
    .line 71
    goto :goto_3

    .line 72
    :cond_4
    :goto_0
    check-cast p1, Ltop/cycdm/model/w;

    .line 73
    .line 74
    invoke-virtual {p1}, Ltop/cycdm/model/w;->a()I

    .line 75
    .line 76
    .line 77
    move-result v4

    .line 78
    invoke-virtual {p1}, Ltop/cycdm/model/w;->b()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    const-string v5, ""

    .line 83
    .line 84
    invoke-static {p1, v5}, Lkotlin/jvm/internal/u;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 85
    .line 86
    .line 87
    move-result v5

    .line 88
    const/4 v6, 0x0

    .line 89
    if-nez v5, :cond_7

    .line 90
    .line 91
    const/4 v5, -0x1

    .line 92
    if-ne v4, v5, :cond_5

    .line 93
    .line 94
    goto :goto_2

    .line 95
    :cond_5
    iget-object v3, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->this$0:Ltop/cycdm/cycapp/ui/history/HistoryVM;

    .line 96
    .line 97
    invoke-static {v3}, Ltop/cycdm/cycapp/ui/history/HistoryVM;->access$getUserRep$p(Ltop/cycdm/cycapp/ui/history/HistoryVM;)Lg8/h;

    .line 98
    .line 99
    .line 100
    move-result-object v3

    .line 101
    invoke-interface {v3, p1, v4}, Lg8/h;->c(Ljava/lang/String;I)Lkotlinx/coroutines/flow/d;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    new-instance v3, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$1;

    .line 106
    .line 107
    invoke-direct {v3, v1, v6}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$1;-><init>(Lorg/orbitmvi/orbit/syntax/simple/b;Lkotlin/coroutines/e;)V

    .line 108
    .line 109
    .line 110
    invoke-static {p1, v3}, Lkotlinx/coroutines/flow/f;->i(Lkotlinx/coroutines/flow/d;Lkotlin/jvm/functions/Function3;)Lkotlinx/coroutines/flow/d;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    new-instance v3, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;

    .line 115
    .line 116
    invoke-direct {v3, v1}, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1$2;-><init>(Lorg/orbitmvi/orbit/syntax/simple/b;)V

    .line 117
    .line 118
    .line 119
    iput-object v6, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->L$0:Ljava/lang/Object;

    .line 120
    .line 121
    iput v2, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->label:I

    .line 122
    .line 123
    invoke-interface {p1, v3, p0}, Lkotlinx/coroutines/flow/d;->collect(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    if-ne p1, v0, :cond_6

    .line 128
    .line 129
    goto :goto_3

    .line 130
    :cond_6
    :goto_1
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 131
    .line 132
    return-object p1

    .line 133
    :cond_7
    :goto_2
    new-instance p1, Ltop/cycdm/cycapp/ui/history/a$d;

    .line 134
    .line 135
    const-string v2, "\u8bf7\u5148\u767b\u5f55"

    .line 136
    .line 137
    invoke-direct {p1, v2}, Ltop/cycdm/cycapp/ui/history/a$d;-><init>(Ljava/lang/String;)V

    .line 138
    .line 139
    .line 140
    iput-object v6, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->L$0:Ljava/lang/Object;

    .line 141
    .line 142
    iput v3, p0, Ltop/cycdm/cycapp/ui/history/HistoryVM$clearHistory$1;->label:I

    .line 143
    .line 144
    invoke-static {v1, p1, p0}, Lorg/orbitmvi/orbit/syntax/simple/SimpleSyntaxExtensionsKt;->d(Lorg/orbitmvi/orbit/syntax/simple/b;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    if-ne p1, v0, :cond_8

    .line 149
    .line 150
    :goto_3
    return-object v0

    .line 151
    :cond_8
    :goto_4
    sget-object p1, Lkotlin/t;->a:Lkotlin/t;

    .line 152
    .line 153
    return-object p1
.end method
