.class public Lcom/sigmob/sdk/base/views/q;
.super Landroid/app/Dialog;
.source "SourceFile"

# interfaces
.implements Landroid/content/DialogInterface$OnDismissListener;
.implements Landroid/content/DialogInterface$OnShowListener;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/sigmob/sdk/base/views/q$a;,
        Lcom/sigmob/sdk/base/views/q$b;
    }
.end annotation


# instance fields
.field private a:Lcom/sigmob/sdk/base/views/q$b;

.field private b:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private c:Landroid/content/Context;

.field private d:Landroid/view/Window;

.field private e:I

.field private f:I

.field private g:Lcom/sigmob/sdk/base/views/g;

.field private h:Lcom/sigmob/sdk/base/models/BaseAdUnit;

.field private i:Z

.field private j:Landroid/widget/ImageView;

.field private k:Ljava/lang/String;

.field private l:Ljava/io/File;

.field private m:Z

.field private n:Landroid/widget/RelativeLayout;

.field private o:Landroid/os/Handler;

.field private p:Landroid/app/Activity;

.field private q:I


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/sigmob/sdk/base/models/BaseAdUnit;)V
    .locals 1

    invoke-static {}, Lcom/sigmob/sdk/base/g;->f()I

    move-result v0

    invoke-direct {p0, p1, v0}, Landroid/app/Dialog;-><init>(Landroid/content/Context;I)V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->d:Landroid/view/Window;

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/sigmob/sdk/base/views/q;->i:Z

    const-string v0, ""

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->k:Ljava/lang/String;

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->c:Landroid/content/Context;

    iput-object p2, p0, Lcom/sigmob/sdk/base/views/q;->h:Lcom/sigmob/sdk/base/models/BaseAdUnit;

    invoke-virtual {p0}, Lcom/sigmob/sdk/base/views/q;->b()Lcom/sigmob/sdk/base/views/g;

    move-result-object p1

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    invoke-direct {p0}, Lcom/sigmob/sdk/base/views/q;->e()Landroid/widget/ImageView;

    move-result-object p1

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    invoke-static {}, Lcom/sigmob/sdk/b;->e()Landroid/content/Context;

    move-result-object p1

    invoke-static {p1}, Lcom/czhj/sdk/common/utils/DeviceUtils;->getRealMetrics(Landroid/content/Context;)Landroid/util/DisplayMetrics;

    move-result-object p1

    iget p1, p1, Landroid/util/DisplayMetrics;->widthPixels:I

    iput p1, p0, Lcom/sigmob/sdk/base/views/q;->e:I

    invoke-static {}, Lcom/sigmob/sdk/b;->e()Landroid/content/Context;

    move-result-object p1

    invoke-static {p1}, Lcom/czhj/sdk/common/utils/DeviceUtils;->getRealMetrics(Landroid/content/Context;)Landroid/util/DisplayMetrics;

    move-result-object p1

    iget p1, p1, Landroid/util/DisplayMetrics;->heightPixels:I

    iput p1, p0, Lcom/sigmob/sdk/base/views/q;->f:I

    return-void
.end method

.method private a(Landroid/net/Uri;)Landroid/webkit/WebResourceResponse;
    .locals 4

    .line 1
    const/4 v0, 0x0

    if-nez p1, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p1}, Landroid/net/Uri;->getHost()Ljava/lang/String;

    move-result-object v1

    if-nez v1, :cond_1

    return-object v0

    :cond_1
    :try_start_0
    const-string v2, "appassets"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-static {}, Lcom/sigmob/sdk/b;->e()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v1

    invoke-virtual {p1}, Landroid/net/Uri;->getPath()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_2

    const-string v2, "/"

    invoke-virtual {p1, v2}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    array-length v2, p1

    if-lez v2, :cond_2

    array-length v2, p1

    add-int/lit8 v2, v2, -0x1

    aget-object p1, p1, v2

    invoke-virtual {v1, p1}, Landroid/content/res/AssetManager;->open(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object p1

    new-instance v1, Landroid/webkit/WebResourceResponse;

    const-string v2, "text/html"

    const-string v3, "utf-8"

    invoke-direct {v1, v2, v3, p1}, Landroid/webkit/WebResourceResponse;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/io/InputStream;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object v1

    :catchall_0
    :cond_2
    return-object v0
.end method

.method public static synthetic a(Lcom/sigmob/sdk/base/views/q;Landroid/net/Uri;)Landroid/webkit/WebResourceResponse;
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/sigmob/sdk/base/views/q;->a(Landroid/net/Uri;)Landroid/webkit/WebResourceResponse;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic a(Lcom/sigmob/sdk/base/views/q;)Lcom/sigmob/sdk/base/models/BaseAdUnit;
    .locals 0

    .line 3
    iget-object p0, p0, Lcom/sigmob/sdk/base/views/q;->h:Lcom/sigmob/sdk/base/models/BaseAdUnit;

    return-object p0
.end method

.method private synthetic a(Landroid/view/View;)V
    .locals 0

    .line 4
    iget-object p1, p0, Lcom/sigmob/sdk/base/views/q;->a:Lcom/sigmob/sdk/base/views/q$b;

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lcom/sigmob/sdk/base/views/q$b;->a()V

    :cond_0
    return-void
.end method

.method public static synthetic a(Lcom/sigmob/sdk/base/views/q;Z)Z
    .locals 0

    .line 7
    iput-boolean p1, p0, Lcom/sigmob/sdk/base/views/q;->m:Z

    return p1
.end method

.method public static synthetic b(Lcom/sigmob/sdk/base/views/q;)Landroid/os/Handler;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/sigmob/sdk/base/views/q;->o:Landroid/os/Handler;

    return-object p0
.end method

.method public static synthetic c(Lcom/sigmob/sdk/base/views/q;)Lcom/sigmob/sdk/base/views/q$b;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/sigmob/sdk/base/views/q;->a:Lcom/sigmob/sdk/base/views/q$b;

    return-object p0
.end method

.method private d()V
    .locals 11

    .line 2
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->h:Lcom/sigmob/sdk/base/models/BaseAdUnit;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/sigmob/sdk/base/models/BaseAdUnit;->getMaterial()Lcom/sigmob/sdk/base/models/rtb/MaterialMeta;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, v0, Lcom/sigmob/sdk/base/models/rtb/MaterialMeta;->ad_privacy:Lcom/sigmob/sdk/base/models/rtb/AdPrivacy;

    if-eqz v0, :cond_1

    iget-object v1, v0, Lcom/sigmob/sdk/base/models/rtb/AdPrivacy;->privacy_template_info:Ljava/util/Map;

    if-eqz v1, :cond_0

    invoke-interface {v1}, Ljava/util/Map;->size()I

    move-result v1

    if-lez v1, :cond_0

    iget-object v1, v0, Lcom/sigmob/sdk/base/models/rtb/AdPrivacy;->privacy_template_info:Ljava/util/Map;

    iput-object v1, p0, Lcom/sigmob/sdk/base/views/q;->b:Ljava/util/Map;

    :cond_0
    iget-object v1, v0, Lcom/sigmob/sdk/base/models/rtb/AdPrivacy;->privacy_template_url:Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_2

    iget-object v0, v0, Lcom/sigmob/sdk/base/models/rtb/AdPrivacy;->privacy_template_url:Ljava/lang/String;

    invoke-static {v0}, Lcom/czhj/sdk/common/utils/Md5Util;->md5(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    sget-object v1, Lcom/sigmob/sdk/base/utils/h;->b:Ljava/lang/String;

    invoke-static {v1}, Lcom/sigmob/sdk/base/utils/h;->d(Ljava/lang/String;)Ljava/io/File;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ".html"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/sigmob/sdk/base/utils/h;->a(Ljava/io/File;Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->l:Ljava/io/File;

    goto :goto_0

    :cond_1
    const-string v0, "ad_privacy is null"

    invoke-static {v0}, Lcom/czhj/sdk/logger/SigmobLog;->i(Ljava/lang/String;)V

    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->l:Ljava/io/File;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    goto :goto_1

    :cond_3
    const/4 v0, 0x0

    :goto_1
    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->k:Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_4

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->k:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/sigmob/sdk/base/views/g;->loadUrl(Ljava/lang/String;)V

    return-void

    :cond_4
    const-string v1, "sigPrivacy"

    if-eqz v0, :cond_5

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    new-instance v2, Lcom/sigmob/sdk/base/views/q$a;

    iget-object v3, p0, Lcom/sigmob/sdk/base/views/q;->b:Ljava/util/Map;

    invoke-direct {v2, v3}, Lcom/sigmob/sdk/base/views/q$a;-><init>(Ljava/util/Map;)V

    invoke-virtual {v0, v2, v1}, Lcom/sigmob/sdk/base/views/g;->addJavascriptInterface(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "file://"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lcom/sigmob/sdk/base/views/q;->l:Ljava/io/File;

    invoke-virtual {v2}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/sigmob/sdk/base/views/g;->loadUrl(Ljava/lang/String;)V

    return-void

    :cond_5
    iget-object v2, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    new-instance v3, Lcom/sigmob/sdk/base/views/q$a;

    iget-object v4, p0, Lcom/sigmob/sdk/base/views/q;->b:Ljava/util/Map;

    invoke-direct {v3, v4}, Lcom/sigmob/sdk/base/views/q$a;-><init>(Ljava/util/Map;)V

    invoke-virtual {v2, v3, v1}, Lcom/sigmob/sdk/base/views/g;->addJavascriptInterface(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    const-string v2, "https://appassets/android_asset/sig_appelements.html"

    invoke-virtual {v1, v2}, Lcom/sigmob/sdk/base/views/g;->loadUrl(Ljava/lang/String;)V

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, " error: htmlUrl: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lcom/sigmob/sdk/base/views/q;->k:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " htmlFile "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lcom/sigmob/sdk/base/views/q;->l:Ljava/io/File;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v2, " exists "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    iget-object v9, p0, Lcom/sigmob/sdk/base/views/q;->h:Lcom/sigmob/sdk/base/models/BaseAdUnit;

    const/4 v10, 0x0

    const-string v3, "h5_error"

    const-string v4, "download_dialog"

    const/4 v5, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    invoke-static/range {v3 .. v10}, Lcom/sigmob/sdk/base/common/ac;->a(Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Lcom/sigmob/windad/WindAdRequest;Lcom/sigmob/sdk/base/models/LoadAdRequest;Lcom/sigmob/sdk/base/models/BaseAdUnit;Lcom/sigmob/sdk/base/common/ac$a;)V

    return-void
.end method

.method public static synthetic d(Lcom/sigmob/sdk/base/views/q;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/sigmob/sdk/base/views/q;->h()V

    return-void
.end method

.method private e()Landroid/widget/ImageView;
    .locals 2

    .line 2
    new-instance v0, Landroid/widget/ImageView;

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->c:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    sget-object v1, Lcom/sigmob/sdk/base/views/s;->e:Lcom/sigmob/sdk/base/views/s;

    invoke-virtual {v1}, Lcom/sigmob/sdk/base/views/s;->a()Landroid/graphics/Bitmap;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    sget-object v1, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    const/16 v1, 0x7f

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageAlpha(I)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/view/View;->setClickable(Z)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    new-instance v1, Lcom/sigmob/sdk/base/views/l0;

    invoke-direct {v1, p0}, Lcom/sigmob/sdk/base/views/l0;-><init>(Lcom/sigmob/sdk/base/views/q;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    return-object v0
.end method

.method public static synthetic e(Lcom/sigmob/sdk/base/views/q;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/sigmob/sdk/base/views/q;->a(Landroid/view/View;)V

    return-void
.end method

.method private f()V
    .locals 3

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    if-eqz v0, :cond_0

    const/high16 v0, 0x41900000    # 18.0f

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->c:Landroid/content/Context;

    invoke-static {v0, v1}, Lcom/czhj/sdk/common/utils/Dips;->dipsToIntPixels(FLandroid/content/Context;)I

    move-result v0

    new-instance v1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v1, v0, v0}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    const/16 v2, 0xa

    invoke-virtual {v1, v2}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    const/16 v2, 0xb

    invoke-virtual {v1, v2}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    div-int/lit8 v0, v0, 0x2

    const/4 v2, 0x0

    invoke-virtual {v1, v2, v0, v0, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->n:Landroid/widget/RelativeLayout;

    iget-object v2, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    invoke-virtual {v0, v2, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    :cond_0
    return-void
.end method

.method private g()V
    .locals 2

    invoke-virtual {p0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    move-result-object v0

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->d:Landroid/view/Window;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/view/Window;->getAttributes()Landroid/view/WindowManager$LayoutParams;

    move-result-object v0

    const/4 v1, -0x1

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->width:I

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->height:I

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->d:Landroid/view/Window;

    invoke-virtual {v1, v0}, Landroid/view/Window;->setAttributes(Landroid/view/WindowManager$LayoutParams;)V

    :cond_0
    return-void
.end method

.method private synthetic h()V
    .locals 1

    iget-boolean v0, p0, Lcom/sigmob/sdk/base/views/q;->m:Z

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/sigmob/sdk/base/views/q;->f()V

    :cond_0
    return-void
.end method


# virtual methods
.method public a(Lcom/sigmob/sdk/base/views/q$b;)V
    .locals 0

    .line 5
    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->a:Lcom/sigmob/sdk/base/views/q$b;

    return-void
.end method

.method public a()Z
    .locals 1

    .line 6
    const/4 v0, 0x1

    return v0
.end method

.method public b()Lcom/sigmob/sdk/base/views/g;
    .locals 2

    .line 2
    new-instance v0, Lcom/sigmob/sdk/base/views/g;

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->c:Landroid/content/Context;

    invoke-direct {v0, v1}, Lcom/sigmob/sdk/base/views/g;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/sigmob/sdk/base/views/g;->a(Z)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->h:Lcom/sigmob/sdk/base/models/BaseAdUnit;

    invoke-virtual {v0, v1}, Lcom/sigmob/sdk/base/views/g;->setAdUnit(Lcom/sigmob/sdk/base/models/BaseAdUnit;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/sigmob/sdk/base/views/g;->a(Lcom/sigmob/sdk/base/common/n$a;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundColor(I)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    new-instance v1, Lcom/sigmob/sdk/base/views/q$1;

    invoke-direct {v1, p0}, Lcom/sigmob/sdk/base/views/q$1;-><init>(Lcom/sigmob/sdk/base/views/q;)V

    invoke-virtual {v0, v1}, Landroid/webkit/WebView;->setWebChromeClient(Landroid/webkit/WebChromeClient;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    new-instance v1, Lcom/sigmob/sdk/base/views/q$2;

    invoke-direct {v1, p0}, Lcom/sigmob/sdk/base/views/q$2;-><init>(Lcom/sigmob/sdk/base/views/q;)V

    invoke-virtual {v0, v1}, Lcom/sigmob/sdk/base/views/g;->setWebViewClient(Landroid/webkit/WebViewClient;)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    return-object v0
.end method

.method public c()V
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/sigmob/sdk/base/views/g;->destroy()V

    iput-object v1, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    :cond_0
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->o:Landroid/os/Handler;

    if-eqz v0, :cond_1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    iput-object v1, p0, Lcom/sigmob/sdk/base/views/q;->o:Landroid/os/Handler;

    :cond_1
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    if-eqz v0, :cond_2

    invoke-static {v0}, Lcom/sigmob/sdk/base/utils/i;->a(Landroid/view/View;)V

    iput-object v1, p0, Lcom/sigmob/sdk/base/views/q;->j:Landroid/widget/ImageView;

    :cond_2
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->c:Landroid/content/Context;

    if-eqz v0, :cond_3

    iput-object v1, p0, Lcom/sigmob/sdk/base/views/q;->c:Landroid/content/Context;

    :cond_3
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->a:Lcom/sigmob/sdk/base/views/q$b;

    if-eqz v0, :cond_4

    iput-object v1, p0, Lcom/sigmob/sdk/base/views/q;->a:Lcom/sigmob/sdk/base/views/q$b;

    :cond_4
    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 3

    invoke-super {p0, p1}, Landroid/app/Dialog;->onCreate(Landroid/os/Bundle;)V

    new-instance p1, Landroid/widget/RelativeLayout;

    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-direct {p1, v0}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->n:Landroid/widget/RelativeLayout;

    new-instance v0, Landroid/widget/RelativeLayout$LayoutParams;

    const/4 v1, -0x1

    invoke-direct {v0, v1, v1}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    invoke-virtual {p0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    move-result-object p1

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->d:Landroid/view/Window;

    invoke-static {p1}, Lcom/sigmob/sdk/videoplayer/d;->a(Landroid/view/Window;)V

    iget-object p1, p0, Lcom/sigmob/sdk/base/views/q;->n:Landroid/widget/RelativeLayout;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroid/view/View;->setBackgroundColor(I)V

    iget-object p1, p0, Lcom/sigmob/sdk/base/views/q;->n:Landroid/widget/RelativeLayout;

    invoke-virtual {p0, p1}, Landroid/app/Dialog;->setContentView(Landroid/view/View;)V

    invoke-virtual {p0, p0}, Landroid/app/Dialog;->setOnShowListener(Landroid/content/DialogInterface$OnShowListener;)V

    invoke-virtual {p0, p0}, Landroid/app/Dialog;->setOnDismissListener(Landroid/content/DialogInterface$OnDismissListener;)V

    iget-object p1, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    if-eqz p1, :cond_0

    new-instance p1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {p1, v1, v1}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->n:Landroid/widget/RelativeLayout;

    iget-object v1, p0, Lcom/sigmob/sdk/base/views/q;->g:Lcom/sigmob/sdk/base/views/g;

    invoke-virtual {v0, v1, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    :cond_0
    new-instance p1, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v0

    invoke-direct {p1, v0}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->o:Landroid/os/Handler;

    new-instance v0, Lcom/sigmob/sdk/base/views/m0;

    invoke-direct {v0, p0}, Lcom/sigmob/sdk/base/views/m0;-><init>(Lcom/sigmob/sdk/base/views/q;)V

    const-wide/16 v1, 0x1388

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Landroid/app/Dialog;->setCanceledOnTouchOutside(Z)V

    invoke-virtual {p0, p1}, Landroid/app/Dialog;->setCancelable(Z)V

    invoke-direct {p0}, Lcom/sigmob/sdk/base/views/q;->g()V

    return-void
.end method

.method public onDismiss(Landroid/content/DialogInterface;)V
    .locals 1

    const-string p1, "DownloadDialog  onDismiss"

    invoke-static {p1}, Lcom/czhj/sdk/logger/SigmobLog;->i(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/sigmob/sdk/base/views/q;->p:Landroid/app/Activity;

    if-eqz p1, :cond_0

    iget v0, p0, Lcom/sigmob/sdk/base/views/q;->q:I

    invoke-virtual {p1, v0}, Landroid/app/Activity;->setRequestedOrientation(I)V

    const/4 p1, 0x0

    iput-object p1, p0, Lcom/sigmob/sdk/base/views/q;->p:Landroid/app/Activity;

    :cond_0
    iget-object p1, p0, Lcom/sigmob/sdk/base/views/q;->a:Lcom/sigmob/sdk/base/views/q$b;

    if-eqz p1, :cond_1

    invoke-interface {p1}, Lcom/sigmob/sdk/base/views/q$b;->a()V

    :cond_1
    return-void
.end method

.method public onShow(Landroid/content/DialogInterface;)V
    .locals 0

    const-string p1, "DownloadDialog  onShow"

    invoke-static {p1}, Lcom/czhj/sdk/logger/SigmobLog;->i(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/sigmob/sdk/base/views/q;->a:Lcom/sigmob/sdk/base/views/q$b;

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lcom/sigmob/sdk/base/views/q$b;->b()V

    :cond_0
    return-void
.end method

.method public show()V
    .locals 2

    invoke-super {p0}, Landroid/app/Dialog;->show()V

    invoke-static {}, Lcom/sigmob/sdk/b;->i()Landroid/app/Activity;

    move-result-object v0

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->p:Landroid/app/Activity;

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/sigmob/sdk/b;->c()Landroid/app/Activity;

    move-result-object v0

    iput-object v0, p0, Lcom/sigmob/sdk/base/views/q;->p:Landroid/app/Activity;

    :cond_0
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->p:Landroid/app/Activity;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroid/app/Activity;->getRequestedOrientation()I

    move-result v0

    iput v0, p0, Lcom/sigmob/sdk/base/views/q;->q:I

    invoke-static {}, Lcom/czhj/sdk/common/ClientMetadata;->getInstance()Lcom/czhj/sdk/common/ClientMetadata;

    move-result-object v0

    invoke-virtual {v0}, Lcom/czhj/sdk/common/ClientMetadata;->getOrientationInt()Ljava/lang/Integer;

    move-result-object v0

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_1

    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->p:Landroid/app/Activity;

    :goto_0
    invoke-virtual {v0, v1}, Landroid/app/Activity;->setRequestedOrientation(I)V

    goto :goto_1

    :cond_1
    iget-object v0, p0, Lcom/sigmob/sdk/base/views/q;->p:Landroid/app/Activity;

    const/4 v1, 0x0

    goto :goto_0

    :cond_2
    :goto_1
    invoke-direct {p0}, Lcom/sigmob/sdk/base/views/q;->d()V

    return-void
.end method
