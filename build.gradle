// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'ru.cleverpumpkin.proguard-dictionaries-generator' version '1.0.8' apply false
    id 'com.android.application' version '8.8.1' apply false
    id 'com.android.library' version '8.8.1' apply false
}

tasks.register('clean', Delete) {
    delete rootProject.layout.buildDirectory
}

project.ext {
    okhttpVersion = '5.0.0-alpha.14'
}