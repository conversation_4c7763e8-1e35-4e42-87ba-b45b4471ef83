.class final Lcom/kwad/components/ct/detail/b/c$1;
.super Lcom/kwad/components/core/j/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/b/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic avk:Lcom/kwad/components/ct/detail/b/c;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/b/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/j/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final pQ()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c;->a(Lcom/kwad/components/ct/detail/b/c;Z)Z

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 8
    .line 9
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c;->b(Lcom/kwad/components/ct/detail/b/c;Z)Z

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 13
    .line 14
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/b/c;->c(Lcom/kwad/components/ct/detail/b/c;Z)Z

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 18
    .line 19
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->b(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/widget/a/b;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 24
    .line 25
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c;->a(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/sdk/core/i/c;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/widget/a/a;->a(Lcom/kwad/sdk/core/i/c;)V

    .line 30
    .line 31
    .line 32
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 33
    .line 34
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->c(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/e/a;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    if-eqz v0, :cond_0

    .line 39
    .line 40
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 41
    .line 42
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->d(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/c;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 47
    .line 48
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/a;->aq(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Z

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    if-nez v0, :cond_0

    .line 53
    .line 54
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 55
    .line 56
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->c(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/e/a;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 61
    .line 62
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c;->e(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/video/n;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->c(Lcom/kwad/components/core/video/n;)V

    .line 67
    .line 68
    .line 69
    :cond_0
    return-void
.end method

.method public final pR()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->f(Lcom/kwad/components/ct/detail/b/c;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->g(Lcom/kwad/components/ct/detail/b/c;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 15
    .line 16
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->h(Lcom/kwad/components/ct/detail/b/c;)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 20
    .line 21
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->b(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/widget/a/b;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 26
    .line 27
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c;->a(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/sdk/core/i/c;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-virtual {v0, v1}, Lcom/kwad/components/core/widget/a/a;->b(Lcom/kwad/sdk/core/i/c;)V

    .line 32
    .line 33
    .line 34
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 35
    .line 36
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->c(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/e/a;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    if-eqz v0, :cond_1

    .line 41
    .line 42
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 43
    .line 44
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->i(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/c;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iget-object v0, v0, Lcom/kwad/components/ct/detail/c;->mAdTemplate:Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 49
    .line 50
    invoke-static {v0}, Lcom/kwad/components/ct/response/a/a;->aq(Lcom/kwad/components/ct/response/model/CtAdTemplate;)Z

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    if-nez v0, :cond_1

    .line 55
    .line 56
    iget-object v0, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 57
    .line 58
    invoke-static {v0}, Lcom/kwad/components/ct/detail/b/c;->c(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/ct/detail/e/a;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    iget-object v1, p0, Lcom/kwad/components/ct/detail/b/c$1;->avk:Lcom/kwad/components/ct/detail/b/c;

    .line 63
    .line 64
    invoke-static {v1}, Lcom/kwad/components/ct/detail/b/c;->e(Lcom/kwad/components/ct/detail/b/c;)Lcom/kwad/components/core/video/n;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    invoke-virtual {v0, v1}, Lcom/kwad/components/ct/detail/e/a;->d(Lcom/kwad/components/core/video/n;)V

    .line 69
    .line 70
    .line 71
    :cond_1
    return-void
.end method
