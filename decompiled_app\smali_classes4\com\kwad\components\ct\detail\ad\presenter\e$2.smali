.class final Lcom/kwad/components/ct/detail/ad/presenter/e$2;
.super Lcom/kwad/components/core/video/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/kwad/components/ct/detail/ad/presenter/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic amC:Lcom/kwad/components/ct/detail/ad/presenter/e;


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/e;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    invoke-direct {p0}, Lcom/kwad/components/core/video/o;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final onMediaPlayCompleted()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final onMediaPlayError(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    const/4 p2, 0x0

    .line 4
    invoke-static {p1, p2}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final onMediaPlayProgress(JJ)V
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->c(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-boolean v0, v0, Lcom/kwad/components/ct/response/model/CtAdTemplate;->isDrawAdHasLook:Z

    .line 8
    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/kwad/components/ct/detail/ad/presenter/e;->c(Lcom/kwad/components/ct/detail/ad/presenter/e;)Lcom/kwad/components/ct/response/model/CtAdTemplate;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iget-wide v0, v0, Lcom/kwad/sdk/core/response/model/AdTemplate;->posId:J

    .line 18
    .line 19
    invoke-static {v0, v1}, Lcom/kwad/components/ct/home/<USER>/b;->aa(J)I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    iget-object v1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 24
    .line 25
    mul-int/lit16 v0, v0, 0x3e8

    .line 26
    .line 27
    int-to-long v2, v0

    .line 28
    move-wide v6, p1

    .line 29
    move-wide v4, p3

    .line 30
    invoke-static/range {v1 .. v7}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;JJJ)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    if-gtz p1, :cond_0

    .line 35
    .line 36
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 37
    .line 38
    const/4 p2, 0x0

    .line 39
    invoke-static {p1, p2}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;Z)V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_0
    iget-object p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 44
    .line 45
    invoke-static {p2, p1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->b(Lcom/kwad/components/ct/detail/ad/presenter/e;I)V

    .line 46
    .line 47
    .line 48
    :cond_1
    return-void
.end method

.method public final onVideoPlayBufferingPaused()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final onVideoPlayBufferingPlaying()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/e$2;->amC:Lcom/kwad/components/ct/detail/ad/presenter/e;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v0, v1}, Lcom/kwad/components/ct/detail/ad/presenter/e;->a(Lcom/kwad/components/ct/detail/ad/presenter/e;Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method
