.class final Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;
.super Landroid/animation/AnimatorListenerAdapter;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;->k(ZZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

.field final synthetic anz:Z


# direct methods
.method public constructor <init>(Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;Z)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 2
    .line 3
    iput-boolean p2, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->anz:Z

    .line 4
    .line 5
    invoke-direct {p0}, Landroid/animation/AnimatorListenerAdapter;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final onAnimationCancel(Landroid/animation/Animator;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, Landroid/animation/AnimatorListenerAdapter;->onAnimationCancel(Landroid/animation/Animator;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 5
    .line 6
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->anz:Z

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    const/4 v0, 0x4

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final onAnimationEnd(Landroid/animation/Animator;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, Landroid/animation/AnimatorListenerAdapter;->onAnimationEnd(Landroid/animation/Animator;)V

    .line 2
    .line 3
    .line 4
    iget-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->anz:Z

    .line 5
    .line 6
    if-nez p1, :cond_0

    .line 7
    .line 8
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 9
    .line 10
    const/4 v0, 0x4

    .line 11
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    :cond_0
    return-void
.end method

.method public final onAnimationPause(Landroid/animation/Animator;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, Landroid/animation/AnimatorListenerAdapter;->onAnimationPause(Landroid/animation/Animator;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 5
    .line 6
    iget-boolean v0, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->anz:Z

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    const/4 v0, 0x4

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final onAnimationStart(Landroid/animation/Animator;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, Landroid/animation/AnimatorListenerAdapter;->onAnimationStart(Landroid/animation/Animator;)V

    .line 2
    .line 3
    .line 4
    iget-boolean p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->anz:Z

    .line 5
    .line 6
    if-eqz p1, :cond_0

    .line 7
    .line 8
    iget-object p1, p0, Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel$8;->any:Lcom/kwad/components/ct/detail/ad/presenter/comment/CommentBottomAdPanel;

    .line 9
    .line 10
    const/4 v0, 0x0

    .line 11
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    :cond_0
    return-void
.end method
